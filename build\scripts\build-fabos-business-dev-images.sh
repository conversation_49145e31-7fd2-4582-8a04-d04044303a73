#!/usr/bin/bash
TIME=3.1.1
TIME_LS=ls-demo-3.1.1
echo "${TIME}"
IMAGE_NAME=hub.fabos.com:8090/ls-demo

FILE_PATH=/var/lib/jenkins/workspace/02.Build-Deploy-Backend-Business_DEV/

# build ecs
docker build -t "${IMAGE_NAME}"/fabos-business-ecs-server:"${TIME}" -f "${FILE_PATH}"/fabos-business-ecs-server/Dockerfile "${FILE_PATH}"/fabos-business-ecs-server
docker push "${IMAGE_NAME}"/fabos-business-ecs-server:"${TIME}"

# build its
docker build -t "${IMAGE_NAME}"/fabos-business-its-server:"${TIME}" -f "${FILE_PATH}"/fabos-business-its-server/Dockerfile "${FILE_PATH}"/fabos-business-its-server
docker push "${IMAGE_NAME}"/fabos-business-its-server:"${TIME}"

# build mes
docker build -t "${IMAGE_NAME}"/fabos-business-mes-server:"${TIME}" -f "${FILE_PATH}"/fabos-business-mes-server/Dockerfile "${FILE_PATH}"/fabos-business-mes-server
docker push "${IMAGE_NAME}"/fabos-business-mes-server:"${TIME}"

# build pms
docker build -t "${IMAGE_NAME}"/fabos-business-pms-server:"${TIME}" -f "${FILE_PATH}"/fabos-business-pms-server/Dockerfile "${FILE_PATH}"/fabos-business-pms-server
docker push "${IMAGE_NAME}"/fabos-business-pms-server:"${TIME}"

# build qms
docker build -t "${IMAGE_NAME}"/fabos-business-qms-server:"${TIME}" -f "${FILE_PATH}"/fabos-business-qms-server/Dockerfile "${FILE_PATH}"/fabos-business-qms-server
docker push "${IMAGE_NAME}"/fabos-business-qms-server:"${TIME}"

# build wms
docker build -t "${IMAGE_NAME}"/fabos-business-wms-server:"${TIME}" -f "${FILE_PATH}"/fabos-business-wms-server/Dockerfile "${FILE_PATH}"/fabos-business-wms-server
docker push "${IMAGE_NAME}"/fabos-business-wms-server:"${TIME}"

# build modeler
docker build -t "${IMAGE_NAME}"/fabos-modeler-factory-server:"${TIME_LS}" -f "${FILE_PATH}"/fabos-modeler-factory-server/Dockerfile "${FILE_PATH}"/fabos-modeler-factory-server
docker push "${IMAGE_NAME}"/fabos-modeler-factory-server:"${TIME_LS}"