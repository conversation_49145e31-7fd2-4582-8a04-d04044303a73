<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>fabos-business</artifactId>
        <groupId>cec.jiutian</groupId>
        <version>3.1.1-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>fabos-business-ecs-server</artifactId>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <mysql-connector-java.version>8.0.29</mysql-connector-java.version>
        <postgresql.version>42.4.0</postgresql.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>cec.jiutian</groupId>
            <artifactId>fabos-ext</artifactId>
            <version>${framework-version}</version>
        </dependency>
        <dependency>
            <groupId>cec.jiutian</groupId>
            <artifactId>core-comn</artifactId>
            <version>${framework-version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>cec.jiutian</groupId>
            <artifactId>xxljob-spring-boot-starter</artifactId>
            <version>1.0-RELEASE</version>
        </dependency>

        <dependency>
            <groupId>cec.jiutian</groupId>
            <artifactId>fabos-business-ecs-interface</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>org.postgresql</groupId>
            <artifactId>postgresql</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-mail</artifactId>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>
</project>