package cec.jiutian;

import ch.qos.logback.classic.LoggerContext;
import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;
import io.micrometer.core.instrument.MeterRegistry;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.actuate.autoconfigure.metrics.MeterRegistryCustomizer;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.web.servlet.ServletComponentScan;
import org.springframework.cloud.netflix.eureka.EnableEurekaClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Bean;
import java.util.TimeZone;

/**
 * ServletComponentScan注解用于扫描license包中的filter， 此注解在coms不依赖license包时可有可无，但生产环境下必须存在
 *
 * <AUTHOR>
 * @date 2022/2/28
 */
@SpringBootApplication
@EnableEurekaClient
@EnableFeignClients
@EnableApolloConfig
@ServletComponentScan
@Slf4j
public class EcsApp {
    public static void main(String[]args) {
        TimeZone.setDefault(TimeZone.getTimeZone("Asia/Shanghai"));
        SpringApplication.run(EcsApp.class, args);
        log.info("wow");
    }

    @Bean
    MeterRegistryCustomizer<MeterRegistry> configurer(@Value("${spring.application.name}") String applicationName) {
        return registry -> registry.config().commonTags("application", applicationName);
    }

    @Bean
    public boolean removeLogAppender(@Value("${logback.detach.appender:FILE}") String appenderName) {
        // 第一步：获取日志上下文
        LoggerContext lc = (LoggerContext) LoggerFactory.getILoggerFactory();
        // 第二步：获取日志对象 （日志是有继承关系的，关闭上层，下层如果没有特殊说明也会关闭）
        ch.qos.logback.classic.Logger rootLogger = lc.getLogger("root");
        // 第三步：移除 appender
        return rootLogger.detachAppender(appenderName);
    }
}
