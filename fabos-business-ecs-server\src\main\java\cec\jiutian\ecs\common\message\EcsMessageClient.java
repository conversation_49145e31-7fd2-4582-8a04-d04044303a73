package cec.jiutian.ecs.common.message;

import org.springframework.cloud.stream.annotation.Input;
import org.springframework.cloud.stream.annotation.Output;
import org.springframework.messaging.MessageChannel;
import org.springframework.messaging.SubscribableChannel;

public interface EcsMessageClient {
    String INPUT = "inputEcsMessageInfo";
    String OUTPUT = "outputEcsMessageInfo";

    @Input("inputEcsMessageInfo")
    SubscribableChannel input();

    @Output("outputEcsMessageInfo")
    MessageChannel output();
}
