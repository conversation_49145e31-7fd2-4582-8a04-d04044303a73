package cec.jiutian.ecs.controller;

import cec.jiutian.core.base.BaseController;
import cec.jiutian.core.comn.util.BeanValidators;
import cec.jiutian.ecs.dto.GeneralIpCreateDTO;
import cec.jiutian.ecs.dto.GeneralIpDeleteDTO;
import cec.jiutian.ecs.dto.GeneralIpUpdateDTO;
import cec.jiutian.ecs.query.dto.GeneralIpQueryDTO;
import cec.jiutian.ecs.service.business.GeneralIpBizService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.ws.rs.core.Response;

/**
 * <AUTHOR>
 * @date 2023/5/29
 */

@Api(tags = "白名单管理")
@RestController
@RequestMapping("/generalIp")
public class GeneralIpController extends BaseController {

    private final GeneralIpBizService generalIpBizService;

    public GeneralIpController(GeneralIpBizService generalIpBizService) {
        this.generalIpBizService = generalIpBizService;
    }

    @PostMapping("/getGeneralIpList")
    @ApiOperation(value = "查询白名单列表", notes = "查询白名单列表，相关表：ecs_general_ip")
    public Response getMessageGroupList(@RequestBody GeneralIpQueryDTO generalIpQueryDTO) {
        Object list = generalIpBizService.getGeneralIpList(generalIpQueryDTO);
        return respSuccessResult(list, "success");
    }

    @PostMapping("/create")
    @ApiOperation(value = "创建白名单", notes = "创建白名单，相关表：ecs_general_ip")
    public Response messageGroupCreate(@RequestBody GeneralIpCreateDTO generalIpCreateDTO) {
        BeanValidators.validateWithException(validator, generalIpCreateDTO);
        generalIpBizService.generalIpCreate(generalIpCreateDTO);
        return respSuccessResult("创建成功");
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新白名单", notes = "更新白名单，相关表：ecs_general_ip")
    public Response messageGroupUpdate(@RequestBody GeneralIpUpdateDTO generalIpUpdateDTO) {
        BeanValidators.validateWithException(validator, generalIpUpdateDTO);
        generalIpBizService.generalIpUpdate(generalIpUpdateDTO);
        return respSuccessResult("修改成功");
    }

    @PostMapping("/delete")
    @ApiOperation(value = "删除白名单", notes = "删除白名单， 相关表：ecs_general_ip")
    public Response messageGroupDelete(@RequestBody GeneralIpDeleteDTO generalIpDeleteDTO) {
        BeanValidators.validateWithException(validator, generalIpDeleteDTO);
        generalIpBizService.generalIpDelete(generalIpDeleteDTO);
        return respSuccessResult("删除成功");
    }

}
