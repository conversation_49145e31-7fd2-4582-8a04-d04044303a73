package cec.jiutian.ecs.controller;

import cec.jiutian.core.base.BaseController;
import cec.jiutian.core.comn.util.BeanValidators;
import cec.jiutian.ecs.dto.ConfirmMessageDTO;
import cec.jiutian.ecs.dto.ConfirmMessageTempDTO;
import cec.jiutian.ecs.dto.MessageCreateDTO;
import cec.jiutian.ecs.po.MessagePO;
import cec.jiutian.ecs.query.dto.MessageQueryDTO;
import cec.jiutian.ecs.service.business.GeneralIpBizService;
import cec.jiutian.ecs.service.business.MessageBizService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.ws.rs.core.Response;

/**
 * <AUTHOR>
 * @date 2023/5/22
 */

@Api(tags = "消息管理")
@RestController
@RequestMapping("/message")
public class MessageController extends BaseController {

    private final MessageBizService messageBizService;
    private final GeneralIpBizService generalIpBizService;

    public MessageController(MessageBizService messageBizService, GeneralIpBizService generalIpBizService) {
        this.messageBizService = messageBizService;
        this.generalIpBizService = generalIpBizService;
    }

    @PostMapping("/getMessageList")
    @ApiOperation(value = "查询消息列表", notes = "查询消息列表，相关表：ecs_message")
    public Response getMessageList(@RequestBody MessageQueryDTO messageQueryDTO) {
        Object list = messageBizService.getMessageList(messageQueryDTO);
        return respSuccessResult(list, "success");
    }

    @PostMapping("/receiveMsg")
    @ApiOperation(value = "接收上游消息", notes = "接收上游消息，相关表：ecs_message")
    public Response messageGroupCreate(@RequestBody MessageCreateDTO messageCreateDTO) {
        generalIpBizService.validateSystem(messageCreateDTO);
        BeanValidators.validateWithException(validator, messageCreateDTO);
        messageBizService.messageCreate(messageCreateDTO);
        return respSuccessResult("消息接收成功");
    }

    /**
     * 方法保留，后续消息反馈改为点击消息连接跳转进入反馈页面时使用
     */
    @ApiModelProperty(value = "根据加密secret查询消息详情", notes = "根据加密secret查询消息详情，相关表：ecs_message")
    @PostMapping("/getMessageBySecret/{secret}")
    public Response getMessageBySecret(@PathVariable String secret) {
        MessagePO messagePO = messageBizService.getMessageBySecret(secret);
        return respSuccessResult(messagePO, "success");
    }

    /**
     * 方法保留，后续消息反馈改为点击消息连接跳转进入反馈页面时使用
     */
    @ApiModelProperty(value = "确认接收消息/确认处理消息", notes = "确认接收消息/确认处理消息，相关表：ecs_message")
    @PostMapping("/confirmMessage")
    public Response confirmMessage(@RequestBody ConfirmMessageDTO confirmMessageDTO){
        BeanValidators.validateWithException(validator, confirmMessageDTO);
        messageBizService.confirmMessage(confirmMessageDTO);
        return respSuccessResult("操作成功");
    }

    /**
     * 查询待反馈消息列表，暂时使用这种方式，后续更换
     */
    @ApiModelProperty(value = "查询待反馈消息列表", notes = "查询待反馈消息列表，相关表：ecs_message")
    @PostMapping("/getWaitReceiveMessageList")
    public Response getWaitReceiveMessageList(@RequestBody MessageQueryDTO messageQueryDTO){
        Object list = messageBizService.getWaitReceiveMessageList(messageQueryDTO);
        return respSuccessResult(list, "success");
    }

    /**
     * 查询待处理消息列表，暂时使用这种方式，后续更换
     */
    @ApiModelProperty(value = "查询待反馈消息列表", notes = "查询待反馈消息列表，相关表：ecs_message")
    @PostMapping("/getWaitHandleMessageList")
    public Response getWaitHandleMessageList(@RequestBody MessageQueryDTO messageQueryDTO){
        Object list = messageBizService.getWaitHandleMessageList(messageQueryDTO);
        return respSuccessResult(list, "success");
    }

    /**
     * 方法保留，后续消息反馈改为点击消息连接跳转进入反馈页面时使用
     */
    @ApiModelProperty(value = "确认接收消息/确认处理消息", notes = "确认接收消息/确认处理消息，相关表：ecs_message")
    @PostMapping("/confirmMessageTemp")
    public Response confirmMessageTemp(@RequestBody ConfirmMessageTempDTO confirmMessageTempDTO){
        BeanValidators.validateWithException(validator, confirmMessageTempDTO);
        messageBizService.confirmMessageTemp(confirmMessageTempDTO);
        return respSuccessResult("操作成功");
    }
}
