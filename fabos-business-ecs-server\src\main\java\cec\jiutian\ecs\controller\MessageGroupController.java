package cec.jiutian.ecs.controller;

import cec.jiutian.core.base.BaseController;
import cec.jiutian.core.comn.util.BeanValidators;
import cec.jiutian.ecs.dto.MessageGroupCreateDTO;
import cec.jiutian.ecs.dto.MessageGroupDeleteDTO;
import cec.jiutian.ecs.dto.MessageGroupUpdateDTO;
import cec.jiutian.ecs.query.dto.MessageGroupQueryDTO;
import cec.jiutian.ecs.service.business.GeneralIpBizService;
import cec.jiutian.ecs.service.business.MessageGroupBizService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.ws.rs.core.Response;

/**
 * <AUTHOR>
 * @date 2023/5/16
 */

@Api(tags = "消息组管理")
@RestController
@RequestMapping("/messageGroup")
public class MessageGroupController extends BaseController {

    private final MessageGroupBizService messageGroupBizService;
    private final GeneralIpBizService generalIpBizService;

    public MessageGroupController(MessageGroupBizService messageGroupBizService, GeneralIpBizService generalIpBizService) {
        this.messageGroupBizService = messageGroupBizService;
        this.generalIpBizService = generalIpBizService;
    }

    @PostMapping("/getMessageGroupList")
    @ApiOperation(value = "查询消息组列表", notes = "查询消息组列表，相关表：ecs_message_group")
    public Response getMessageGroupList(@RequestBody MessageGroupQueryDTO messageGroupQueryDTO) {
        Object list = messageGroupBizService.getMessageGroupList(messageGroupQueryDTO);
        return respSuccessResult(list, "success");
    }

    @PostMapping("/getEcsMessageGroup")
    @ApiOperation(value = "外部系统查询消息组列表", notes = "外部系统查询消息组列表，相关表：ecs_message_group")
    public Response getMessageGroupList() {
        Object list = messageGroupBizService.getEcsMessageGroup();
        return respSuccessResult(list, "success");
    }

    @PostMapping("/create")
    @ApiOperation(value = "创建消息组", notes = "创建消息组，相关表：ecs_message_group")
    public Response messageGroupCreate(@RequestBody MessageGroupCreateDTO messageGroupCreateDTO) {
        BeanValidators.validateWithException(validator, messageGroupCreateDTO);
        messageGroupBizService.messageGroupCreate(messageGroupCreateDTO);
        return respSuccessResult("创建成功");
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新消息组", notes = "更新消息组，相关表：ecs_message_group")
    public Response messageGroupUpdate(@RequestBody MessageGroupUpdateDTO messageGroupUpdateDTO) {
        BeanValidators.validateWithException(validator, messageGroupUpdateDTO);
        messageGroupBizService.messageGroupUpdate(messageGroupUpdateDTO);
        return respSuccessResult("修改成功");
    }

    @PostMapping("/delete")
    @ApiOperation(value = "删除消息组", notes = "删除消息组， 相关表：ecs_message_group")
    public Response messageGroupDelete(@RequestBody MessageGroupDeleteDTO messageGroupDeleteDTO) {
        BeanValidators.validateWithException(validator, messageGroupDeleteDTO);
        messageGroupBizService.messageGroupDelete(messageGroupDeleteDTO);
        return respSuccessResult("删除成功");
    }

}
