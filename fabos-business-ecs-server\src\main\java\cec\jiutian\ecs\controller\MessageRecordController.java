package cec.jiutian.ecs.controller;

import cec.jiutian.core.base.BaseController;
import cec.jiutian.ecs.query.dto.MessageRecordQueryDTO;
import cec.jiutian.ecs.service.business.MessageRecordBizService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.ws.rs.core.Response;

/**
 * <AUTHOR>
 * @date 2023/5/29
 */

@Api(tags = "消息记录管理")
@RestController
@RequestMapping("/messageRecord")
public class MessageRecordController extends BaseController {

    private final MessageRecordBizService messageRecordBizService;

    public MessageRecordController(MessageRecordBizService messageRecordBizService) {
        this.messageRecordBizService = messageRecordBizService;
    }

    @PostMapping("/getMessageRecordList")
    @ApiOperation(value = "查询消息记录列表", notes = "查询消息记录列表，相关表：ecs_message_record")
    public Response getMessageRecordList(@RequestBody MessageRecordQueryDTO messageRecordQueryDTO) {
        Object list = messageRecordBizService.getMessageRecordList(messageRecordQueryDTO);
        return respSuccessResult(list, "success");
    }

}
