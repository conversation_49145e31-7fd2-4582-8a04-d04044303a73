package cec.jiutian.ecs.controller;

import cec.jiutian.core.base.BaseController;
import cec.jiutian.ecs.dto.EnumDTO;
import cec.jiutian.ecs.dto.NoticeCreateDTO;
import cec.jiutian.ecs.dto.NoticeUpdateDTO;
import cec.jiutian.ecs.dto.UserDTO;
import cec.jiutian.ecs.query.dto.NoticeQueryDTO;
import cec.jiutian.ecs.service.business.NoticeBizService;
import io.swagger.annotations.Api;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.ws.rs.core.Response;


/**
 * <AUTHOR>
 * @date ：2023/5/29 13:37
 * @description：
 */
@Api(tags = "通知公告管理")
@RestController
public class NoticeController extends BaseController {

    private final NoticeBizService noticeBizService;

    public NoticeController(NoticeBizService noticeBizService) {
        this.noticeBizService = noticeBizService;
    }

    @PostMapping("/notice/list")
    public Response getNoticeList(@RequestBody NoticeQueryDTO queryDTO) {
        Object result = noticeBizService.getNoticeList(queryDTO);
        return respSuccessResult(result, "success");
    }

    @PostMapping("/notice/add")
    public Response addNotice(@RequestBody NoticeCreateDTO createDTO) {
        noticeBizService.addNotice(createDTO);
        return respSuccessResult("success");
    }

    @PostMapping("/notice/update")
    public Response updateNotice(@RequestBody NoticeUpdateDTO updateDTO) {
        noticeBizService.updateNotice(updateDTO);
        return respSuccessResult("success");
    }

    @PostMapping("/notice/delete")
    public Response deleteNotice(@RequestBody NoticeUpdateDTO updateDTO) {
        noticeBizService.deleteNotice(updateDTO.getGid());
        return respSuccessResult("success");
    }

    @PostMapping("/notice/getUserInfo")
    public Response getUserInfo(@RequestBody UserDTO userDTO) {
        Object result = noticeBizService.getAllUserInfo(userDTO.getAccountName());
        return respSuccessResult(result, "success");
    }

    @PostMapping("/notice/getEnumInfo")
    public Response getEnumInfo(@RequestBody EnumDTO enumDTO) {
        Object result = noticeBizService.getEnumInfo(enumDTO.getEnumCode());
        return respSuccessResult(result, "success");
    }
}
