package cec.jiutian.ecs.controller;

import cec.jiutian.core.base.BaseController;
import cec.jiutian.ecs.query.dto.StatisticsQueryDTO;
import cec.jiutian.ecs.service.business.StatisticalReportBizService;
import io.swagger.annotations.Api;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.ws.rs.core.Response;


/**
 * <AUTHOR>
 * @date ：2023/5/29 14:41
 * @description：
 */
@Api(tags = "统计报表")
@RestController
public class StatisticalReportController extends BaseController {

    private final StatisticalReportBizService reportBizService;

    public StatisticalReportController(StatisticalReportBizService reportBizService) {
        this.reportBizService = reportBizService;
    }

    @PostMapping("/report/personReceiveRate")
    public Response countPersonReceiveRate(@RequestBody StatisticsQueryDTO queryDTO){
        Object result = reportBizService.countPersonReceiveRate(queryDTO);
        return respSuccessResult(result,"success");
    }

    @PostMapping("/report/messageCount")
    public Response countMessageBySystem(@RequestBody StatisticsQueryDTO queryDTO){
        Object result = reportBizService.countMessageBySystem(queryDTO);
        return respSuccessResult(result,"success");
    }

    @PostMapping("/report/groupReceiveRate")
    public Response countGroupReceiveRate(@RequestBody StatisticsQueryDTO queryDTO){
        Object result = reportBizService.countGroupReceiveRate(queryDTO);
        return respSuccessResult(result,"success");
    }

    @PostMapping("/report/messageSendStatistical")
    public Response messageSendStatistical(@RequestBody StatisticsQueryDTO queryDTO){
        Object result = reportBizService.messageSendStatistical(queryDTO);
        return respSuccessResult(result,"success");
    }
}
