package cec.jiutian.ecs.controller;

import cec.jiutian.core.base.BaseController;
import cec.jiutian.ecs.dto.TemplateAttributeCreateDTO;
import cec.jiutian.ecs.dto.TemplateAttributeUpdateDTO;
import cec.jiutian.ecs.query.dto.TemplateAttributeQueryDTO;
import cec.jiutian.ecs.service.business.TemplateAttributeBizService;
import io.swagger.annotations.Api;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.ws.rs.core.Response;

/**
 * <AUTHOR>
 * @date ：2023/5/19 10:53
 * @description：消息模板属性 controller
 */
@Api(tags = "消息模板属性管理")
@RestController
public class TemplateAttributeController extends BaseController {

    private final TemplateAttributeBizService templateAttributeBizService;

    public TemplateAttributeController(TemplateAttributeBizService templateAttributeBizService) {
        this.templateAttributeBizService = templateAttributeBizService;
    }

    @PostMapping("/templateAttribute/list")
    public Response queryTemplateAttribute(@RequestBody TemplateAttributeQueryDTO queryDTO) {
        Object result = templateAttributeBizService.queryTemplateAttribute(queryDTO);
        return respSuccessResult(result, "success");
    }

    @PostMapping("/templateAttribute/create")
    public Response addTemplateAttribute(@RequestBody TemplateAttributeCreateDTO templateAttributeCreateDTO) {
        if (!templateAttributeBizService.checkUnique(templateAttributeCreateDTO.getAttributeName(), templateAttributeCreateDTO.getTemplateGid(), null)) {
            return respFaultResult("该模板中已存在该字段名称，请重新输入");
        }
        templateAttributeBizService.addTemplateAttribute(templateAttributeCreateDTO);
        return respSuccessResult("创建成功");
    }

    @PostMapping("/templateAttribute/update")
    public Response updateTemplateAttribute(@RequestBody TemplateAttributeUpdateDTO templateAttributeUpdateDTO) {
        if (!templateAttributeBizService.checkUnique(templateAttributeUpdateDTO.getAttributeName(), templateAttributeUpdateDTO.getTemplateGid(), templateAttributeUpdateDTO.getGid())) {
            return respFaultResult("该模板中已存在该字段名称，请重新输入");
        }
        templateAttributeBizService.updateTemplateAttribute(templateAttributeUpdateDTO);
        return respSuccessResult("更新成功");
    }

    @PostMapping("/templateAttribute/delete")
    public Response deleteTemplateAttribute(@RequestBody TemplateAttributeUpdateDTO templateAttributeUpdateDTO) {
        templateAttributeBizService.delete(templateAttributeUpdateDTO.getGid());
        return respSuccessResult("删除成功");
    }
}
