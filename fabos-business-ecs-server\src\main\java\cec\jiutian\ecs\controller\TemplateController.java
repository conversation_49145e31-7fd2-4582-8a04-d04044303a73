package cec.jiutian.ecs.controller;

import cec.jiutian.core.base.BaseController;
import cec.jiutian.ecs.dto.TemplateCreateDTO;
import cec.jiutian.ecs.dto.TemplateUpdateDTO;
import cec.jiutian.ecs.query.dto.TemplateQueryDTO;
import cec.jiutian.ecs.service.business.TemplateBizService;
import io.swagger.annotations.Api;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.ws.rs.core.Response;

/**
 * <AUTHOR>
 * @date ：2023/5/19 10:52
 * @description：消息模板 controller
 */
@Api(tags = "消息模板管理")
@RestController
public class TemplateController extends BaseController {

    private final TemplateBizService templateBizService;

    public TemplateController(TemplateBizService templateBizService) {
        this.templateBizService = templateBizService;
    }

    @PostMapping("/template/list")
    public Response queryTemplate(@RequestBody TemplateQueryDTO queryDTO) {
        Object result = templateBizService.queryTemplate(queryDTO);
        return respSuccessResult(result, "success");
    }

    @PostMapping("/template/create")
    public Response addTemplate(@RequestBody TemplateCreateDTO templateCreateDTO) {
        if (!templateBizService.checkUnique(templateCreateDTO.getTemplateName(), null)) {
            throw new RuntimeException("该模板名称已存在，请重新输入");
        }
        templateBizService.addTemplate(templateCreateDTO);
        return respSuccessResult("创建成功");
    }

    @PostMapping("/template/update")
    public Response updateTemplate(@RequestBody TemplateUpdateDTO templateUpdateDTO) {
        if (!templateBizService.checkUnique(templateUpdateDTO.getTemplateName(), templateUpdateDTO.getGid())) {
            throw new RuntimeException("该模板名称已存在，请重新输入");
        }
        templateBizService.updateTemplate(templateUpdateDTO);
        return respSuccessResult("更新成功");
    }

    @PostMapping("/template/delete")
    public Response deleteTemplate(@RequestBody TemplateUpdateDTO templateUpdateDTO) {
        templateBizService.delete(templateUpdateDTO.getGid());
        return respSuccessResult("删除成功");
    }

    @PostMapping("/template/getTemplatePreview")
    public Response getTemplatePreview(@RequestBody TemplateQueryDTO queryDTO) {
        Object result = templateBizService.getTemplatePreview(queryDTO);
        return respSuccessResult(result, "success");
    }
}
