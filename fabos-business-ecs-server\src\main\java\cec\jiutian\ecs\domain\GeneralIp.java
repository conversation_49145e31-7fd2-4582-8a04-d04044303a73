package cec.jiutian.ecs.domain;

import cec.jiutian.core.comn.util.BeanUtils;
import cec.jiutian.core.data.factory.CrudFactory;
import cec.jiutian.core.ext.base.TrxnDomain;
import cec.jiutian.ecs.po.GeneralIpPO;
import cec.jiutian.ecs.util.AesUtil;

import java.util.List;
import java.util.UUID;

/**
 * <AUTHOR>
 * @date 2023/5/29
 */
public class GeneralIp extends TrxnDomain<GeneralIpPO> {
    public GeneralIp(GeneralIpPO entity) {
        super(entity);
    }

    public <DTO> void init(DTO dto) {
        BeanUtils.copyProperties(dto, this.getEntity());
    }

    public void setAuthKey(String sysName, String aesKey) {
        this.getEntity().setAuthKey(AesUtil.encrypt(sysName + "-" + UUID.randomUUID().toString().replaceAll("-", ""), aesKey));
    }

    public void checkSysNameOnly() {
        GeneralIpPO generalIpPO = new GeneralIpPO();
        generalIpPO.setSysName(this.getEntity().getSysName());
        List<GeneralIpPO> select = CrudFactory.getCrud().select(generalIpPO);
        if (this.getEntity().getGid() == null){
            if (select.size() > 0){
                throw new RuntimeException("该系统已配置，请重试");
            }
        }else {
            if (select.size() > 0 && !select.get(0).getGid().equals(this.getEntity().getGid())){
                throw new RuntimeException("该系统已配置，请重试");
            }
        }
    }
}
