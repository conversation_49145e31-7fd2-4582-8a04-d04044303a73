package cec.jiutian.ecs.domain;

import cec.jiutian.core.comn.util.BeanUtils;
import cec.jiutian.core.comn.util.StringUtils;
import cec.jiutian.core.ext.base.TrxnDomain;
import cec.jiutian.ecs.po.MessagePO;
import cec.jiutian.ecs.service.business.definition.BusinessDefinition;

import java.util.Date;

/**
 * <AUTHOR>
 * @date ：2023/5/18 11:30
 * @description：消息domain
 */
public class Message extends TrxnDomain<MessagePO> {

    public Message(MessagePO entity) {
        super(entity);
    }

    public <DTO> void init(DTO dto) {
        BeanUtils.copyProperties(dto, this.getEntity());
        // 设置当前已升级
        this.getEntity().setIsUpgradation("1");
        this.getEntity().setMessageStatus(BusinessDefinition.MessageStatus.DISPATCHED);
        if (StringUtils.isEmpty(this.getEntity().getIsProcess())){
            // 默认不处理
            this.getEntity().setIsProcess("0");
        }
        this.getEntity().setLastUpDate(new Date());
        this.getEntity().setCurrentUpNumber(1L);
    }
}
