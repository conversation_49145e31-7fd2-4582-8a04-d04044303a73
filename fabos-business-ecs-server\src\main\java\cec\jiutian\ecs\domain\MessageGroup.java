package cec.jiutian.ecs.domain;

import cec.jiutian.core.comn.util.BeanUtils;
import cec.jiutian.core.comn.util.StringUtils;
import cec.jiutian.core.data.factory.CrudFactory;
import cec.jiutian.core.ext.base.TrxnDomain;
import cec.jiutian.ecs.dto.MessageGroupCreateDTO;
import cec.jiutian.ecs.dto.MessageGroupUpdateDTO;
import cec.jiutian.ecs.po.MessageGroupPO;
import com.alibaba.fastjson.JSON;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class MessageGroup extends TrxnDomain<MessageGroupPO> {

    public MessageGroup(MessageGroupPO entity) {
        super(entity);
    }

    public <DTO> void init(DTO dto) {
        BeanUtils.copyProperties(dto, this.getEntity());
    }

    public void checkGroupNameOnly() {
        MessageGroupPO messageGroupPO = new MessageGroupPO();
        messageGroupPO.setGroupName(this.getEntity().getGroupName());
        List<MessageGroupPO> select = CrudFactory.getCrud().select(messageGroupPO);
        if (this.getEntity().getGid() == null){
            if (select.size() > 0){
                throw new RuntimeException("消息组名称重复，请重试");
            }
        }else {
            if (select.size() > 0 && !select.get(0).getGid().equals(this.getEntity().getGid())){
                throw new RuntimeException("消息组名称重复，请重试");
            }
        }
    }

    public void setExtAttribute(MessageGroupCreateDTO createDTO){
        Map<String,Object> dispatchWay = new HashMap<>(3);
        Map<String,Object> dispatchUser = new HashMap<>(3);
        dispatchWay.put("first", StringUtils.join(createDTO.getFirstWay(), ","));
        dispatchUser.put("first", StringUtils.join(createDTO.getFirstUser(), ","));
        if (createDTO.getUpNumber() > 1){
            dispatchWay.put("second", StringUtils.join(createDTO.getSecondWay(), ","));
            dispatchUser.put("second", StringUtils.join(createDTO.getSecondUser(), ","));
        }
        if (createDTO.getUpNumber() > 2){
            dispatchWay.put("third", StringUtils.join(createDTO.getThirdWay(), ","));
            dispatchUser.put("third", StringUtils.join(createDTO.getThirdUser(), ","));
        }
        this.getEntity().setDispatchWay(JSON.toJSONString(dispatchWay));
        this.getEntity().setDispatchUser(JSON.toJSONString(dispatchUser));
    }
    public void setExtAttribute(MessageGroupUpdateDTO updateDTO){
        Map<String,Object> dispatchWay = new HashMap<>(3);
        Map<String,Object> dispatchUser = new HashMap<>(3);
        dispatchWay.put("first", StringUtils.join(updateDTO.getFirstWay(), ","));
        dispatchUser.put("first", StringUtils.join(updateDTO.getFirstUser(), ","));
        if (updateDTO.getUpNumber() > 1){
            dispatchWay.put("second", StringUtils.join(updateDTO.getSecondWay(), ","));
            dispatchUser.put("second", StringUtils.join(updateDTO.getSecondUser(), ","));
        }
        if (updateDTO.getUpNumber() > 2){
            dispatchWay.put("third", StringUtils.join(updateDTO.getThirdWay(), ","));
            dispatchUser.put("third", StringUtils.join(updateDTO.getThirdUser(), ","));
        }
        this.getEntity().setDispatchWay(JSON.toJSONString(dispatchWay));
        this.getEntity().setDispatchUser(JSON.toJSONString(dispatchUser));
    }
}
