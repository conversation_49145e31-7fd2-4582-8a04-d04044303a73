package cec.jiutian.ecs.domain;

import cec.jiutian.core.comn.util.BeanUtils;
import cec.jiutian.core.ext.base.ExtensibleField;
import cec.jiutian.core.ext.base.TrxnDomain;
import cec.jiutian.ecs.po.MessageRecordPO;

/**
 * <AUTHOR>
 * @date ：2023/5/24 14:15
 * @description：
 */
public class MessageRecord extends TrxnDomain<MessageRecordPO> {
    public MessageRecord(MessageRecordPO entity) {
        super(entity);
    }

    public <DTO> void init(DTO dto) {
        BeanUtils.copyProperties(dto, this.getEntity());
    }

}
