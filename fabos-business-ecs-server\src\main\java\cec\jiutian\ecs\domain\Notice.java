package cec.jiutian.ecs.domain;

import cec.jiutian.core.comn.util.BeanUtils;
import cec.jiutian.core.ext.base.TrxnDomain;
import cec.jiutian.ecs.po.NoticePO;

/**
 * <AUTHOR>
 * @date ：2023/5/29 11:40
 * @description：
 */
public class Notice extends TrxnDomain<NoticePO> {
    public Notice(NoticePO entity) {
        super(entity);
    }

    public <DTO> void init(DTO dto) {
        BeanUtils.copyProperties(dto, this.getEntity());
    }
}
