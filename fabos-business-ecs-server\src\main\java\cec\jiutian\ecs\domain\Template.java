package cec.jiutian.ecs.domain;

import cec.jiutian.core.comn.util.BeanUtils;
import cec.jiutian.core.ext.base.TrxnDomain;
import cec.jiutian.ecs.po.TemplatePO;

/**
 * <AUTHOR>
 * @date ：2023/5/19 11:11
 * @description：消息模板 domain
 */
public class Template extends TrxnDomain<TemplatePO> {
    public Template(TemplatePO entity) {
        super(entity);
    }

    public <templateCreateDTO> void init(templateCreateDTO dto) {
        BeanUtils.copyProperties(dto, this.getEntity());
    }
}
