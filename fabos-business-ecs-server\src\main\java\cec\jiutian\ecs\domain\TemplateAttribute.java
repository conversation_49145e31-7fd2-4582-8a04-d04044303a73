package cec.jiutian.ecs.domain;

import cec.jiutian.core.comn.util.BeanUtils;
import cec.jiutian.core.data.factory.CrudFactory;
import cec.jiutian.core.ext.base.TrxnDomain;
import cec.jiutian.ecs.po.TemplateAttributePO;

import java.util.List;

/**
 * <AUTHOR>
 * @date ：2023/5/19 11:12
 * @description：消息模板属性 domain
 */
public class TemplateAttribute extends TrxnDomain<TemplateAttributePO> {
    public TemplateAttribute(TemplateAttributePO entity) {
        super(entity);
    }

    public <DTO> void init(DTO dto) {
        BeanUtils.copyProperties(dto, this.getEntity());
    }

    public List<TemplateAttributePO> getTemplateAttributeList(TemplateAttributePO entity) {
        return CrudFactory.getCrud().select(entity);
    }
}
