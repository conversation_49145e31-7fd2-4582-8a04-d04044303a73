package cec.jiutian.ecs.facade.remote;

import cec.jiutian.core.base.BaseController;
import cec.jiutian.core.comn.util.BeanValidators;
import cec.jiutian.ecs.dto.MessageCreateDTO;
import cec.jiutian.ecs.service.business.GeneralIpBizService;
import cec.jiutian.ecs.service.business.MessageBizService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 接收外部系统消息feign接口
 * <AUTHOR>
 * @date 2023/6/12
 */
@RestController
@RequestMapping("/remote")
public class MessageRequestApi extends BaseController {

    private final MessageBizService messageBizService;
    private final GeneralIpBizService generalIpBizService;

    public MessageRequestApi(MessageBizService messageBizService, GeneralIpBizService generalIpBizService) {
        this.messageBizService = messageBizService;
        this.generalIpBizService = generalIpBizService;
    }

    @PostMapping("/message/receiveMsg")
    public Boolean receiveMsg(@RequestBody MessageCreateDTO messageCreateDTO) {
        generalIpBizService.validateSystem(messageCreateDTO);
        BeanValidators.validateWithException(validator, messageCreateDTO);
        messageBizService.messageCreate(messageCreateDTO);
        return true;
    }

    @PostMapping("/message/sendMsgByEmail")
    public Boolean sendMsgByEmail(@RequestBody MessageCreateDTO messageCreateDTO) {
        BeanValidators.validateWithException(validator, messageCreateDTO);
        messageBizService.sendMessageByEmail(messageCreateDTO);
        return true;
    }
}
