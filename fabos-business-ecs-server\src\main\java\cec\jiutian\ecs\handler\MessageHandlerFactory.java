package cec.jiutian.ecs.handler;

import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date ：2023/5/23 15:56
 * @description：
 */
@Component
public class MessageHandlerFactory {
    public MessageHandler getMessageHandler(String sendChannel) {
        if ("Email".equalsIgnoreCase(sendChannel)) {
            return new SendMessageByEmail();
        } else if ("短信".equalsIgnoreCase(sendChannel)) {
            return new SendMessageBySMS();
        } else if ("APP".equalsIgnoreCase(sendChannel)) {
            return new SendMessageByAPP();
        }
        return null;
    }
}
