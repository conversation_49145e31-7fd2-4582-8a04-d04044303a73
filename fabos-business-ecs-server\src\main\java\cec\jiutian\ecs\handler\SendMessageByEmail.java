package cec.jiutian.ecs.handler;

import cec.jiutian.core.comn.util.StringUtils;
import cec.jiutian.ecs.dto.MessageRecordCreateDTO;
import cec.jiutian.ecs.dto.UserDTO;
import cec.jiutian.ecs.util.AesUtil;
import cec.jiutian.ecs.util.EmailTemplate;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date ：2023/5/22 17:45
 * @description：
 */
@Component
public class SendMessageByEmail implements MessageHandler {
    private static String aesKey;

    private static EmailTemplate emailTemplate;

    @Value("${aes.key}")
    public void setAesKey(String aesKey) {
        SendMessageByEmail.aesKey = aesKey;
    }

    @Resource
    public void setEmailTemplate(EmailTemplate emailTemplate) {
        SendMessageByEmail.emailTemplate = emailTemplate;
    }

    @Override
    public void sendMessage(MessageRecordCreateDTO createDTO) {
        // 根据接收消息用户查询用户列表
        List<UserDTO> users = createDTO.getUserDTOList();

        for (UserDTO user : users) {
            String html;
            if (StringUtils.isNotEmpty(createDTO.getResponseLink())) {
                html = createDTO.getContent() + "<p><a href=\"" + createDTO.getResponseLink() + AesUtil.encrypt(createDTO.getGid() + "-" + user.getLoginId(), aesKey) + "\">点击进入确认</a></p>";
            } else {
                html = createDTO.getContent();
            }

            if (user.getEmail() == null) {
                throw new RuntimeException("推送人员" + user.getAccountName() + "的邮箱账号不存在");
            }
            emailTemplate.sendHtmlMail(user.getEmail(), "", html);
        }
    }
}
