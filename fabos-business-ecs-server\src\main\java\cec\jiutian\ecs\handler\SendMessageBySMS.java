package cec.jiutian.ecs.handler;

import cec.jiutian.core.comn.util.StringUtils;
import cec.jiutian.ecs.dto.MessageRecordCreateDTO;
import cec.jiutian.ecs.dto.UserDTO;
import cec.jiutian.ecs.util.SmsSendUtil;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date ：2023/5/22 17:47
 * @description：
 */
@Component
public class SendMessageBySMS implements MessageHandler {

    private static SmsSendUtil smsSendUtil;

    @Resource
    public void setSmsSendUtil(SmsSendUtil smsSendUtil){
        SendMessageBySMS.smsSendUtil = smsSendUtil;
    }


    @Override
    public void sendMessage(MessageRecordCreateDTO createDTO) {
// 根据接收消息用户查询用户列表
        List<UserDTO> users = createDTO.getUserDTOList();

        for (UserDTO user : users) {
            if (StringUtils.isNotEmpty(user.getPhoneNumber())){
                int result = smsSendUtil.send(user.getPhoneNumber(),createDTO.getContent());
                System.out.print(smsSendUtil.getMessage(result));
            }
        }
    }
}
