package cec.jiutian.ecs.mapper;

import cec.jiutian.ecs.po.GeneralIpPO;
import cec.jiutian.ecs.query.dto.GeneralIpQueryDTO;
import cec.jiutian.ecs.vo.GeneralIpVO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/5/29
 */
@Mapper
public interface GeneralIpMapper {

    List<GeneralIpPO> getGeneralIpList(GeneralIpQueryDTO queryDTO);

    List<GeneralIpVO> getGeneralIpVOList(GeneralIpQueryDTO queryDTO);
}
