package cec.jiutian.ecs.mapper;

import cec.jiutian.ecs.dto.EnumDTO;
import cec.jiutian.ecs.dto.UserDTO;
import cec.jiutian.ecs.po.MessagePO;
import cec.jiutian.ecs.query.dto.MessageQueryDTO;
import cec.jiutian.ecs.vo.ResultVO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @date ：2023/5/18 11:36
 * @description：消息管理 mapper层
 */
@Mapper
public interface MessageMapper {

    List<MessagePO> getMessageList(MessageQueryDTO messageQueryDTO);

    UserDTO getUserInfo(String loginId);

    List<UserDTO> getAllUserInfo(String userName);

    List<EnumDTO> getEnumInfo(String enumCode);

    List<ResultVO> messageSendCount(MessageQueryDTO messageQueryDTO);
}
