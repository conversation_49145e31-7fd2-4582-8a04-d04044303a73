package cec.jiutian.ecs.mapper;

import cec.jiutian.ecs.dto.TemplateAttributeCreateDTO;
import cec.jiutian.ecs.po.TemplateAttributePO;
import cec.jiutian.ecs.query.dto.TemplateAttributeQueryDTO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @date ：2023/5/19 11:14
 * @description：消息模板属性 mapper
 */
@Mapper
public interface TemplateAttributeMapper {

    List<TemplateAttributePO> getTemplateAttributeList(TemplateAttributeQueryDTO queryDTO);

    TemplateAttributePO selectTemplateAttributeByName(String attributeName, String templateGid);

    void deleteByTemplateGid(String templateGid);
}
