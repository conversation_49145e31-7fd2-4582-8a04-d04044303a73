package cec.jiutian.ecs.mapper;

import cec.jiutian.ecs.po.TemplatePO;
import cec.jiutian.ecs.query.dto.TemplateQueryDTO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @date ：2023/5/19 11:13
 * @description：消息模板 mapper
 */
@Mapper
public interface TemplateMapper {

    List<TemplatePO> getTemplateList(TemplateQueryDTO templateDTO);

    TemplatePO selectTemplateByName(String templateName);
}
