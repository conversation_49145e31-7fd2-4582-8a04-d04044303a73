package cec.jiutian.ecs.service.business;

import cec.jiutian.core.comn.util.StringUtils;
import cec.jiutian.core.data.factory.CrudFactory;
import cec.jiutian.core.exception.MesErrorCodeException;
import cec.jiutian.core.ext.utils.PageUtils;
import cec.jiutian.ecs.domain.GeneralIp;
import cec.jiutian.ecs.dto.GeneralIpCreateDTO;
import cec.jiutian.ecs.dto.GeneralIpDeleteDTO;
import cec.jiutian.ecs.dto.GeneralIpUpdateDTO;
import cec.jiutian.ecs.dto.MessageCreateDTO;
import cec.jiutian.ecs.mapper.GeneralIpMapper;
import cec.jiutian.ecs.po.GeneralIpPO;
import cec.jiutian.ecs.query.dto.GeneralIpQueryDTO;
import cec.jiutian.ecs.util.AesUtil;
import com.github.pagehelper.PageInfo;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/5/17
 */
@Service
@Transactional
public class GeneralIpBizService {

    @Value("${aes.key}")
    private String aesKey;

    private final GeneralIpMapper generalIpMapper;

    public GeneralIpBizService(GeneralIpMapper generalIpMapper) {
        this.generalIpMapper = generalIpMapper;
    }

    public Object getGeneralIpList(GeneralIpQueryDTO generalIpQueryDTO) {
        PageUtils.startPage(generalIpQueryDTO.getPageNum(), generalIpQueryDTO.getPageSize());
        List<GeneralIpPO> list = generalIpMapper.getGeneralIpList(generalIpQueryDTO);
        PageInfo<GeneralIpPO> pageInfo = new PageInfo<>(list);
        return generalIpQueryDTO.getPageSize() == 0 ? pageInfo.getList() : pageInfo;
    }

    public void generalIpCreate(GeneralIpCreateDTO generalIpCreateDTO) {
        GeneralIp generalIp = new GeneralIp(new GeneralIpPO());
        generalIp.init(generalIpCreateDTO);
        generalIp.setAuthKey(generalIpCreateDTO.getSysName(), aesKey);
        generalIp.checkSysNameOnly();
        generalIp.save();
    }

    public void generalIpUpdate(GeneralIpUpdateDTO generalIpUpdateDTO) {
        GeneralIp generalIp = new GeneralIp(new GeneralIpPO());
        generalIp.getById(generalIpUpdateDTO.getGid());
        generalIp.init(generalIpUpdateDTO);
        generalIp.setAuthKey(generalIpUpdateDTO.getSysName(), aesKey);
        generalIp.checkSysNameOnly();
        generalIp.update();
    }

    public void generalIpDelete(GeneralIpDeleteDTO generalIpDeleteDTO) {
        GeneralIp generalIp = new GeneralIp(new GeneralIpPO());
        generalIp.getById(generalIpDeleteDTO.getGid());
        generalIp.delete();
    }

    public void validateSystem(MessageCreateDTO messageCreateDTO){
        String system = messageCreateDTO.getSysName();
        String authKey = messageCreateDTO.getAuthKey();
        if (StringUtils.isEmpty(system) || StringUtils.isEmpty(authKey)){
            throw new MesErrorCodeException("system或authKey为空，请重试");
        }
        String decrypt = AesUtil.decrypt(authKey, aesKey);
        if (StringUtils.isEmpty(decrypt)){
            throw new MesErrorCodeException("authKey解析失败，请重试");
        }
        GeneralIpPO condition = new GeneralIpPO();
        condition.setStatus("启用");
        condition.setSysName(system);
        List<GeneralIpPO> generalIpList = CrudFactory.getCrud().select(condition);
        if (generalIpList == null || generalIpList.size() == 0){
            throw new MesErrorCodeException("该系统未配置或已禁用，请重试");
        }
        GeneralIpPO generalIpPO = generalIpList.get(0);
        if (!authKey.equals(generalIpPO.getAuthKey()) || authKey.split("-")[0].equals(system)){
            throw new MesErrorCodeException("authKey无效，请重试");
        }
    }

}
