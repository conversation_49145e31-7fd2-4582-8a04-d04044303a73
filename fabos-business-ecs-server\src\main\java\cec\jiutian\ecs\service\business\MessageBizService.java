package cec.jiutian.ecs.service.business;

import cec.jiutian.core.comn.util.StringUtils;
import cec.jiutian.core.data.factory.CrudFactory;
import cec.jiutian.core.exception.MesErrorCodeException;
import cec.jiutian.core.ext.utils.PageUtils;
import cec.jiutian.ecs.common.message.EcsMessageClient;
import cec.jiutian.ecs.domain.Message;
import cec.jiutian.ecs.domain.MessageGroup;
import cec.jiutian.ecs.domain.MessageRecord;
import cec.jiutian.ecs.dto.ConfirmMessageDTO;
import cec.jiutian.ecs.domain.*;
import cec.jiutian.ecs.dto.ConfirmMessageTempDTO;
import cec.jiutian.ecs.dto.MessageCreateDTO;
import cec.jiutian.ecs.dto.MessageRecordCreateDTO;
import cec.jiutian.ecs.dto.UserDTO;
import cec.jiutian.ecs.handler.MessageHandler;
import cec.jiutian.ecs.handler.MessageHandlerFactory;
import cec.jiutian.ecs.mapper.MessageMapper;
import cec.jiutian.ecs.mapper.TemplateAttributeMapper;
import cec.jiutian.ecs.po.MessageGroupPO;
import cec.jiutian.ecs.po.MessagePO;
import cec.jiutian.ecs.po.MessageRecordPO;
import cec.jiutian.ecs.po.*;
import cec.jiutian.ecs.query.dto.MessageQueryDTO;
import cec.jiutian.ecs.query.dto.TemplateAttributeQueryDTO;
import cec.jiutian.ecs.service.business.definition.BusinessDefinition;
import cec.jiutian.ecs.util.AesUtil;
import cec.jiutian.ecs.util.EmailTemplate;
import cec.jiutian.ecs.vo.MessageVO;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.stream.annotation.EnableBinding;
import org.springframework.cloud.stream.annotation.StreamListener;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/5/22
 */
@Service
@Slf4j
@Transactional
@EnableBinding(EcsMessageClient.class)
public class MessageBizService {

    @Value("${aes.key}")
    private String aesKey;

    @Value("${confirm.url}")
    private String confirmUrl;

    private final MessageMapper messageMapper;

    private final TemplateAttributeMapper templateAttributeMapper;

    private final MessageHandlerFactory messageHandlerFactory;

    private final EmailTemplate emailTemplate;

    public MessageBizService(MessageMapper messageMapper, TemplateAttributeMapper templateAttributeMapper, MessageHandlerFactory messageHandlerFactory, EmailTemplate emailTemplate) {
        this.messageMapper = messageMapper;
        this.templateAttributeMapper = templateAttributeMapper;
        this.messageHandlerFactory = messageHandlerFactory;
        this.emailTemplate = emailTemplate;
    }

    public Object getMessageList(MessageQueryDTO messageQueryDTO) {
        PageUtils.startPage(messageQueryDTO.getPageNum(), messageQueryDTO.getPageSize());
        List<MessagePO> list = messageMapper.getMessageList(messageQueryDTO);
        List<MessageVO> messageVOList = new ArrayList<>(list.size());
        for (MessagePO messagePO : list) {
            MessageVO messageVO = new MessageVO();
            BeanUtils.copyProperties(messagePO, messageVO);
            JSONObject dispatchWay = JSONObject.parseObject(messageVO.getDispatchWay());
            JSONObject dispatchUser = JSONObject.parseObject(messageVO.getDispatchUser());
            if (dispatchWay.get("first") != null){
                messageVO.setFirstWay(dispatchWay.get("first").toString().split(","));
            }
            if (dispatchWay.get("second") != null){
                messageVO.setSecondWay(dispatchWay.get("second").toString().split(","));
            }
            if (dispatchWay.get("third") != null){
                messageVO.setThirdWay(dispatchWay.get("second").toString().split(","));
            }
            if (dispatchUser.get("first") != null){
                messageVO.setFirstUser(dispatchUser.get("first").toString().split(","));
            }
            if (dispatchUser.get("second") != null){
                messageVO.setSecondUser(dispatchUser.get("second").toString().split(","));
            }
            if (dispatchUser.get("third") != null){
                messageVO.setThirdUser(dispatchUser.get("third").toString().split(","));
            }
            messageVOList.add(messageVO);
        }
        PageInfo<MessageVO> pageInfo = new PageInfo<>(messageVOList);
        return messageQueryDTO.getPageSize() == 0 ? pageInfo.getList() : pageInfo;
    }

    public void messageCreate(MessageCreateDTO messageCreateDTO) {
        Message message = new Message(new MessagePO());
        if (StringUtils.isEmpty(messageCreateDTO.getMessageGroupGid())) {
            if (StringUtils.isEmpty(messageCreateDTO.getDispatchWay()) || StringUtils.isEmpty(messageCreateDTO.getDispatchUser()) || messageCreateDTO.getUpNumber() == null|| messageCreateDTO.getUpIntervalMinute() == null) {
                throw new MesErrorCodeException("消息组id和推送规则不能同时为空");
            }
        } else {
            MessageGroup messageGroup = new MessageGroup(new MessageGroupPO());
            messageGroup.getById(messageCreateDTO.getMessageGroupGid());
            if (messageGroup.getEntity() == null) {
                throw new MesErrorCodeException("消息组不存在，请重试");
            }
            messageCreateDTO.setDispatchWay(messageGroup.getEntity().getDispatchWay());
            messageCreateDTO.setDispatchUser(messageGroup.getEntity().getDispatchUser());
            messageCreateDTO.setUpNumber(messageGroup.getEntity().getUpNumber());
            messageCreateDTO.setUpIntervalMinute(messageGroup.getEntity().getUpIntervalMinute());
        }
        message.init(messageCreateDTO);
        message.save();
        sendMessage(message.getEntity());
    }

    public void sendMessageByEmail(MessageCreateDTO messageCreateDTO) {
        if (StringUtils.isNotEmpty(messageCreateDTO.getEmail())) {
            //emailTemplate.sendHtmlMail(messageCreateDTO.getEmail(), messageCreateDTO.getTitle(), messageCreateDTO.getContent());
            emailTemplate.sendMail587(messageCreateDTO.getEmail(),messageCreateDTO.getTitle(),messageCreateDTO.getContent());
        }
    }

    public List<MessagePO> getMessageListByStatus(String messageStatus) {
        MessageQueryDTO queryDTO = new MessageQueryDTO();
        queryDTO.setMessageStatus(messageStatus);
        return messageMapper.getMessageList(queryDTO);
    }

    public MessagePO getMessageBySecret(String secret) {
        // 对secret解密
        String decrypt = AesUtil.decrypt(secret, aesKey);
        if (StringUtils.isBlank(decrypt)) {
            throw new RuntimeException("secret无效，请重试");
        }
        String messageRecordId = decrypt.split("-")[0];
        MessageRecord messageRecord = new MessageRecord(new MessageRecordPO());
        messageRecord.getById(messageRecordId);
        Message message = new Message(new MessagePO());
        message.getById(messageRecord.getEntity().getMessageGid());
        return message.getEntity();
    }

    public void confirmMessage(ConfirmMessageDTO confirmMessageDTO) {
        String decrypt = AesUtil.decrypt(confirmMessageDTO.getSecret(), aesKey);
        if (StringUtils.isBlank(decrypt)) {
            throw new RuntimeException("secret无效，请重试");
        }
        String messageRecordId = decrypt.split("-")[0];
        MessageRecord messageRecord = new MessageRecord(new MessageRecordPO());
        messageRecord.getById(messageRecordId);
        Message message = new Message(new MessagePO());
        message.getById(messageRecord.getEntity().getMessageGid());
        message.getEntity().setLastEventComment(confirmMessageDTO.getLastEventComment());
        if (BusinessDefinition.MessageOpType.RECEIVE.equals(confirmMessageDTO.getOpType())) {
            // 点击接收消息
            if (!BusinessDefinition.MessageStatus.DISPATCHED.equals(message.getEntity().getMessageStatus())) {
                throw new RuntimeException("操作失败，消息已被接收或关闭");
            }else {
                message.getEntity().setMessageStatus(BusinessDefinition.MessageStatus.RECEIVED);
                message.getEntity().setCloseReason(BusinessDefinition.MessageStatus.RECEIVED);
                message.getEntity().setReceivedTime(new Date());
                message.getEntity().setReceivedBy(decrypt.split("-")[1]);
                message.getEntity().setReceivedWay(messageRecord.getEntity().getDispatchWay());
                message.update();
            }
        }else if (BusinessDefinition.MessageOpType.HANDLE.equals(confirmMessageDTO.getOpType())) {
            // 点击处理消息
            if (!BusinessDefinition.MessageStatus.RECEIVED.equals(message.getEntity().getMessageStatus())) {
                throw new RuntimeException("操作失败，消息状态不是已接收");
            }else {
                message.getEntity().setMessageStatus(BusinessDefinition.MessageStatus.PROCESSED);
                message.update();
            }
        }
    }

    public void sendMessage(MessagePO messagePO) {
        MessageRecord messageRecord = new MessageRecord(new MessageRecordPO());
        MessageRecordCreateDTO createDTO = new MessageRecordCreateDTO();
        StringBuilder stringBuilder = new StringBuilder();
        if (StringUtils.isNotEmpty(messagePO.getMessageTemplateGid())) {
            Template template = new Template(new TemplatePO());
            template.getById(messagePO.getMessageTemplateGid());

            TemplateAttributeQueryDTO queryDTO = new TemplateAttributeQueryDTO();
            queryDTO.setTemplateGid(template.getId());
            List<TemplateAttributePO> attributePOS = templateAttributeMapper.getTemplateAttributeList(queryDTO);
            attributePOS = attributePOS.stream().sorted(Comparator.comparing(TemplateAttributePO::getOrderNumber)).collect(Collectors.toList());

            // 根据模板设置消息内容
            try {
                Map contentMap = JSON.parseObject(messagePO.getContent());
                attributePOS.forEach(a -> {
                    if (contentMap.get(a.getConnectAttribute()) != null) {
                        stringBuilder.append(contentMap.get(a.getConnectAttribute()));
                    } else {
                        stringBuilder.append(a.getDefaultValue());
                    }
                });
                createDTO.setContent(stringBuilder.toString());
            } catch (Exception e) {
                e.printStackTrace();
            }
        } else {
            createDTO.setContent(messagePO.getContent());
        }

        JSONObject wayMap = JSON.parseObject(messagePO.getDispatchWay());
        JSONObject userMap = JSON.parseObject(messagePO.getDispatchUser());
        switch (messagePO.getCurrentUpNumber().intValue()) {
            case 1:
                createDTO.setDispatchWay(wayMap.getString("first"));
                createDTO.setDispatchUser(userMap.getString("first"));
                createDTO.setUserDTOList(getUserInfo(createDTO));
                break;
            case 2:
                createDTO.setDispatchWay(wayMap.getString("second"));
                createDTO.setDispatchUser(userMap.getString("second"));
                createDTO.setUserDTOList(getUserInfo(createDTO));
                break;
            case 3:
                createDTO.setDispatchWay(wayMap.getString("third"));
                createDTO.setDispatchUser(userMap.getString("third"));
                createDTO.setUserDTOList(getUserInfo(createDTO));
                break;
            default:
                break;
        }

        createDTO.setMessageGid(messagePO.getGid());
        createDTO.setResponseLink(confirmUrl);
        if (StringUtils.isNotEmpty(createDTO.getDispatchWay())) {
            List<String> wayList = Arrays.asList(createDTO.getDispatchWay().split(","));
            wayList.forEach(w -> {
                createDTO.setDispatchWay(w);
                messageRecord.init(createDTO);
                messageRecord.save();
                createDTO.setGid(messageRecord.getEntity().getGid());
                MessageHandler messageHandler = messageHandlerFactory.getMessageHandler(w);
                messageHandler.sendMessage(createDTO);
                createDTO.setGid(null);
            });
        }
    }

    private List<UserDTO> getUserInfo(MessageRecordCreateDTO createDTO) {
        List<UserDTO> list = new ArrayList<>();
        if (StringUtils.isNotEmpty(createDTO.getDispatchUser())) {
            for (String loginId : createDTO.getDispatchUser().split(",")) {
                UserDTO userInfo = messageMapper.getUserInfo(loginId);
                list.add(userInfo);
            }
        }
        return list;
    }

    public Object getWaitReceiveMessageList(MessageQueryDTO messageQueryDTO) {
        PageUtils.startPage(messageQueryDTO.getPageNum(), messageQueryDTO.getPageSize());
        messageQueryDTO.setMessageStatus(BusinessDefinition.MessageStatus.DISPATCHED);
        List<MessagePO> list = messageMapper.getMessageList(messageQueryDTO);
        List<MessagePO> collect = list.stream().filter(x -> {
            // 根据每一条消息当前派送次数对应的派送人员，判断是否包含当前登录用户
            List<String> dispatchUser;
            JSONObject dispatchUserJson = JSONObject.parseObject(x.getDispatchUser());
            if (x.getCurrentUpNumber() == 1) {
                dispatchUser = Arrays.asList(dispatchUserJson.get("first").toString().split(","));
            } else if (x.getCurrentUpNumber() == 2) {
                dispatchUser = Arrays.asList(dispatchUserJson.get("second").toString().split(","));
            } else {
                dispatchUser = Arrays.asList(dispatchUserJson.get("third").toString().split(","));
            }
            return dispatchUser.contains(messageQueryDTO.getLoginUser());
        }).collect(Collectors.toList());
        PageInfo<MessagePO> pageInfo = new PageInfo<>(collect);
        return messageQueryDTO.getPageSize() == 0 ? pageInfo.getList() : pageInfo;
    }

    public Object getWaitHandleMessageList(MessageQueryDTO messageQueryDTO) {
        PageUtils.startPage(messageQueryDTO.getPageNum(), messageQueryDTO.getPageSize());
        messageQueryDTO.setReceivedBy(messageQueryDTO.getLoginUser());
        messageQueryDTO.setIsProcess("1");
        messageQueryDTO.setMessageStatus(BusinessDefinition.MessageStatus.RECEIVED);
        List<MessagePO> list = messageMapper.getMessageList(messageQueryDTO);
        PageInfo<MessagePO> pageInfo = new PageInfo<>(list);
        return messageQueryDTO.getPageSize() == 0 ? pageInfo.getList() : pageInfo;
    }

    public void confirmMessageTemp(ConfirmMessageTempDTO confirmMessageTempDTO) {
        Message message = new Message(new MessagePO());
        message.getById(confirmMessageTempDTO.getMessageGid());
        message.getEntity().setLastEventComment(confirmMessageTempDTO.getLastEventComment());
        if (BusinessDefinition.MessageOpType.RECEIVE.equals(confirmMessageTempDTO.getOpType())) {
            // 点击接收消息
            if (!BusinessDefinition.MessageStatus.DISPATCHED.equals(message.getEntity().getMessageStatus())) {
                throw new RuntimeException("操作失败，消息已被接收或关闭");
            }else {
                message.getEntity().setMessageStatus(BusinessDefinition.MessageStatus.RECEIVED);
                message.getEntity().setCloseReason(BusinessDefinition.MessageStatus.RECEIVED);
                message.getEntity().setReceivedTime(new Date());
                message.getEntity().setReceivedBy(confirmMessageTempDTO.getOpUser());
                message.getEntity().setReceivedWay("系统内接收");
                message.update();
            }
        }else if (BusinessDefinition.MessageOpType.HANDLE.equals(confirmMessageTempDTO.getOpType())) {
            // 点击处理消息
            if (!BusinessDefinition.MessageStatus.RECEIVED.equals(message.getEntity().getMessageStatus())) {
                throw new RuntimeException("操作失败，消息状态不是已接收");
            }else {
                message.getEntity().setMessageStatus(BusinessDefinition.MessageStatus.PROCESSED);
                message.update();
            }
        }
    }

    @StreamListener(EcsMessageClient.INPUT)
    public void processMessage(MessageCreateDTO messageCreateDTO){
        String system = messageCreateDTO.getSysName();
        String authKey = messageCreateDTO.getAuthKey();
        if (StringUtils.isEmpty(system) || StringUtils.isEmpty(authKey)){
            log.error("消息{}发送失败, system或authKey为空，请重试", messageCreateDTO.toString());
        }
        String decrypt = AesUtil.decrypt(authKey, aesKey);
        if (StringUtils.isEmpty(decrypt)){
            log.error("消息{}发送失败, authKey解析失败，请重试", messageCreateDTO.toString());
        }
        GeneralIpPO condition = new GeneralIpPO();
        condition.setStatus("启用");
        condition.setSysName(system);
        List<GeneralIpPO> generalIpList = CrudFactory.getCrud().select(condition);
        if (generalIpList == null || generalIpList.size() == 0){
            log.error("消息{}发送失败, 该系统未配置或已禁用，请重试", messageCreateDTO.toString());
        }
        GeneralIpPO generalIpPO = generalIpList.get(0);
        if (!authKey.equals(generalIpPO.getAuthKey()) || authKey.split("-")[0].equals(system)){
            log.error("消息{}发送失败, authKey无效，请重试", messageCreateDTO.toString());
        }
        if (messageCreateDTO.getContent() == null) {
            log.error("消息{}发送失败, 消息发送失败，消息内容不能为空", messageCreateDTO.toString());
        }
        Message message = new Message(new MessagePO());
        if (StringUtils.isEmpty(messageCreateDTO.getMessageGroupGid())) {
            if (StringUtils.isEmpty(messageCreateDTO.getDispatchWay()) || StringUtils.isEmpty(messageCreateDTO.getDispatchUser()) || messageCreateDTO.getUpNumber() == null|| messageCreateDTO.getUpIntervalMinute() == null) {
                log.error("消息{}发送失败, 消息发送失败，消息组id和推送规则不能同时为空", messageCreateDTO.toString());
            }
        } else {
            MessageGroup messageGroup = new MessageGroup(new MessageGroupPO());
            messageGroup.getById(messageCreateDTO.getMessageGroupGid());
            if (messageGroup.getEntity() == null) {

                log.error("消息{}发送失败, 消息发送失败，消息组不存在", messageCreateDTO.toString());
            }
            messageCreateDTO.setDispatchWay(messageGroup.getEntity().getDispatchWay());
            messageCreateDTO.setDispatchUser(messageGroup.getEntity().getDispatchUser());
            messageCreateDTO.setUpNumber(messageGroup.getEntity().getUpNumber());
            messageCreateDTO.setUpIntervalMinute(messageGroup.getEntity().getUpIntervalMinute());
        }
        message.init(messageCreateDTO);
        message.save();
        sendMessage(message.getEntity());
    }
}
