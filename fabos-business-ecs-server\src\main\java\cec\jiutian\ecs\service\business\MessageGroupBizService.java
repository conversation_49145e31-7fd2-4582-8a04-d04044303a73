package cec.jiutian.ecs.service.business;

import cec.jiutian.core.ext.utils.PageUtils;
import cec.jiutian.ecs.domain.MessageGroup;
import cec.jiutian.ecs.dto.MessageGroupDeleteDTO;
import cec.jiutian.ecs.dto.MessageGroupUpdateDTO;
import cec.jiutian.ecs.mapper.MessageGroupMapper;
import cec.jiutian.ecs.po.MessageGroupPO;
import cec.jiutian.ecs.dto.MessageGroupCreateDTO;
import cec.jiutian.ecs.query.dto.MessageGroupQueryDTO;
import cec.jiutian.ecs.vo.MessageGroupVO;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/5/17
 */
@Service
@Transactional
public class MessageGroupBizService {

    private final MessageGroupMapper messageGroupMapper;

    public MessageGroupBizService(MessageGroupMapper messageGroupMapper) {
        this.messageGroupMapper = messageGroupMapper;
    }

    public Object getMessageGroupList(MessageGroupQueryDTO messageGroupQueryDTO) {
        PageUtils.startPage(messageGroupQueryDTO.getPageNum(), messageGroupQueryDTO.getPageSize());
        List<MessageGroupVO> list = getMessageGroup(messageGroupQueryDTO);
        PageInfo<MessageGroupVO> pageInfo = new PageInfo<>(list);
        return messageGroupQueryDTO.getPageSize() == 0 ? pageInfo.getList() : pageInfo;
    }

    private List<MessageGroupVO> getMessageGroup(MessageGroupQueryDTO messageGroupQueryDTO) {
        List<MessageGroupVO> list = messageGroupMapper.getMessageGroupList(messageGroupQueryDTO);
        list.forEach(x -> {
            JSONObject dispatchWay = JSONObject.parseObject(x.getDispatchWay());
            JSONObject dispatchUser = JSONObject.parseObject(x.getDispatchUser());
            if (dispatchWay.get("first") != null){
                x.setFirstWay(dispatchWay.get("first").toString().split(","));
            }
            if (dispatchWay.get("second") != null){
                x.setSecondWay(dispatchWay.get("second").toString().split(","));
            }
            if (dispatchWay.get("third") != null){
                x.setThirdWay(dispatchWay.get("third").toString().split(","));
            }
            if (dispatchUser.get("first") != null){
                x.setFirstUser(dispatchUser.get("first").toString().split(","));
            }
            if (dispatchUser.get("second") != null){
                x.setSecondUser(dispatchUser.get("second").toString().split(","));
            }
            if (dispatchUser.get("third") != null){
                x.setThirdUser(dispatchUser.get("third").toString().split(","));
            }
        });
        return list;
    }

    public Object getEcsMessageGroup() {
        return getMessageGroup(new MessageGroupQueryDTO());
    }

    public void messageGroupCreate(MessageGroupCreateDTO messageGroupCreateDTO) {
        MessageGroup messageGroup = new MessageGroup(new MessageGroupPO());
        messageGroup.init(messageGroupCreateDTO);
        messageGroup.setExtAttribute(messageGroupCreateDTO);
        messageGroup.checkGroupNameOnly();
        messageGroup.save();
    }

    public void messageGroupUpdate(MessageGroupUpdateDTO messageGroupUpdateDTO) {
        MessageGroup messageGroup = new MessageGroup(new MessageGroupPO());
        messageGroup.getById(messageGroupUpdateDTO.getGid());
        messageGroup.init(messageGroupUpdateDTO);
        messageGroup.setExtAttribute(messageGroupUpdateDTO);
        messageGroup.checkGroupNameOnly();
        messageGroup.update();
    }

    public void messageGroupDelete(MessageGroupDeleteDTO messageGroupDeleteDTO) {
        MessageGroup messageGroup = new MessageGroup(new MessageGroupPO());
        messageGroup.getById(messageGroupDeleteDTO.getGid());
        messageGroup.delete();
    }
}
