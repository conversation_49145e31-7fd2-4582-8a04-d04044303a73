package cec.jiutian.ecs.service.business;

import cec.jiutian.core.ext.utils.PageUtils;
import cec.jiutian.ecs.mapper.MessageRecordMapper;
import cec.jiutian.ecs.po.MessagePO;
import cec.jiutian.ecs.po.MessageRecordPO;
import cec.jiutian.ecs.query.dto.MessageRecordQueryDTO;
import com.github.pagehelper.PageInfo;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/5/29
 */
@Service
@Transactional
public class MessageRecordBizService {

    private final MessageRecordMapper messageRecordMapper;

    public MessageRecordBizService(MessageRecordMapper messageRecordMapper) {
        this.messageRecordMapper = messageRecordMapper;
    }

    public Object getMessageRecordList(MessageRecordQueryDTO messageRecordQueryDTO) {
        PageUtils.startPage(messageRecordQueryDTO.getPageNum(), messageRecordQueryDTO.getPageSize());
        List<MessageRecordPO> list = messageRecordMapper.getMessageRecordList(messageRecordQueryDTO);
        PageInfo<MessageRecordPO> pageInfo = new PageInfo<>(list);
        return messageRecordQueryDTO.getPageSize() == 0 ? pageInfo.getList() : pageInfo;
    }
}
