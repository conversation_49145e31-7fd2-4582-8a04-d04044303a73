package cec.jiutian.ecs.service.business;

import cec.jiutian.core.comn.util.BeanUtils;
import cec.jiutian.core.comn.util.StringUtils;
import cec.jiutian.core.ext.utils.PageUtils;
import cec.jiutian.ecs.domain.MessageRecord;
import cec.jiutian.ecs.domain.Notice;
import cec.jiutian.ecs.dto.*;
import cec.jiutian.ecs.handler.MessageHandler;
import cec.jiutian.ecs.handler.MessageHandlerFactory;
import cec.jiutian.ecs.mapper.MessageMapper;
import cec.jiutian.ecs.mapper.NoticeMapper;
import cec.jiutian.ecs.po.MessageRecordPO;
import cec.jiutian.ecs.po.NoticePO;
import cec.jiutian.ecs.query.dto.NoticeQueryDTO;
import cec.jiutian.ecs.vo.NoticeVO;
import com.github.pagehelper.PageInfo;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @date ：2023/5/29 9:35
 * @description：
 */
@Service
@Transactional
public class NoticeBizService {

    private final NoticeMapper noticeMapper;

    private final MessageMapper messageMapper;

    private final MessageHandlerFactory messageHandlerFactory;

    public NoticeBizService(NoticeMapper noticeMapper, MessageMapper messageMapper, MessageHandlerFactory messageHandlerFactory) {
        this.noticeMapper = noticeMapper;
        this.messageMapper = messageMapper;
        this.messageHandlerFactory = messageHandlerFactory;
    }

    public Object getNoticeList(NoticeQueryDTO queryDTO) {
        PageUtils.startPage(queryDTO.getPageNum(), queryDTO.getPageSize());
        List<NoticePO> list = noticeMapper.getNoticeList(queryDTO);
        List<NoticeVO> noticeVOList = new ArrayList<>(list.size());
        list.forEach(messagePO -> {
            NoticeVO noticeVO = new NoticeVO();
            BeanUtils.copyProperties(messagePO, noticeVO);
            noticeVO.setDispatchWay(messagePO.getDispatchWay().split(","));
            noticeVO.setDispatchUser(messagePO.getDispatchUser().split(","));
            noticeVOList.add(noticeVO);
        });
        PageInfo<NoticeVO> pageInfo = new PageInfo<>(noticeVOList);
        return queryDTO.getPageSize() == 0 ? pageInfo.getList() : pageInfo;
    }

    public void addNotice(NoticeCreateDTO noticeCreateDTO) {
        Notice notice = new Notice(new NoticePO());
        notice.init(noticeCreateDTO);
        notice.getEntity().setDispatchWay(StringUtils.join(noticeCreateDTO.getDispatchWay(), ","));
        notice.getEntity().setDispatchUser(StringUtils.join(noticeCreateDTO.getDispatchUser(), ","));
        notice.save();

        MessageRecord messageRecord = new MessageRecord(new MessageRecordPO());
        MessageRecordCreateDTO createDTO = new MessageRecordCreateDTO();
        createDTO.setContent(noticeCreateDTO.getContent());
        createDTO.setDispatchWay(StringUtils.join(noticeCreateDTO.getDispatchWay(), ","));
        createDTO.setDispatchUser(StringUtils.join(noticeCreateDTO.getDispatchUser(), ","));
        createDTO.setUserDTOList(getUserInfo(noticeCreateDTO));
        createDTO.setMessageGid(notice.getEntity().getGid());

        List<String> wayList = Arrays.asList(createDTO.getDispatchWay().split(","));
        wayList.forEach(w -> {
            createDTO.setDispatchWay(w);
            messageRecord.init(createDTO);
            messageRecord.save();
            createDTO.setGid(messageRecord.getEntity().getGid());
            MessageHandler messageHandler = messageHandlerFactory.getMessageHandler(w);
            messageHandler.sendMessage(createDTO);
            createDTO.setGid(null);
        });
    }

    public void updateNotice(NoticeUpdateDTO noticeUpdateDTO) {
        Notice notice = new Notice(new NoticePO());
        notice.getById(noticeUpdateDTO.getGid());
        notice.update();
    }

    public void deleteNotice(String gid) {
        Notice notice = new Notice(new NoticePO());
        notice.getById(gid);
        notice.delete();
    }

    public List<UserDTO> getAllUserInfo(String userName) {
        return messageMapper.getAllUserInfo(userName);
    }

    public List<EnumDTO> getEnumInfo(String enumCode) {
        return messageMapper.getEnumInfo(enumCode);
    }

    private List<UserDTO> getUserInfo(NoticeCreateDTO createDTO) {
        List<UserDTO> list = new ArrayList<>();
        if (createDTO.getDispatchUser() != null) {
            for (String loginId : createDTO.getDispatchUser()) {
                UserDTO userInfo = messageMapper.getUserInfo(loginId);
                list.add(userInfo);
            }
        }
        return list;
    }
}
