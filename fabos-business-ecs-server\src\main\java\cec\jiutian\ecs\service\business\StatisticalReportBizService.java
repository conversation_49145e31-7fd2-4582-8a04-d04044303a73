package cec.jiutian.ecs.service.business;

import cec.jiutian.core.comn.util.StringUtils;
import cec.jiutian.core.ext.utils.PageUtils;
import cec.jiutian.ecs.domain.MessageGroup;
import cec.jiutian.ecs.dto.UserDTO;
import cec.jiutian.ecs.mapper.GeneralIpMapper;
import cec.jiutian.ecs.mapper.MessageGroupMapper;
import cec.jiutian.ecs.mapper.MessageMapper;
import cec.jiutian.ecs.po.GeneralIpPO;
import cec.jiutian.ecs.po.MessageGroupPO;
import cec.jiutian.ecs.po.MessagePO;
import cec.jiutian.ecs.po.TemplatePO;
import cec.jiutian.ecs.query.dto.MessageQueryDTO;
import cec.jiutian.ecs.query.dto.StatisticsQueryDTO;
import cec.jiutian.ecs.util.GenericUtils;
import cec.jiutian.ecs.vo.*;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date ：2023/6/1 14:25
 * @description：
 */
@Service
@Transactional
public class StatisticalReportBizService {

    private final MessageMapper messageMapper;

    private final GeneralIpMapper generalIpMapper;

    private final MessageGroupMapper groupMapper;

    public StatisticalReportBizService(MessageMapper messageMapper, GeneralIpMapper generalIpMapper, MessageGroupMapper groupMapper) {
        this.messageMapper = messageMapper;
        this.generalIpMapper = generalIpMapper;
        this.groupMapper = groupMapper;
    }

    /**
     * @Description 统计人员接收效率
     * <AUTHOR>
     * @Date 2023/6/7 11:24 
     */
    public Object countPersonReceiveRate(StatisticsQueryDTO queryDTO) {
        PageUtils.startPage(queryDTO.getPageNum(), queryDTO.getPageSize());
        List<UserDTO> userList = messageMapper.getAllUserInfo(null);
        MessageQueryDTO messageQueryDTO = new MessageQueryDTO();
        messageQueryDTO.setStartTime(queryDTO.getStartTime());
        messageQueryDTO.setEndTime(queryDTO.getEndTime());
        List<MessagePO> messageList = messageMapper.getMessageList(messageQueryDTO);

        userList.forEach(user -> {
            long messageCount = messageList.stream().filter(m -> {
                Map userMap = JSON.parseObject(m.getDispatchUser());
                return userMap.containsValue(user.getLoginId());
            }).count();
            long receiveCount = messageList.stream().filter(m -> {
                Map userMap = JSON.parseObject(m.getDispatchUser());
                return userMap.containsValue(user.getLoginId()) && StringUtils.isNotEmpty(m.getReceivedBy()) && m.getReceivedBy().equals(user.getLoginId());
            }).count();
            List<MessagePO> receivedCountEvent = messageList.stream().filter(m -> {
                Map userMap = JSON.parseObject(m.getDispatchUser());
                return userMap.containsValue(user.getLoginId()) && StringUtils.isNotEmpty(m.getReceivedBy()) && m.getReceivedBy().equals(user.getLoginId());
            }).collect(Collectors.toList());

            // 计算接收平均耗时
            int min = 0;
            int sec = 0;
            String averageTime = "";
            if (!CollectionUtils.isEmpty(receivedCountEvent)) {
                for (MessagePO event : receivedCountEvent) {
                    Map<String, Integer> result = GenericUtils.getMinAndSecondByNum(event.getCreateTs(), event.getReceivedTime());
                    min = min + result.get("min");
                    sec = sec + result.get("sec");
                }
                min = Math.round(min / receivedCountEvent.size());
                sec = Math.round(sec / receivedCountEvent.size());
                if (sec < 10) {
                    averageTime = min + "分0" + sec + "秒";
                } else averageTime = min + "分" + sec + "秒";
            }

            String rate = GenericUtils.getPercent((int)receiveCount,(int)messageCount);
            user.setMessageCount((int)messageCount);
            user.setReceiveRate(rate);
            user.setAverageTime(averageTime);
        });
        PageInfo<UserDTO> pageInfo = new PageInfo<>(userList);
        return pageInfo;
    }

    /**
     * @Description 根据发送方统计发送消息数量
     * <AUTHOR>
     * @Date 2023/6/7 11:25 
     */
    public Object countMessageBySystem(StatisticsQueryDTO queryDTO){
        MessageQueryDTO messageQueryDTO = new MessageQueryDTO();
        messageQueryDTO.setStartTime(queryDTO.getStartTime());
        messageQueryDTO.setEndTime(queryDTO.getEndTime());
        List<MessagePO> messageList = messageMapper.getMessageList(messageQueryDTO);
        PageUtils.startPage(queryDTO.getPageNum(), queryDTO.getPageSize());
        List<GeneralIpVO> generalIpPOS = generalIpMapper.getGeneralIpVOList(null);

        generalIpPOS.forEach(g -> {
            long messageCount = messageList.stream().filter(m -> m.getSysName().equals(g.getSysName())).count();
            g.setSystemName(g.getSysName());
            g.setSendCount((int)messageCount);
        });
        PageInfo<GeneralIpVO> pageInfo = new PageInfo<>(generalIpPOS);
        return pageInfo;
    }

    /**
     * @Description 统计消息组接收率
     * <AUTHOR>
     * @Date 2023/6/7 11:25 
     */
    public Object countGroupReceiveRate(StatisticsQueryDTO queryDTO){
        PageUtils.startPage(queryDTO.getPageNum(), queryDTO.getPageSize());
        List<MessageGroupVO> groupList = groupMapper.getMessageGroupList(null);
        MessageQueryDTO messageQueryDTO = new MessageQueryDTO();
        messageQueryDTO.setStartTime(queryDTO.getStartTime());
        messageQueryDTO.setEndTime(queryDTO.getEndTime());
        List<MessagePO> messageList = messageMapper.getMessageList(messageQueryDTO);

        groupList.forEach(g -> {
            long messageCount = messageList.stream().filter(m -> StringUtils.isNotEmpty(m.getMessageGroupGid()) && m.getMessageGroupGid().equals(g.getGid())).count();
            long receiveCount = messageList.stream().filter(m -> StringUtils.isNotEmpty(m.getMessageGroupGid())
                    && m.getMessageGroupGid().equals(g.getGid()) && (m.getMessageStatus().equals("已接收") || m.getMessageStatus().equals("已处理"))).count();

            List<MessagePO> receivedCountEvent = messageList.stream().filter(m -> StringUtils.isNotEmpty(m.getMessageGroupGid())
                    && m.getMessageGroupGid().equals(g.getGid()) && (m.getMessageStatus().equals("已接收") || m.getMessageStatus().equals("已处理"))).collect(Collectors.toList());
            // 计算接收平均耗时
            int min = 0;
            int sec = 0;
            String averageTime = "";
            if (!CollectionUtils.isEmpty(receivedCountEvent)) {
                for (MessagePO event : receivedCountEvent) {
                    Map<String, Integer> result = GenericUtils.getMinAndSecondByNum(event.getCreateTs(), event.getReceivedTime());
                    min = min + result.get("min");
                    sec = sec + result.get("sec");
                }
                min = Math.round(min / receivedCountEvent.size());
                sec = Math.round(sec / receivedCountEvent.size());
                if (sec < 10) {
                    averageTime = min + "分0" + sec + "秒";
                } else averageTime = min + "分" + sec + "秒";
            }

            String rate = GenericUtils.getPercent((int)receiveCount,(int)messageCount);
            g.setMessageCount((int)messageCount);
            g.setReceiveRate(rate);
            g.setAverageTime(averageTime);
        });
        PageInfo<MessageGroupVO> pageInfo = new PageInfo<>(groupList);
        return pageInfo;
    }

    public Object messageSendStatistical(StatisticsQueryDTO queryDTO){
        MessageQueryDTO messageQueryDTO = new MessageQueryDTO();
        messageQueryDTO.setStartTime(queryDTO.getStartTime());
        messageQueryDTO.setEndTime(queryDTO.getEndTime());
        List<ResultVO> messageList = messageMapper.messageSendCount(messageQueryDTO);
        return messageList;
    }
}
