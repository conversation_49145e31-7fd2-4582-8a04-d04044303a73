package cec.jiutian.ecs.service.business;

import cec.jiutian.core.comn.util.StringUtils;
import cec.jiutian.core.ext.utils.PageUtils;
import cec.jiutian.ecs.domain.TemplateAttribute;
import cec.jiutian.ecs.dto.TemplateAttributeCreateDTO;
import cec.jiutian.ecs.dto.TemplateAttributeUpdateDTO;
import cec.jiutian.ecs.mapper.TemplateAttributeMapper;
import cec.jiutian.ecs.po.TemplateAttributePO;
import cec.jiutian.ecs.query.dto.TemplateAttributeQueryDTO;
import com.github.pagehelper.PageInfo;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @date ：2023/5/19 11:07
 * @description：消息模板属性 service
 */
@Service
@Transactional
public class TemplateAttributeBizService {

    private final TemplateAttributeMapper templateAttributeMapper;

    public TemplateAttributeBizService(TemplateAttributeMapper templateAttributeMapper) {
        this.templateAttributeMapper = templateAttributeMapper;
    }

    public Object queryTemplateAttribute(TemplateAttributeQueryDTO queryDTO) {
        PageUtils.startPage(queryDTO.getPageNum(), queryDTO.getPageSize());
        List<TemplateAttributePO> list = templateAttributeMapper.getTemplateAttributeList(queryDTO);
        PageInfo<TemplateAttributePO> pageInfo = new PageInfo<>(list);
        return queryDTO.getPageSize() == 0 ? pageInfo.getList() : pageInfo;
    }

    public boolean checkUnique(String attributeName, String templateGid, String gid) {
        TemplateAttributePO templateAttributePO = templateAttributeMapper.selectTemplateAttributeByName(attributeName, templateGid);
        if (templateAttributePO != null && StringUtils.isNotEmpty(gid) && templateAttributePO.getGid().equals(gid)) {
            return true;
        }
        return templateAttributePO == null;
    }

    public void addTemplateAttribute(TemplateAttributeCreateDTO templateAttributeCreateDTO) {
        TemplateAttributeQueryDTO queryDTO = new TemplateAttributeQueryDTO();
        queryDTO.setTemplateGid(templateAttributeCreateDTO.getTemplateGid());
        queryDTO.setOrderNumber(templateAttributeCreateDTO.getOrderNumber());
        List<TemplateAttributePO> templateAttributePOs = templateAttributeMapper.getTemplateAttributeList(queryDTO);
        if (!CollectionUtils.isEmpty(templateAttributePOs)){
            throw new RuntimeException("该模板中已有该属性序号");
        }
        TemplateAttribute templateAttribute = new TemplateAttribute(new TemplateAttributePO());
        templateAttribute.init(templateAttributeCreateDTO);
        templateAttribute.save();
    }

    public void updateTemplateAttribute(TemplateAttributeUpdateDTO templateAttributeUpdateDTO) {
        TemplateAttribute templateAttribute = new TemplateAttribute(new TemplateAttributePO());
        templateAttribute.getById(templateAttributeUpdateDTO.getGid());
        templateAttribute.init(templateAttributeUpdateDTO);
        templateAttribute.update();
    }

    public void delete(String gid) {
        TemplateAttribute templateAttribute = new TemplateAttribute(new TemplateAttributePO());
        templateAttribute.getById(gid);
        templateAttribute.delete();
    }
}
