package cec.jiutian.ecs.task;

import cec.jiutian.ecs.domain.Message;
import cec.jiutian.ecs.po.MessagePO;
import cec.jiutian.ecs.service.business.MessageBizService;
import cec.jiutian.ecs.service.business.definition.BusinessDefinition;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/5/23
 */
@RestController
public class MessageTimeoutTask {

    private final MessageBizService messageBizService;

    public MessageTimeoutTask(MessageBizService messageBizService) {
        this.messageBizService = messageBizService;
    }

    @XxlJob("messageTimeout")
    public void messageTimeout(){
        // 查询全部状态为已派送的消息
        List<MessagePO> messageList = messageBizService.getMessageListByStatus(BusinessDefinition.MessageStatus.DISPATCHED);
        if (StringUtils.isEmpty(messageList)){
            return;
        }
        messageList.forEach(messagePO -> {
            // 判断当前推送事件是否已经超时
            Date lastUpDate = messagePO.getLastUpDate();
            Date timeoutTime = new Date(lastUpDate.getTime() + messagePO.getUpIntervalMinute() * 60 * 1000);
            if (timeoutTime.before(new Date())){
                if (messagePO.getUpNumber().equals(messagePO.getCurrentUpNumber())){
                    // 消息超时关闭
                    messagePO.setMessageStatus(BusinessDefinition.MessageStatus.TIMEOUT_CLOSE);
                    messagePO.setCloseReason(BusinessDefinition.MessageStatus.TIMEOUT_CLOSE);
                    Message message = new Message(messagePO);
                    message.update();
                }else {
                    messagePO.setLastUpDate(new Date());
                    messagePO.setCurrentUpNumber(messagePO.getCurrentUpNumber() + 1);
                    Message message = new Message(messagePO);
                    message.update();
                    messageBizService.sendMessage(message.getEntity());
                }
            }
        });
    }
}
