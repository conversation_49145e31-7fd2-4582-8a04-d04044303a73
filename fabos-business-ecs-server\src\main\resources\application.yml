app:
  id: fabos-business-ecs
spring:
  application:
    name: fabos-business-ecs
  mail:
    host: smtp.cecjiutian.com # 发件服务器地址,不同邮件平台地址不同
    port: 587 #常用邮件端口25、109、110、143、465、995、993、994 如果开启了SSL安全则使用对应的端口号，25为非加密端口号
    username: <EMAIL> #发送邮件的账号
    sendFrom: <EMAIL> #发送邮件的账号
    password: Jiutian@!23456 #发送邮件账号的授权码,这里的授权码不是验证码.需要到邮箱
    default-encoding: utf-8 #设置编码
    properties: # 设置邮件超时时间防止服务器阻塞
      timeout: 5000
      connection-timeout: 5000
      write-timeout: 5000
apollo:
  bootstrap:
    enabled: true
    namespaces: application
    eagerLoad:
      enabled: true

management:
  endpoints:
    web:
      exposure:
        include: "*"

mybatis:
  type-handlers-package: cec.jiutian.ecs.common.handler

# 字符串加密key
aes:
  key: aWXoyC4UNb756984

confirm:
  url: http://***********:8205/confirm?secret=


sms:
  uid: test
  key: test
  url: url
