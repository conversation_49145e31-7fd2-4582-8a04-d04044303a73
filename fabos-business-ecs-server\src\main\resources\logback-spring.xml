<configuration>
    <springProperty scope="context" name="grayloghost" source="graylog.host"/>
    <springProperty scope="context" name="graylogport" source="graylog.port"/>
    <springProperty scope="context" name="db" source="mybatis.configuration.database-id"/>
    <springProperty scope="context" name="namespace" source="apollo.bootstrap.namespaces"/>
    <springProperty scope="context" name="env" source="env"/>
    <appender name="consoleLog" class="ch.qos.logback.core.ConsoleAppender">
        <layout class="ch.qos.logback.classic.PatternLayout">
            <pattern>
                %d - %msg%n
            </pattern>
        </layout>
    </appender>

    <!--格式化输出：%d表示日期，%thread表示线程名，%-5level：级别从左显示5个字符宽度 %msg：日志消息，%logger{50}包名缩写，%n是换行符 -->
    <property name="log_pattern" value="%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{50} - %msg%n"/>
    <!--定义日志文件的存储地址 勿在 LogBack 的配置中使用相对路径-->
    <!-- 日志存储路径 -->
    <!--    <springProperty-->
    <!--            scope="context"-->
    <!--            name="defaultLogDir"-->
    <!--            source="log.history.logDir"-->
    <!--    />-->

    <!-- 日志备份保留时长 -->
    <springProperty
            scope="context"
            name="logMaxHistory"
            source="log.history.maxHistory"
    />

    <!-- 日志大小 -->
    <springProperty
            scope="context"
            name="logMaxSize"
            source="log.history.logMaxSize"
    />
    <property name="logMaxSize" value="200MB"/>
    <!--    <property name="defaultLogDir" value="${logPath}"/>-->
    <springProperty
            scope="context"
            name="defaultLogDir"
            source="log.dir.path"
            defaultValue="/app/data/app/instance1/log"
    />

    <!--文件日志， 按照每天生成日志文件 -->
    <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <!-- 正在记录的日志文件的路径及文件名 -->
        <file>${defaultLogDir}/app.log</file>
        <!--TimeBasedRollingPolicy 基于时间来定义轮转策略 -->
        <!--SizeAndTimeBasedRollingPolicy 基于大小以及时间的轮转策略-->
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <!--日志文件输出的路径和文件名-->
            <!--该属性定义了轮转时的属性名。它的值应该由文件名加上一个 %d 的占位符。%d 应该包含 java.text.SimpleDateFormat 中规定的日期格式。
            如果省略掉这个日期格式，那么就默认为 yyyy-MM-dd。轮转周期是通过 fileNamePattern 推断出来的。
            注意事项：
            1.如果FileNamePattern中指定多个 %d，只能报留一个%d作为主要的，用于推断轮转周期。其它的 %d 占位符必须通过 'aux' 标记为辅助的。
            2.MaxHistory 用来控制最多保留多少数量的归档文件，将会异步删除旧的文件。
                         保留日志的量 = 轮转周期 * MaxHistory
            3.FileNamePattern中除了 %d 之外还有 %i。这两个占位符都是强制要求的。在当前时间还没有到达周期轮转之前，日志文件达到了 maxFileSize 指定的大小，
              会进行归档，递增索引从 0 开始。
            -->
            <!--轮询周期：天-->
            <!-- 每天轮转（晚上零点），自动将归档文件压缩成 GZIP 格式，减少日志占用空间-->
            <FileNamePattern>${defaultLogDir}/%d{yyyy-MM-dd, aux}/app.%d.%i.zip</FileNamePattern>
            <!--日志文件保留轮询周期个数-->
            <MaxHistory>${logMaxHistory:-15}</MaxHistory>
            <!--单个日志文件最大的大小-->
            <MaxFileSize>${logMaxSize:-100MB}</MaxFileSize>
            <!--这个可选属性用来控制所有归档文件总的大小。当达到这个大小后，旧的归档文件将会被异步的删除。使用这个属性时还需要设置 maxHistory 属性。
            而且，maxHistory 将会被作为第一条件，该属性作为第二条件。-->
            <!--归档文件总的大小-->
            <totalSizeCap>1GB</totalSizeCap>
            <!--cleanHistoryOnStart=true时在 appender 启动的时候，归档文件将会被删除。默认的值为 false-->
            <cleanHistoryOnStart>true</cleanHistoryOnStart>
        </rollingPolicy>
        <!--<append>：如果是 true，日志被追加到文件结尾，如果是 false，清空现存文件，默认是true。-->
        <append>false</append>
        <encoder>
            <pattern>${log_pattern}</pattern>
            <charset>utf8</charset>
        </encoder>
    </appender>

    <appender name="GELF" class="de.siegmar.logbackgelf.GelfUdpAppender">
        <graylogHost>${grayloghost}</graylogHost>
        <graylogPort>${graylogport}</graylogPort>
        <maxChunkSize>508</maxChunkSize>
        <useCompression>true</useCompression>
        <encoder class="de.siegmar.logbackgelf.GelfEncoder">

            <includeRawMessage>true</includeRawMessage>
            <includeMarker>true</includeMarker>
            <includeMdcData>true</includeMdcData>
            <includeCallerData>false</includeCallerData>
            <includeRootCauseData>false</includeRootCauseData>
            <includeLevelName>true</includeLevelName>
            <shortPatternLayout class="ch.qos.logback.classic.PatternLayout">
                <pattern>%d - %m%nopex</pattern>
            </shortPatternLayout>
            <fullPatternLayout class="ch.qos.logback.classic.PatternLayout">
                <pattern>%d - %m%n</pattern>
            </fullPatternLayout>
            <numbersAsString>false</numbersAsString>
            <staticField>app_name:fabos-business-ecs-server-server</staticField>
            <staticField>os_arch:${os.arch}</staticField>
            <staticField>os_name:${os.name}</staticField>
            <staticField>db_type:${namespace}</staticField>
            <staticField>env_name:${env}</staticField>
        </encoder>
    </appender>


    <root level="info">
        <appender-ref ref="consoleLog"/>
        <appender-ref ref="GELF"/>
        <appender-ref ref="FILE"/>
    </root>

</configuration>
