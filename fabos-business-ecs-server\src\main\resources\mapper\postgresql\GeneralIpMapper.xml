<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cec.jiutian.ecs.mapper.GeneralIpMapper">

    <resultMap id="BaseResultMap" type="cec.jiutian.ecs.po.GeneralIpPO">
        <id column="gid" property="gid" jdbcType="VARCHAR"/>
        <result column="sys_name" property="sysName" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="VARCHAR"/>
        <result column="CREATE_TS" property="createTs" jdbcType="TIMESTAMP"/>
        <result column="CREATE_USER" property="createUser" jdbcType="VARCHAR"/>
        <result column="LAST_TRXN_TS" property="lastTrxnTs" jdbcType="TIMESTAMP"/>
        <result column="LAST_TRXN_USER" property="lastTrxnUser" jdbcType="VARCHAR"/>
        <result column="lst_evnt_cmnt" property="lastEventComment"/>
    </resultMap>

    <select id="getGeneralIpList" resultMap="BaseResultMap">
        select gid, sys_name, status, create_ts, create_user, last_trxn_ts, last_trxn_user, lst_evnt_cmnt, oid from ecs_general_ip
        <where>
            <if test="sysName != null and sysName != ''">
                and sys_name like concat('%',#{sysName}::text,'%')
            </if>
            <if test="status != null and status != ''">
                and status = #{status}
            </if>
        </where>
        order by CREATE_TS desc
    </select>

    <resultMap id="VOResultMap" type="cec.jiutian.ecs.vo.GeneralIpVO">
        <id column="gid" property="gid" jdbcType="VARCHAR"/>
        <result column="sys_name" property="sysName" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="VARCHAR"/>
        <result column="CREATE_TS" property="createTs" jdbcType="TIMESTAMP"/>
        <result column="CREATE_USER" property="createUser" jdbcType="VARCHAR"/>
        <result column="LAST_TRXN_TS" property="lastTrxnTs" jdbcType="TIMESTAMP"/>
        <result column="LAST_TRXN_USER" property="lastTrxnUser" jdbcType="VARCHAR"/>
        <result column="lst_evnt_cmnt" property="lastEventComment"/>
    </resultMap>
    <select id="getGeneralIpVOList" resultMap="VOResultMap">
        select gid, sys_name, status, create_ts, create_user, last_trxn_ts, last_trxn_user, lst_evnt_cmnt, oid from ecs_general_ip
        <where>
            <if test="sysName != null and sysName != ''">
                and sys_name like concat('%',#{sysName}::text,'%')
            </if>
            <if test="status != null and status != ''">
                and status = #{status}
            </if>
        </where>
    </select>

</mapper>
