<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cec.jiutian.ecs.mapper.MessageGroupMapper">

    <resultMap id="BaseResultMap" type="cec.jiutian.ecs.po.MessageGroupPO">
        <id column="gid" property="gid" jdbcType="VARCHAR"/>
        <result column="group_name" property="groupName" jdbcType="VARCHAR"/>
        <result column="dispatch_way" property="dispatchWay" jdbcType="VARCHAR"/>
        <result column="dispatch_user" property="dispatchUser" jdbcType="VARCHAR"/>
        <result column="up_number" property="upNumber" jdbcType="BIGINT"/>
        <result column="up_interval_minute" property="upIntervalMinute" jdbcType="BIGINT"/>
        <result column="CREATE_TS" property="createTs" jdbcType="TIMESTAMP"/>
        <result column="CREATE_USER" property="createUser" jdbcType="VARCHAR"/>
        <result column="LAST_TRXN_TS" property="lastTrxnTs" jdbcType="TIMESTAMP"/>
        <result column="LAST_TRXN_USER" property="lastTrxnUser" jdbcType="VARCHAR"/>
        <result column="lst_evnt_cmnt" property="lastEventComment"/>
    </resultMap>

    <resultMap id="messageGroupResultMap" type="cec.jiutian.ecs.vo.MessageGroupVO" extends="BaseResultMap">

    </resultMap>

    <select id="getMessageGroupList" resultMap="messageGroupResultMap">
        select gid, group_name, dispatch_way, dispatch_user, up_number, up_interval_minute, create_ts, create_user, last_trxn_ts, last_trxn_user, lst_evnt_cmnt, oid from ecs_message_group
        <where>
            <if test="dispatchWay != null and dispatchWay != ''">
                and dispatch_way like concat('%',#{dispatchWay}::text,'%')
            </if>
            <if test="dispatchUser != null and dispatchUser != ''">
                and dispatch_user like concat('%',#{dispatchUser}::text,'%')
            </if>
            <if test="groupName != null and groupName != ''">
                and group_name like concat('%',#{groupName}::text,'%')
            </if>
            <if test="startTime != null and endTime != null"  >
                and CREATE_TS &gt;= to_date(#{startTime},'yyyy-mm-dd hh24:mi:ss') and m.CREATE_TS &lt;= to_date(#{endTime},'yyyy-mm-dd hh24:mi:ss')
            </if>
        </where>
        order by CREATE_TS desc
    </select>

    <insert id="addMessageGroup" parameterType="cec.jiutian.ecs.dto.MessageGroupCreateDTO">
        insert into ecs_message_group(
        <trim suffixOverrides =",">
        <if test="gid != null and gid != ''">gid,</if>
        <if test="groupName != null and groupName != ''">group_name,</if>
        <if test="dispatchWay != null">dispatch_way,</if>
        <if test="dispatchUser != null">dispatch_user,</if>
        <if test="upNumber != null and upNumber != ''">up_number,</if>
        <if test="upIntervalMinute != null and upIntervalMinute !=''">up_interval_minute,</if>
        <if test="createTs != null">create_ts,</if>
        <if test="createUser != null and createUser != ''">create_user,</if>
        <if test="lastTrxnTs != null">last_trxn_ts,</if>
        <if test="lastTrxnUser != null and lastTrxnUser != ''">last_trxn_user,</if>
        </trim>
        )values(
        <trim suffixOverrides =",">
        <if test="gid != null and gid != ''">#{gid},</if>
        <if test="groupName != null and groupName != ''">#{groupName},</if>
        <if test="dispatchWay != null">#{dispatchWay},</if>
        <if test="dispatchUser != null">#{dispatchUser},</if>
        <if test="upNumber != null and upNumber != ''">#{upNumber},</if>
        <if test="upIntervalMinute != null and upIntervalMinute !=''">#{upIntervalMinute},</if>
        <if test="createTs != null and createTs != ''">#{createTs},</if>
        <if test="createUser != null and createUser != ''">#{createUser},</if>
        <if test="lastTrxnTs != null and lastTrxnTs != ''">#{lastTrxnTs},</if>
        <if test="lastTrxnUser != null and lastTrxnUser != ''">#{lastTrxnUser},</if>
        </trim>
        )
    </insert>
</mapper>
