<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cec.jiutian.ecs.mapper.MessageMapper">
    <resultMap id="BasicDataResult" type="cec.jiutian.ecs.po.MessagePO">
        <id column="GID" property="gid" jdbcType="VARCHAR"/>
        <result column="CREATE_TS" property="createTs" jdbcType="TIMESTAMP"/>
        <result column="CREATE_USER" property="createUser" jdbcType="VARCHAR"/>
        <result column="LAST_TRXN_TS" property="lastTrxnTs" jdbcType="TIMESTAMP"/>
        <result column="LAST_TRXN_USER" property="lastTrxnUser" jdbcType="VARCHAR"/>
        <result column="DISPATCH_WAY" property="dispatchWay" jdbcType="VARCHAR"/>
        <result column="DISPATCH_USER" property="dispatchUser" jdbcType="VARCHAR"/>
        <result column="UP_NUMBER" property="upNumber" jdbcType="BIGINT"/>
        <result column="CURRENT_UP_NUMBER" property="currentUpNumber" jdbcType="BIGINT"/>
        <result column="LAST_UP_DATE" property="lastUpDate" jdbcType="TIMESTAMP"/>
        <result column="UP_INTERVAL_MINUTE" property="upIntervalMinute" jdbcType="BIGINT"/>
        <result column="MESSAGE_GROUP_GID" property="messageGroupGid" jdbcType="VARCHAR"/>
        <result column="MESSAGE_TEMPLATE_GID" property="messageTemplateGid" jdbcType="VARCHAR"/>
        <result column="CONTENT" property="content" jdbcType="VARCHAR"/>
        <result column="RECEIVED_BY" property="receivedBy" jdbcType="VARCHAR"/>
        <result column="RECEIVED_WAY" property="receivedWay" jdbcType="VARCHAR"/>
        <result column="RECEIVED_TIME" property="receivedTime" jdbcType="TIMESTAMP"/>
        <result column="MESSAGE_STATUS" property="messageStatus" jdbcType="VARCHAR"/>
        <result column="CLOSE_REASON" property="closeReason" jdbcType="VARCHAR"/>
        <result column="IS_PROCESS" property="isProcess" jdbcType="CHAR"/>
        <result column="IS_UPGRADATION" property="isUpgradation" jdbcType="CHAR"/>
        <result column="sys_name" property="sysName" jdbcType="VARCHAR"/>
    </resultMap>

    <select id="getMessageList" resultMap="BasicDataResult">
        select * from ecs_message m
        where m.gid is not null
        <if test="dispatchWay != null and dispatchWay != ''">
            and m.dispatch_way like concat('%',#{dispatchWay}::text,'%')
        </if>
        <if test="dispatchUser != null and dispatchUser != ''">
            and m.dispatch_user like concat('%',#{dispatchUser}::text,'%')
        </if>
        <if test="messageGroupGid != null and messageGroupGid != ''">
            and m.message_group_gid like concat('%',#{messageGroupGid}::text,'%')
        </if>
        <if test="messageTemplateGid != null and messageTemplateGid != ''">
            and m.message_template_gid like concat('%',#{messageTemplateGid}::text,'%')
        </if>
        <if test="content != null and content != ''">
            and m.content like concat('%',#{content}::text,'%')
        </if>
        <if test="receivedBy != null and receivedBy != ''">
            and m.received_by = #{receivedBy}
        </if>
        <if test="messageStatus != null and messageStatus != ''">
            and m.message_status = #{messageStatus}
        </if>
        <if test="isProcess != null and isProcess != ''">
            and m.is_process = #{isProcess}
        </if>
        <if test="startTime != null and endTime != null"  >
            and m.CREATE_TS &gt;= to_date(#{startTime},'yyyy-mm-dd hh24:mi:ss') and m.CREATE_TS &lt;= to_date(#{endTime},'yyyy-mm-dd hh24:mi:ss')
        </if>
        order by m.CREATE_TS desc
    </select>

    <insert id="addMessage" parameterType="cec.jiutian.ecs.po.MessagePO">
        insert into ecs_message(
        <if test="gid != null and gid != ''">gid,</if>
        <if test="createTs != null">create_ts,</if>
        <if test="createUser != null and createUser != ''">create_user,</if>
        <if test="lastTrxnTs != null">last_trxn_ts,</if>
        <if test="lastTrxnUser != null and lastTrxnUser != ''">last_trxn_user,</if>
        <if test="dispatchWay != null and dispatchWay != ''">dispatch_way,</if>
        <if test="dispatchUser != null and dispatchUser != ''">dispatch_user,</if>
        <if test="upNumber != null and upNumber != ''">up_number,</if>
        <if test="currentUpNumber != null and currentUpNumber != ''">current_up_number,</if>
        <if test="lastUpDate != null">last_up_date,</if>
        <if test="upIntervalMinute != null and upIntervalMinute !=''">up_interval_minute,</if>
        <if test="messageGroupGid != null and messageGroupGid = !=''">message_group_gid,</if>
        <if test="messageTemplateGid != null and messageTemplateGid != ''">message_template_gid,</if>
        <if test="content != null and content != ''">content,</if>
        <if test="receivedBy != null and receivedBy != ''">received_by,</if>
        <if test="receivedWay != null and receivedWay != ''">received_way,</if>
        <if test="receivedTime != null">received_time,</if>
        <if test="messageStatus != null and messageStatus != ''">message_status,</if>
        <if test="closeReason != null and closeReason != ''">close_reason,</if>
        <if test="isProcess != null and isProcess != ''">is_process,</if>
        <if test="isUpgradation != null and isUpgradation != ''">is_upgradation,</if>
        )values(
        <if test="gid != null and gid != ''">#{gid},</if>
        <if test="createTs != null and createTs != ''">#{createTs},</if>
        <if test="createUser != null and createUser != ''">#{createUser},</if>
        <if test="lastTrxnTs != null and lastTrxnTs != ''">#{lastTrxnTs},</if>
        <if test="lastTrxnUser != null and lastTrxnUser != ''">#{lastTrxnUser},</if>
        <if test="dispatchWay != null and dispatchWay != ''">#{dispatchWay},</if>
        <if test="dispatchUser != null and dispatchUser != ''">#{dispatchUser},</if>
        <if test="upNumber != null and upNumber != ''">#{upNumber},</if>
        <if test="currentUpNumber != null and currentUpNumber != ''">#{currentUpNumber},</if>
        <if test="lastUpDate != null">#{lastUpDate},</if>
        <if test="upIntervalMinute != null and upIntervalMinute !=''">#{upIntervalMinute},</if>
        <if test="messageGroupGid != null and messageGroupGid = !=''">#{messageGroupGid},</if>
        <if test="messageTemplateGid != null and messageTemplateGid != ''">#{messageTemplateGid},</if>
        <if test="content != null and content != ''">#{content},</if>
        <if test="receivedBy != null and receivedBy != ''">#{receivedBy},</if>
        <if test="receivedWay != null and receivedWay != ''">#{receivedWay},</if>
        <if test="receivedTime != null">#{receivedTime},</if>
        <if test="messageStatus != null and messageStatus != ''">#{messageStatus},</if>
        <if test="closeReason != null and closeReason != ''">#{closeReason},</if>
        <if test="isProcess != null and isProcess != ''">#{isProcess},</if>
        <if test="isUpgradation != null and isUpgradation != ''">#{isUpgradation},</if>
        )
    </insert>

    <resultMap id="userResult" type="cec.jiutian.ecs.dto.UserDTO">
        <result column="acnt_lgn_id" property="loginId" jdbcType="VARCHAR"/>
        <result column="acnt_nm" property="accountName" jdbcType="VARCHAR"/>
        <result column="rgstr_phn_nb" property="phoneNumber" jdbcType="VARCHAR"/>
        <result column="rgstr_eml_tx" property="email" jdbcType="VARCHAR"/>
    </resultMap>
    <select id="getUserInfo" resultMap="userResult">
        select * from fd_acnt a
        inner join fd_acnt_orgnzn_rltn o on o.acnt_id = a.id
        where a.vld_flg = 'Y' and a.acnt_lgn_id = #{loginId}
        limit 1
    </select>

    <select id="getAllUserInfo" parameterType="java.lang.String" resultMap="userResult">
        select * from fd_acnt a
        inner join fd_acnt_orgnzn_rltn o on o.acnt_id = a.id
        where a.vld_flg = 'Y'
        <if test="accountName != null and accountName != ''">
            and a.acnt_nm like concat('%',#{accountName}::text,'%')
        </if>
    </select>

    <resultMap id="enumResult" type="cec.jiutian.ecs.dto.EnumDTO">
        <result column="enum_cd" property="enumCode" jdbcType="VARCHAR"/>
        <result column="enum_vlu" property="enumValue" jdbcType="VARCHAR"/>
        <result column="enum_vlu_ds" property="enumValueDs" jdbcType="VARCHAR"/>
    </resultMap>
    <select id="getEnumInfo" parameterType="java.lang.String" resultMap="enumResult">
        select * from fr_enum_vlu where enum_cd = #{enumCode}
    </select>

    <resultMap id="statisticResult" type="cec.jiutian.ecs.vo.ResultVO">
        <result column="date" property="key" jdbcType="VARCHAR"/>
        <result column="count_num" property="value" jdbcType="VARCHAR"/>
    </resultMap>
    <select id="messageSendCount" resultMap="statisticResult">
        select a.date date,coalesce(b.count_num,0) count_num
        from
        (select to_char(t,'HH24') date
        from generate_series(cast('2023-06-01 00:00:00' as timestamp),cast('2023-06-01 23:00:00' as timestamp),'1 hour') t) a
        left join
        (select to_char(create_ts,'HH24') date,count(distinct gid) count_num
        from ecs_message
        where gid is not null
        <if test="startTime != null and endTime != null"  >
            and CREATE_TS &gt;= to_date(#{startTime},'yyyy-mm-dd hh24:mi:ss') and CREATE_TS &lt;= to_date(#{endTime},'yyyy-mm-dd hh24:mi:ss')
        </if>
        group by date) b on a.date=b.date
        ORDER BY date
    </select>
</mapper>
