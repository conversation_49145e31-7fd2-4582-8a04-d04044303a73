<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cec.jiutian.ecs.mapper.MessageRecordMapper">
    <resultMap id="BasicDataResult" type="cec.jiutian.ecs.po.MessageRecordPO">
        <id column="GID" property="gid" jdbcType="VARCHAR"/>
        <id column="MESSAGE_GID" property="messageGid" jdbcType="VARCHAR"/>
        <result column="CREATE_TS" property="createTs" jdbcType="TIMESTAMP"/>
        <result column="CREATE_USER" property="createUser" jdbcType="VARCHAR"/>
        <result column="LAST_TRXN_TS" property="lastTrxnTs" jdbcType="TIMESTAMP"/>
        <result column="LAST_TRXN_USER" property="lastTrxnUser" jdbcType="VARCHAR"/>
        <result column="DISPATCH_WAY" property="dispatchWay" jdbcType="VARCHAR"/>
        <result column="DISPATCH_USER" property="dispatchUser" jdbcType="VARCHAR"/>
        <result column="CONTENT" property="content" jdbcType="VARCHAR"/>
        <result column="RESPONSE_LINK" property="responseLink" jdbcType="VARCHAR"/>
    </resultMap>

    <select id="getMessageRecordList" resultMap="BasicDataResult">
        select * from ecs_message_record m
        where m.gid is not null
        <if test="dispatchWay != null and dispatchWay != ''">
            and m.dispatch_way like concat('%',#{dispatchWay}::text,'%')
        </if>
        <if test="dispatchUser != null and dispatchUser != ''">
            and m.dispatch_user like concat('%',#{dispatchUser}::text,'%')
        </if>
        <if test="content != null and content != ''">
            and m.content like concat('%',#{content}::text,'%')
        </if>
        <if test="startTime != null and endTime != null"  >
            and m.CREATE_TS &gt;= to_date(#{startTime},'yyyy-mm-dd hh24:mi:ss') and m.CREATE_TS &lt;= to_date(#{endTime},'yyyy-mm-dd hh24:mi:ss')
        </if>
        order by m.CREATE_TS desc
    </select>

</mapper>
