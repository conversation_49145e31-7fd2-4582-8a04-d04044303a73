<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cec.jiutian.ecs.mapper.NoticeMapper">
    <resultMap id="BasicDataResult" type="cec.jiutian.ecs.po.NoticePO">
        <id column="GID" property="gid" jdbcType="VARCHAR"/>
        <result column="CREATE_TS" property="createTs" jdbcType="TIMESTAMP"/>
        <result column="CREATE_USER" property="createUser" jdbcType="VARCHAR"/>
        <result column="LAST_TRXN_TS" property="lastTrxnTs" jdbcType="TIMESTAMP"/>
        <result column="LAST_TRXN_USER" property="lastTrxnUser" jdbcType="VARCHAR"/>
        <result column="DISPATCH_WAY" property="dispatchWay" jdbcType="VARCHAR"/>
        <result column="DISPATCH_USER" property="dispatchUser" jdbcType="VARCHAR"/>
        <result column="CONTENT" property="content" jdbcType="VARCHAR"/>
        <result column="lst_evnt_cmnt" property="lastEventComment"/>
    </resultMap>

    <select id="getNoticeList" resultMap="BasicDataResult">
        select * from ecs_notice t
        where t.gid is not null
        <if test="dispatchWay != null and dispatchWay != ''">
            and t.DISPATCH_WAY like concat('%',#{dispatchWay}::text,'%')
        </if>
        <if test="dispatchUser != null and dispatchUser != ''">
            and t.DISPATCH_USER like concat('%',#{dispatchUser}::text,'%')
        </if>
        <if test="content != null and content != ''">
            and t.content like concat('%',#{content}::text,'%')
        </if>
        <if test="startTime != null and endTime != null"  >
            and t.CREATE_TS &gt;= to_date(#{startTime},'yyyy-mm-dd hh24:mi:ss') and t.CREATE_TS &lt;= to_date(#{endTime},'yyyy-mm-dd hh24:mi:ss')
        </if>
        order by t.CREATE_TS desc
    </select>

    <select id="selectTemplateByName" parameterType="java.lang.String" resultMap="BasicDataResult">
        select * from ecs_template t where t.TEMPLATE_NAME = #{templateName}
    </select>

    <insert id="addTemplate" parameterType="cec.jiutian.ecs.po.TemplatePO">
        insert into ecs_template(
        <if test="gid != null and gid != ''">gid,</if>
        <if test="createTs != null">create_ts,</if>
        <if test="createUser != null and createUser != ''">create_user,</if>
        <if test="lastTrxnTs != null">last_trxn_ts,</if>
        <if test="lastTrxnUser != null and lastTrxnUser != ''">last_trxn_user,</if>
        <if test="templateName != null and templateName != ''">template_name,</if>
        <if test="templateDesc != null and templateDesc != ''">template_desc,</if>
        )values(
        <if test="gid != null and gid != ''">#{gid},</if>
        <if test="createTs != null and createTs != ''">#{createTs},</if>
        <if test="createUser != null and createUser != ''">#{createUser},</if>
        <if test="lastTrxnTs != null and lastTrxnTs != ''">#{lastTrxnTs},</if>
        <if test="lastTrxnUser != null and lastTrxnUser != ''">#{lastTrxnUser},</if>
        <if test="templateName != null and templateName != ''">#{templateName},</if>
        <if test="templateDesc != null and templateDesc != ''">#{templateDesc},</if>
        )
    </insert>

    <update id="updateTemplate" parameterType="cec.jiutian.ecs.po.TemplatePO">
        update ecs_template
        <set>
            <if test="templateName != null and templateName != ''">template_name = #{templateName},</if>
            <if test="templateDesc != null and templateDesc != ''">template_desc = #{templateDesc},</if>
            <if test="lastTrxnTs != null and lastTrxnTs != ''">last_trxn_ts = #{lastTrxnTs},</if>
            <if test="lastTrxnUser != null and lastTrxnUser != ''">last_trxn_user = #{lastTrxnUser},</if>
        </set>
        where gid = #{gid}
    </update>

    <delete id="batchDelete">
        delete from ecs_template where gid in
        <foreach collection="list" item="gid" open="(" separator="," close=")">
            #{gid}
        </foreach>
    </delete>
</mapper>
