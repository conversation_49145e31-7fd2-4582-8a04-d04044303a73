<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cec.jiutian.ecs.mapper.TemplateAttributeMapper">
    <resultMap id="BasicDataResult" type="cec.jiutian.ecs.po.TemplateAttributePO">
        <id column="GID" property="gid" jdbcType="VARCHAR"/>
        <result column="CREATE_TS" property="createTs" jdbcType="TIMESTAMP"/>
        <result column="CREATE_USER" property="createUser" jdbcType="VARCHAR"/>
        <result column="LAST_TRXN_TS" property="lastTrxnTs" jdbcType="TIMESTAMP"/>
        <result column="LAST_TRXN_USER" property="lastTrxnUser" jdbcType="VARCHAR"/>
        <result column="TEMPLATE_GID" property="templateGid" jdbcType="VARCHAR"/>
        <result column="TEMPLATE_NAME" property="templateName" jdbcType="VARCHAR"/>
        <result column="ATTRIBUTE_NAME" property="attributeName" jdbcType="VARCHAR"/>
        <result column="CONNECT_ATTRIBUTE" property="connectAttribute" jdbcType="VARCHAR"/>
        <result column="DEFAULT_VALUE" property="defaultValue" jdbcType="VARCHAR"/>
        <result column="ORDER_NUMBER" property="orderNumber" jdbcType="BIGINT"/>
        <result column="lst_evnt_cmnt" property="lastEventComment"/>
    </resultMap>

    <select id="getTemplateAttributeList" resultMap="BasicDataResult">
        select t.* from ecs_template_attribute t
        where t.gid is not null
        <if test="templateGid != null and templateGid != ''">
            and t.TEMPLATE_GID like concat('%',#{templateGid}::text,'%')
        </if>
        <if test="attributeName != null and attributeName != ''">
            and t.ATTRIBUTE_NAME like concat('%',#{attributeName}::text,'%')
        </if>
        <if test="connectAttribute != null and connectAttribute != ''">
            and t.CONNECT_ATTRIBUTE like concat('%',#{connectAttribute}::text,'%')
        </if>
        <if test="defaultValue != null and defaultValue != ''">
            and t.DEFAULT_VALUE like concat('%',#{defaultValue}::text,'%')
        </if>
        order by t.CREATE_TS desc
    </select>

    <insert id="addTemplateAttribute" parameterType="cec.jiutian.ecs.po.TemplateAttributePO">
        insert into ecs_template_attribute(
        <if test="gid != null and gid != ''">gid,</if>
        <if test="createTs != null">create_ts,</if>
        <if test="createUser != null and createUser != ''">create_user,</if>
        <if test="lastTrxnTs != null">last_trxn_ts,</if>
        <if test="lastTrxnUser != null and lastTrxnUser != ''">last_trxn_user,</if>
        <if test="templateGid != null and templateGid != ''">TEMPLATE_GID,</if>
        <if test="attributeName != null and attributeName != ''">ATTRIBUTE_NAME,</if>
        <if test="connectAttribute != null and connectAttribute != ''">CONNECT_ATTRIBUTE,</if>
        <if test="defaultValue != null and defaultValue != ''">DEFAULT_VALUE,</if>
        )values(
        <if test="gid != null and gid != ''">#{gid},</if>
        <if test="createTs != null and createTs != ''">#{createTs},</if>
        <if test="createUser != null and createUser != ''">#{createUser},</if>
        <if test="lastTrxnTs != null and lastTrxnTs != ''">#{lastTrxnTs},</if>
        <if test="lastTrxnUser != null and lastTrxnUser != ''">#{lastTrxnUser},</if>
        <if test="templateGid != null and templateGid != ''">#{templateGid},</if>
        <if test="attributeName != null and attributeName != ''">#{attributeName},</if>
        <if test="connectAttribute != null and connectAttribute != ''">#{connectAttribute},</if>
        <if test="defaultValue != null and defaultValue != ''">#{defaultValue},</if>
        )
    </insert>

    <select id="selectTemplateAttributeByName" resultMap="BasicDataResult">
        select * from ecs_template_attribute t where t.ATTRIBUTE_NAME = #{attributeName} and t.TEMPLATE_GID = #{templateGid}
    </select>

    <update id="updateTemplateAttribute" parameterType="cec.jiutian.ecs.po.TemplateAttributePO">
        update ecs_template_attribute
        <set>
            <if test="templateGid != null and templateGid != ''">TEMPLATE_GID = #{templateGid},</if>
            <if test="attributeName != null and attributeName != ''">ATTRIBUTE_NAME = #{attributeName},</if>
            <if test="connectAttribute != null and connectAttribute !=''">CONNECT_ATTRIBUTE = #{connectAttribute},</if>
            <if test="defaultValue != null and defaultValue != ''">DEFAULT_VALUE = #{defaultValue},</if>
            <if test="lastTrxnTs != null and lastTrxnTs != ''">last_trxn_ts = #{lastTrxnTs},</if>
            <if test="lastTrxnUser != null and lastTrxnUser != ''">last_trxn_user = #{lastTrxnUser},</if>
        </set>
        where gid = #{gid}
    </update>

    <delete id="batchDelete">
        delete from ecs_template_attribute where gid in
        <foreach collection="list" item="gid" open="(" separator="," close=")">
            #{gid}
        </foreach>
    </delete>

    <delete id="deleteByTemplateGid">
        delete from ecs_template_attribute where TEMPLATE_GID  = #{templateGid}
    </delete>
</mapper>
