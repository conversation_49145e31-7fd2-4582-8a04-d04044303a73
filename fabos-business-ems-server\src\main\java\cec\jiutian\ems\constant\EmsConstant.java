package cec.jiutian.ems.constant;

import java.util.HashMap;

public interface EmsConstant {
    class timeType{
        public static final String YEAR = "year";
        public static final String MONTH = "month";
        public static final String WEEK = "week";
        public static final String DAY = "day";
    }

    HashMap<String, String > energyType  = new HashMap<String, String>(){{
        put("waterConsumption","水");
        put("electricityConsumption","电");
        put("gasConsumption","气");
        put("CarbonEmission","碳排放");
    }};
}
