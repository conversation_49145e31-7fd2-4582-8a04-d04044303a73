package cec.jiutian.ems.controller;

import cec.jiutian.core.base.BaseController;
import cec.jiutian.core.comn.util.BeanValidators;
import cec.jiutian.core.comn.util.StringUtils;
import cec.jiutian.ems.dto.*;
import cec.jiutian.ems.service.business.AlarmSettingBizService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import javax.ws.rs.core.Response;

@RestController
public class AlarmSettingController extends BaseController {
    private final AlarmSettingBizService alarmSettingBizService;

    public AlarmSettingController(AlarmSettingBizService alarmSettingBizService) {
        this.alarmSettingBizService = alarmSettingBizService;
    }

    @PostMapping("/getAlarmSettingList")
    public Response getAlarmSettingList(@RequestBody GetAlarmSettingDTO getAlarmSettingDTO) {
        StringUtils.doTrim(getAlarmSettingDTO);
        BeanValidators.validateWithException(validator, getAlarmSettingDTO);
        Object result = alarmSettingBizService.getAlarmSettingList(getAlarmSettingDTO);
        return respSuccessResult(result, "查询成功");
    }

    @PostMapping("/getAlarmSettingParameterRelationList")
    public Response getAlarmSettingParameterRelationList(@RequestBody GetAlarmSettingParameterRelationDTO getAlarmSettingParameterRelationDTO) {
        StringUtils.doTrim(getAlarmSettingParameterRelationDTO);
        BeanValidators.validateWithException(validator, getAlarmSettingParameterRelationDTO);
        Object result = alarmSettingBizService.getAlarmSettingParameterRelationList(getAlarmSettingParameterRelationDTO);
        return respSuccessResult(result, "查询成功");
    }

    @PostMapping("/alarmSetting/create")
    public Response createAlarmSetting(@RequestBody AlarmSettingCreateDTO alarmSettingCreateDTO) {
        BeanValidators.validateWithException(validator, alarmSettingCreateDTO);
        Boolean result = alarmSettingBizService.createAlarmSetting(alarmSettingCreateDTO);
        return respResult(result, "操作成功", "操作失败");
    }

    @PostMapping("/alarmSetting/update")
    public Response updateAlarmSetting(@RequestBody AlarmSettingUpdateDTO alarmSettingUpdateDTO) {
        BeanValidators.validateWithException(validator, alarmSettingUpdateDTO);
        Boolean result = alarmSettingBizService.updateAlarmSetting(alarmSettingUpdateDTO);
        return respResult(result, "操作成功", "操作失败");
    }

    @PostMapping("/alarmSetting/delete")
    public Response deleteAlarmSetting(@RequestBody AlarmSettingDeleteDTO alarmSettingDeleteDTO) {
        BeanValidators.validateWithException(validator, alarmSettingDeleteDTO);
        Boolean result = alarmSettingBizService.deleteAlarmSetting(alarmSettingDeleteDTO);
        return respResult(result, "操作成功", "操作失败");
    }
}
