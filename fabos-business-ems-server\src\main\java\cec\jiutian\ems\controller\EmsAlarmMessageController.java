package cec.jiutian.ems.controller;

import cec.jiutian.core.base.BaseController;
import cec.jiutian.core.comn.util.BeanValidators;
import cec.jiutian.core.comn.util.StringUtils;
import cec.jiutian.ems.dto.GetEmsAlarmMessageHistoryDTO;
import cec.jiutian.ems.service.business.EmsAlarmMessageBizService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.ws.rs.core.Response;
import java.text.ParseException;

@RestController
public class EmsAlarmMessageController extends BaseController {
    private final EmsAlarmMessageBizService emsAlarmMessageBizService;

    public EmsAlarmMessageController(EmsAlarmMessageBizService emsAlarmMessageBizService) {
        this.emsAlarmMessageBizService = emsAlarmMessageBizService;
    }

    @PostMapping("/emsAlarmMessage/create")
    public Response createAlarmMessage() {
        Boolean result = emsAlarmMessageBizService.autoAlarmTrigger();
        return respResult(result, "操作成功", "操作失败");
    }

    @PostMapping("/getEmsAlarmMessageHistoryList")
    public Response getEmsAlarmMessageHistoryList(@RequestBody GetEmsAlarmMessageHistoryDTO getEmsAlarmMessageHistoryDTO) throws ParseException {
        StringUtils.doTrim(getEmsAlarmMessageHistoryDTO);
        BeanValidators.validateWithException(validator, getEmsAlarmMessageHistoryDTO);
        Object result = emsAlarmMessageBizService.getEmsAlarmMessageHistoryList(getEmsAlarmMessageHistoryDTO);
        return respSuccessResult(result, "查询成功");
    }

    @PostMapping("/getEmsAlarmMessageList")
    public Response getEmsAlarmMessageList(@RequestBody GetEmsAlarmMessageHistoryDTO getEmsAlarmMessageHistoryDTO) throws ParseException {
        StringUtils.doTrim(getEmsAlarmMessageHistoryDTO);
        BeanValidators.validateWithException(validator, getEmsAlarmMessageHistoryDTO);
        Object result = emsAlarmMessageBizService.getEmsAlarmMessageList(getEmsAlarmMessageHistoryDTO);
        return respSuccessResult(result, "查询成功");
    }

    @PostMapping("/emsAlarmMessage/update")
    public Response updateAlarmMessage(@RequestParam(required = true) String gid){
        Boolean result = emsAlarmMessageBizService.updateAlarmMessage(gid);
        return respResult(result, "操作成功", "操作失败");
    }

    @PostMapping("/getEmsAlarmMessageHistoryByEventAggregationList")
    public Response getEmsAlarmMessageHistoryByEventAggregationList(@RequestBody GetEmsAlarmMessageHistoryDTO getEmsAlarmMessageHistoryDTO) throws ParseException {
        StringUtils.doTrim(getEmsAlarmMessageHistoryDTO);
        BeanValidators.validateWithException(validator, getEmsAlarmMessageHistoryDTO);
        Object result = emsAlarmMessageBizService.getEmsAlarmMessageHistoryByEventAggregationList(getEmsAlarmMessageHistoryDTO);
        return respSuccessResult(result, "查询成功");
    }
}
