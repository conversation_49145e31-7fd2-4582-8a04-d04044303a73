package cec.jiutian.ems.controller;

import cec.jiutian.core.base.BaseController;
import cec.jiutian.ems.dto.CapacityDataCreateDTO;
import cec.jiutian.ems.dto.CapacityDataGetDTO;
import cec.jiutian.ems.dto.CapacityDataOutDTO;
import cec.jiutian.ems.dto.CapacityDataUpdateOrDelDTO;
import cec.jiutian.ems.po.EmsCapacityDataPO;
import cec.jiutian.ems.service.business.EmsCapacityDataBizService;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.ws.rs.core.Response;
import java.util.ArrayList;
import java.util.List;

/**

 *
 * <AUTHOR>
 * @since 2023/6/19 creation
 */
@RestController
@RequestMapping("capacity")
@AllArgsConstructor
public class EmsCapacityDataController extends BaseController {

    private final EmsCapacityDataBizService emsCapacityDataBizService;


    @RequestMapping("/insertCapacityData")
    public Response insertCapacityData(@RequestBody CapacityDataCreateDTO capacityDataCreateDTO)
    {

        Object result=   emsCapacityDataBizService.insertCapacityData(capacityDataCreateDTO);
        return respSuccessResult(result,"产能数据插入成功!");
    }


    // 获取区域产能
    @RequestMapping("/getRegionCapacityByRegion")
    public Response getRegionCapacityByRegionReturnTable(@RequestBody CapacityDataGetDTO capacityDataGetDTO)
    {
        CapacityDataOutDTO result=emsCapacityDataBizService.getRegionCapacityByRegion(capacityDataGetDTO);
        return respSuccessResult(result,"产能数据获取成功!");
    }

    @RequestMapping("/getUpdateData")
    public Response getUpdateData(@RequestBody CapacityDataGetDTO capacityDataGetDTO)
    {
        List<EmsCapacityDataPO> result=emsCapacityDataBizService.getUpdateData(capacityDataGetDTO);
        return respSuccessResult(result,"产能数据获取成功!");
    }

    @RequestMapping("/updateEmsCapacityDataPOs")
    public Response updateEmsCapacityDataPOs(@RequestBody CapacityDataUpdateOrDelDTO updateData)
    {
        Integer result=emsCapacityDataBizService.updateEmsCapacityDataPOs(updateData.getUpdateDatas());
        return respSuccessResult(result,"产能数据修改成功!");
    }


    @RequestMapping("/deleteEmsCapacityDataPOs")
    public Response deleteEmsCapacityDataPOs(@RequestBody CapacityDataUpdateOrDelDTO deleteDatas)
    {
        Integer result=emsCapacityDataBizService.deleteEmsCapacityDataPOs(deleteDatas.getUpdateDatas());
        return respSuccessResult(result,"产能数据删除成功!");
    }
    @RequestMapping("/deleteEmsCapacityDataPO")
    public Response deleteEmsCapacityDataPO(@RequestBody EmsCapacityDataPO deleteDatas)
    {

        ArrayList<EmsCapacityDataPO> emsCapacityDataPOS = new ArrayList<>();
        emsCapacityDataPOS.add(deleteDatas);
        CapacityDataUpdateOrDelDTO capacityDataUpdateOrDelDTO = new CapacityDataUpdateOrDelDTO().setUpdateDatas(emsCapacityDataPOS);
        Integer result=emsCapacityDataBizService.deleteEmsCapacityDataPOs(capacityDataUpdateOrDelDTO.getUpdateDatas());
        return respSuccessResult(result,"产能数据删除成功!");
    }





}
