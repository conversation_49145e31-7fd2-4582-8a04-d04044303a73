package cec.jiutian.ems.controller;

import cec.jiutian.core.base.BaseController;
import cec.jiutian.ems.query.dto.CarbonEmissionQueryDTO;
import cec.jiutian.ems.service.business.EmsCarbonManageBizService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.ws.rs.core.Response;

/**
 * 碳管理
 *
 * <AUTHOR> CodeGenerator
 * @date : 2023-6-15
 */
@Api(tags = "EmsCarbonManageController")
@RestController
@Slf4j
@AllArgsConstructor
public class EmsCarbonManageController extends BaseController {
    private final EmsCarbonManageBizService emsCarbonManageBizService;

    @PostMapping("/getCarbonEmission")
    @ApiOperation(value = "碳排放总览", notes = "相关表：ems_used_data_month_summary")
    public Response getCarbonEmission(@RequestBody CarbonEmissionQueryDTO queryDTO) {
        Object result = emsCarbonManageBizService.getCarbonEmission(queryDTO);
        return respSuccessResult(result, "操作成功");
    }

    @PostMapping("/getCarbonEmissionByMonth")
    @ApiOperation(value = "碳排放月度统计图", notes = "相关表：ems_used_data_day_summary")
    public Response getCarbonEmissionByMonth(@RequestBody CarbonEmissionQueryDTO queryDTO) {
        Object result = emsCarbonManageBizService.getCarbonEmissionByMonth(queryDTO);
        return respSuccessResult(result, "操作成功");
    }

}
