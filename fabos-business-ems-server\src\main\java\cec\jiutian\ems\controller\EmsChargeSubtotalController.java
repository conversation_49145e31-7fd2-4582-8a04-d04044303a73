package cec.jiutian.ems.controller;

import cec.jiutian.core.base.BaseController;
import cec.jiutian.core.comn.util.BeanValidators;
import cec.jiutian.core.comn.util.StringUtils;
import cec.jiutian.ems.dto.GetAlarmSettingDTO;
import cec.jiutian.ems.dto.GetTotalChargeDTO;
import cec.jiutian.ems.service.business.EmsChargeSubtotalBizService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.ws.rs.core.Response;

@RestController
public class EmsChargeSubtotalController extends BaseController {
    private final EmsChargeSubtotalBizService emsChargeSubtotalBizService;

    public EmsChargeSubtotalController(EmsChargeSubtotalBizService emsChargeSubtotalBizService) {
        this.emsChargeSubtotalBizService = emsChargeSubtotalBizService;
    }

    /**
     * 电费 electricityConsumption
     * @param getTotalChargeDTO
     * @return
     */
    @PostMapping("/getTotalChargeList")
    public Response getTotalChargeList(@RequestBody GetTotalChargeDTO getTotalChargeDTO) {
        StringUtils.doTrim(getTotalChargeDTO);
        BeanValidators.validateWithException(validator, getTotalChargeDTO);
        Object result = emsChargeSubtotalBizService.getTotalChargeList(getTotalChargeDTO);
        return respSuccessResult(result, "查询成功");
    }

    /**
     * 能源费用统计
     * 电费、水费、天然气统计
     * @param getTotalChargeDTO
     * @return
     */
    @PostMapping("/getEmsChargeSubtotalForBarList")
    public Response getEmsChargeSubtotalForBarList(@RequestBody GetTotalChargeDTO getTotalChargeDTO) {
        StringUtils.doTrim(getTotalChargeDTO);
        BeanValidators.validateWithException(validator, getTotalChargeDTO);
        Object result = emsChargeSubtotalBizService.getEmsChargeSubtotalForBarList(getTotalChargeDTO);
        return respSuccessResult(result, "查询成功");
    }

    @PostMapping("/getEmsChargeSubtotalForPieList")
    public Response getEmsChargeSubtotalForPieList(@RequestBody GetTotalChargeDTO getTotalChargeDTO) {
        StringUtils.doTrim(getTotalChargeDTO);
        BeanValidators.validateWithException(validator, getTotalChargeDTO);
        Object result = emsChargeSubtotalBizService.getEmsChargeSubtotalForPieList(getTotalChargeDTO);
        return respSuccessResult(result, "查询成功");
    }


    /**
     * 能源费用统计
     * 表格
     * @param getTotalChargeDTO
     * @return
     */
    @PostMapping("/getEmsChargeSubtotalForTableList")
    public Response getEmsChargeSubtotalForTableList(@RequestBody GetTotalChargeDTO getTotalChargeDTO) {
        StringUtils.doTrim(getTotalChargeDTO);
        BeanValidators.validateWithException(validator, getTotalChargeDTO);
        Object result = emsChargeSubtotalBizService.getEmsChargeSubtotalForTableList(getTotalChargeDTO);
        return respSuccessResult(result, "查询成功");
    }
}
