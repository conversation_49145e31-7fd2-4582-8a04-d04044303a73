package cec.jiutian.ems.controller;

import cec.jiutian.core.base.BaseController;
import cec.jiutian.ems.query.dto.EmsConfigQueryDTO;
import cec.jiutian.ems.service.business.EmsConfigBizService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.ws.rs.core.Response;

/**
 * ems_config;配置表
 *
 * <AUTHOR> CodeGenerator
 * @date : 2023-6-21
 */
@Api(tags = "Ems ConfigAPI")
@RestController
@Slf4j
@AllArgsConstructor
public class EmsConfigController extends BaseController {
    private final EmsConfigBizService emsConfigBizService;

    @PostMapping("/getEmsConfig")
    @ApiOperation(value = "ems配置查询", notes = "相关表：ems_config")
    public Response getConfig(@RequestBody EmsConfigQueryDTO queryDTO) {
        Object result = emsConfigBizService.getByConditions(queryDTO);
        return respSuccessResult(result, "操作成功");
    }

}
