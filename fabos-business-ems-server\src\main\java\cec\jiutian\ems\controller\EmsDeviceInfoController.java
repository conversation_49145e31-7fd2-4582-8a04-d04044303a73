
package cec.jiutian.ems.controller;

import cec.jiutian.core.base.BaseController;
import cec.jiutian.ems.service.business.EmsDeviceInfoBizService;
import io.swagger.annotations.Api;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;

/**
 * Ems Device Info;设备信息表
 * <AUTHOR> CodeGenerator
 * @date : 2023-5-29
 */
@Api(tags = "Ems Device InfoAPI")
@RestController
@Slf4j
@AllArgsConstructor
public class EmsDeviceInfoController extends BaseController {
    private final EmsDeviceInfoBizService emsDeviceInfoBizService;


}
