package cec.jiutian.ems.controller;

import cec.jiutian.core.base.BaseController;
import cec.jiutian.core.comn.util.BeanValidators;
import cec.jiutian.ems.dto.IndexSettingCreateDTO;
import cec.jiutian.ems.query.dto.EnergyConsumptionIndexQueryDTO;
import cec.jiutian.ems.service.business.EmsIndexManagementBizService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.ws.rs.core.Response;

/**
 * 指标管理
 *
 * <AUTHOR> CodeGenerator
 * @date : 2023-6-19
 */
@Api(tags = "Ems Index ManagementAPI")
@RestController
@Slf4j
@AllArgsConstructor
public class EmsIndexManagementController extends BaseController {
    private final EmsIndexManagementBizService emsIndexManagementBizService;

    @PostMapping("/indexSetting/create")
    @ApiOperation(value = "指标设置创建", notes = "相关表：ems_index_setting")
    public Response createIndexSetting(@RequestBody IndexSettingCreateDTO opDTO) {
        BeanValidators.validateWithException(validator, opDTO);
        emsIndexManagementBizService.createIndex(opDTO);
        return respSuccessResult("操作成功");
    }

    @PostMapping("/indexSetting/update")
    @ApiOperation(value = "指标设置更新", notes = "相关表：ems_index_setting")
    public Response updateIndexSetting(@RequestBody IndexSettingCreateDTO opDTO) {
        BeanValidators.validateWithException(validator, opDTO);
        emsIndexManagementBizService.updateIndex(opDTO);
        return respSuccessResult("操作成功");
    }

    @PostMapping("/getIndexSetting")
    @ApiOperation(value = "指标设置查询", notes = "相关表：ems_index_setting")
    public Response getIndexSetting(@RequestBody EnergyConsumptionIndexQueryDTO queryDTO) {
        BeanValidators.validateWithException(validator, queryDTO);
        Object result = emsIndexManagementBizService.getSetting(queryDTO);
        return respSuccessResult(result, "操作成功");
    }

    @PostMapping("/getEnergyConsumptionIndex")
    @ApiOperation(value = "能耗指标主页面查询", notes = "相关表：ems_used_data_month_summary, ems_index_setting")
    public Response getEnergyConsumptionIndex(@RequestBody EnergyConsumptionIndexQueryDTO queryDTO) {
        BeanValidators.validateWithException(validator, queryDTO);
        Object result = emsIndexManagementBizService.getEnergyConsumptionIndex(queryDTO);
        return respSuccessResult(result, "操作成功");
    }

}
