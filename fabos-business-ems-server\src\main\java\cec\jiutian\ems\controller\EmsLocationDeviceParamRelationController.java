
package cec.jiutian.ems.controller;

import cec.jiutian.core.base.BaseController;
import cec.jiutian.ems.query.dto.LocationDeviceParamRelationQueryDTO;
import cec.jiutian.ems.service.business.EmsLocationDeviceParamRelationBizService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.ws.rs.core.Response;

/**
 * EMS Location Device Param Relation;位置树与测点关系
 * <AUTHOR> CodeGenerator
 * @date : 2023-5-29
 */
@Api(tags = "EMS Location Device Param RelationAPI")
@RestController
@Slf4j
@AllArgsConstructor
public class EmsLocationDeviceParamRelationController extends BaseController {
    private final EmsLocationDeviceParamRelationBizService emsLocationDeviceParamRelationBizService;


    @PostMapping("/locationDeviceParamRelation/getRelationshipList")
    @ApiOperation("查询")
    public Response getRelationshipList(@RequestBody LocationDeviceParamRelationQueryDTO param) {
        return respSuccessResult(emsLocationDeviceParamRelationBizService.getRelationshipList(param), "success");
    }

    @PostMapping("/locationDeviceParamRelation/bindToLocation")
    @ApiOperation("创建/绑定")
    public Response bindToLocation(@RequestBody LocationDeviceParamRelationQueryDTO param) {
        emsLocationDeviceParamRelationBizService.bindToLocation(param);
        return respSuccessResult("success");
    }

    @PostMapping("/locationDeviceParamRelation/unbindFromLocation")
    @ApiOperation("移除/解绑")
    public Response unbindFromLocation(@RequestBody LocationDeviceParamRelationQueryDTO param) {
        emsLocationDeviceParamRelationBizService.unbindFromLocation(param);
        return respSuccessResult("success");
    }

    @PostMapping("/locationDeviceParamRelation/unbindAllFromLocation")
    @ApiOperation("全部移除/解绑")
    public Response unbindAllFromLocation(@RequestBody LocationDeviceParamRelationQueryDTO param) {
        emsLocationDeviceParamRelationBizService.unbindAllFromLocation(param);
        return respSuccessResult("success");
    }

}
