package cec.jiutian.ems.controller;

import cec.jiutian.core.base.BaseController;
import cec.jiutian.ems.dto.GetElectricTopologyDTO;
import cec.jiutian.ems.dto.GetWaterTopologyDTO;
import cec.jiutian.ems.dto.LocationTreeModDTO;
import cec.jiutian.ems.po.EmsLocationTreePO;
import cec.jiutian.ems.query.dto.LocationTreeQueryDTO;
import cec.jiutian.ems.service.business.EmsLocationTreeBizService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.ws.rs.core.Response;

/**
 * EMS location tree;位置树表
 * <AUTHOR> CodeGenerator
 * @date : 2023-5-29
 */
@Api(tags = "EMS location treeAPI")
@RestController
@Slf4j
@AllArgsConstructor
public class EmsLocationTreeController extends BaseController {
    private final EmsLocationTreeBizService emsLocationTreeBizService;

    @PostMapping("/locationTree/getMapByLocationType")
    @ApiOperation("根据类型查询拓扑图")
    public Response getMapByLocationType(@RequestBody GetWaterTopologyDTO getWaterTopologyDTO) {
        return respSuccessResult(emsLocationTreeBizService.getMapByLocationType( getWaterTopologyDTO), "success");
    }

    @PostMapping("/locationTree/getElectricTopology")
    @ApiOperation("查询电力拓扑图")
    public Response getElectricTopology(@RequestBody GetElectricTopologyDTO getElectricTopologyDTO) {
        return respSuccessResult(emsLocationTreeBizService.getElectricTopology(getElectricTopologyDTO), "success");
    }

    @PostMapping("/locationTree/getAllTopLocationsByTypeAndCode")
    @ApiOperation("查询顶层")
    public Response getAllTopLocationsByTypeAndCode(@RequestBody LocationTreeQueryDTO param) {
        return respSuccessResult(emsLocationTreeBizService.getAllTopLocationsByTypeAndCode(param), "success");
    }

    @PostMapping("/locationTree/getLocationsByParentGid")
    @ApiOperation("按父节点查询")
    public Response getLocationsByParentGid(@RequestBody LocationTreeQueryDTO param) {
        return respSuccessResult(emsLocationTreeBizService.getLocationsByParentGid(param), "success");
    }

    @PostMapping("/locationTree/getLocationsByTopParentGid")
    @ApiOperation("按顶级父节点查询")
    public Response getLocationsByTopParentGid(@RequestBody LocationTreeQueryDTO param) {
        return respSuccessResult(emsLocationTreeBizService.getLocationsByTopParentGid(param), "success");
    }

    @PostMapping("/locationTree/getLocationsByName")
    @ApiOperation("按名称查询")
    public Response getLocationsByName(@RequestBody LocationTreeQueryDTO param) {
        return respSuccessResult(emsLocationTreeBizService.getLocationsByName(param), "success");
    }

    @PostMapping("/locationTree/saveLocationTree")
    @ApiOperation("创建")
    public Response saveLocationTree(@RequestBody EmsLocationTreePO param) {
        emsLocationTreeBizService.saveLocationTree(param);
        return respSuccessResult("success");
    }

    @PostMapping("/locationTree/updateLocationTreeName")
    @ApiOperation("更新名称")
    public Response updateLocationTreeName(@RequestBody LocationTreeModDTO param) {
        emsLocationTreeBizService.updateLocationTreeName(param);
        return respSuccessResult("success");
    }

    @PostMapping("/locationTree/updateSortSeq")
    @ApiOperation("变更顺序")
    public Response updateSortSeq(@RequestBody LocationTreeModDTO param) {
        emsLocationTreeBizService.updateSortSeq(param);
        return respSuccessResult("success");
    }

    @PostMapping("/locationTree/removeLocationTree")
    @ApiOperation("移除")
    public Response removeLocationTree(@RequestBody LocationTreeModDTO param) {
        emsLocationTreeBizService.removeLocationTree(param);
        return respSuccessResult("success");
    }

    @PostMapping("/locationTree/getTreeChild")
    @ApiOperation("查询顶层")
    public Response getTreeChild(@RequestBody LocationTreeQueryDTO param) {
        return respSuccessResult(emsLocationTreeBizService.getTreeChild(param), "success");
    }

    @PostMapping("/getLocationType")
    @ApiOperation(value = "节点类型下拉列表", notes = "相关表：ems_location_tree")
    public Response getLocationType() {
        Object result= emsLocationTreeBizService.getLocationType();
        return respSuccessResult(result,"操作成功");
    }

    @PostMapping("/getEnergyType")
    @ApiOperation(value = "能源类型下拉列表", notes = "相关表：ems_location_tree")
    public Response getEnergyType() {
        Object result= emsLocationTreeBizService.getEnergyType();
        return respSuccessResult(result,"操作成功");
    }
}
