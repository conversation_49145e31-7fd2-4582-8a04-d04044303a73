
package cec.jiutian.ems.controller;

import cec.jiutian.core.base.BaseController;
import cec.jiutian.core.comn.util.BeanValidators;
import cec.jiutian.core.comn.util.StringUtils;
import cec.jiutian.ems.dto.GetAlarmSettingDTO;
import cec.jiutian.ems.dto.GetDeviceParamDTO;
import cec.jiutian.ems.po.EmsParamInfoPO;
import cec.jiutian.ems.query.dto.ParamInfoQueryDTO;
import cec.jiutian.ems.service.business.EmsParamInfoBizService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.ws.rs.core.Response;

/**
 * Ems Param Info;模型参数信息表
 * <AUTHOR> CodeGenerator
 * @date : 2023-5-29
 */
@Api(tags = "Ems Param InfoAPI")
@RestController
@Slf4j
@AllArgsConstructor
public class EmsParamInfoController extends BaseController {
    private final EmsParamInfoBizService emsParamInfoBizService;


    @PostMapping("/paramInfo/getParamInfoList")
    @ApiOperation("查询参数信息")
    public Response getLocationsByName(@RequestBody ParamInfoQueryDTO param) {
        return respSuccessResult(emsParamInfoBizService.getParamInfoList(param), "success");
    }

    @PostMapping("/paramInfo/saveParamInfo")
    @ApiOperation("创建")
    public Response saveParamInfo(@RequestBody EmsParamInfoPO param) {
        emsParamInfoBizService.saveParamInfo(param);
        return respSuccessResult("success");
    }

    @PostMapping("/paramInfo/updateParamInfo")
    @ApiOperation("修改")
    public Response updateParamInfo(@RequestBody EmsParamInfoPO param) {
        emsParamInfoBizService.updateParamInfo(param);
        return respSuccessResult("success");
    }

    @PostMapping("/paramInfo/removeParamInfo")
    @ApiOperation("移除")
    public Response removeParamInfo(@RequestBody EmsParamInfoPO param) {
        emsParamInfoBizService.removeParamInfo(param);
        return respSuccessResult("success");
    }

    @PostMapping("/getDeviceParamList")
    public Response getDeviceParamList(@RequestBody GetDeviceParamDTO getDeviceParamDTO) {
        StringUtils.doTrim(getDeviceParamDTO);
        BeanValidators.validateWithException(validator, getDeviceParamDTO);
        Object result = emsParamInfoBizService.getDeviceParamList(getDeviceParamDTO);
        return respSuccessResult(result, "查询成功");
    }

}
