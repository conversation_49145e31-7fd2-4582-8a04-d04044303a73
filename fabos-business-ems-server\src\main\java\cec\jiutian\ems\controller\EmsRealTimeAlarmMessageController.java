package cec.jiutian.ems.controller;


import cec.jiutian.core.base.BaseController;
import cec.jiutian.ems.service.business.EmsRealTimeAlarmMessageBizService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.ws.rs.core.Response;
import java.text.ParseException;

@RestController
public class EmsRealTimeAlarmMessageController extends BaseController {
    private final EmsRealTimeAlarmMessageBizService emsRealTimeAlarmMessageBizService;

    public EmsRealTimeAlarmMessageController(EmsRealTimeAlarmMessageBizService emsRealTimeAlarmMessageBizService) {
        this.emsRealTimeAlarmMessageBizService = emsRealTimeAlarmMessageBizService;
    }

    @PostMapping("/getEmsAlarmLevelSummary")
    public Response getEmsAlarmLevelSummary() throws ParseException {
        Object result = emsRealTimeAlarmMessageBizService.getEmsAlarmLevelSummary();
        return respSuccessResult(result, "查询成功");
    }


    @PostMapping("/getEmsAlarmClassSummary")
    public Response getEmsAlarmClassSummary() throws ParseException {
        Object result = emsRealTimeAlarmMessageBizService.getEmsAlarmClassSummary();
        return respSuccessResult(result, "查询成功");
    }

    @PostMapping("/getEmsAlarmFactorySummary")
    public Response getEmsAlarmFactorySummary() throws ParseException {
        Object result = emsRealTimeAlarmMessageBizService.getEmsAlarmFactorySummary();
        return respSuccessResult(result, "查询成功");
    }
}
