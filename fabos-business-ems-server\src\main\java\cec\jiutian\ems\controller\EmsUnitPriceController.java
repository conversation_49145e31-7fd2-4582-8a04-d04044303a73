package cec.jiutian.ems.controller;

import cec.jiutian.core.base.BaseController;
import cec.jiutian.core.comn.util.BeanValidators;
import cec.jiutian.core.comn.util.StringUtils;
import cec.jiutian.ems.dto.*;
import cec.jiutian.ems.service.business.EmsUnitPriceBizService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.ws.rs.core.Response;

@RestController
public class EmsUnitPriceController extends BaseController {
    private final EmsUnitPriceBizService emsUnitPriceBizService;

    public EmsUnitPriceController(EmsUnitPriceBizService emsUnitPriceBizService) {
        this.emsUnitPriceBizService = emsUnitPriceBizService;
    }


    @PostMapping("/getEmsUnitPriceList")
    public Response getEmsUnitPriceList(@RequestBody GetEmsUnitPriceDTO getEmsUnitPriceDTO) {
        StringUtils.doTrim(getEmsUnitPriceDTO);
        BeanValidators.validateWithException(validator, getEmsUnitPriceDTO);
        Object result = emsUnitPriceBizService.getEmsUnitPriceList(getEmsUnitPriceDTO);
        return respSuccessResult(result, "查询成功");
    }

    @PostMapping("/emsUnitPrice/create")
    public Response create(@RequestBody EmsUnitPriceCreateDTO emsUnitPriceCreateDTO) {
        BeanValidators.validateWithException(validator, emsUnitPriceCreateDTO);
        Boolean result = emsUnitPriceBizService.create(emsUnitPriceCreateDTO);
        return respResult(result, "操作成功", "操作失败");
    }

    @PostMapping("/emsUnitPrice/update")
    public Response update(@RequestBody EmsUnitPriceUpdateDTO emsUnitPriceUpdateDTO) {
        BeanValidators.validateWithException(validator, emsUnitPriceUpdateDTO);
        Boolean result = emsUnitPriceBizService.update(emsUnitPriceUpdateDTO);
        return respResult(result, "操作成功", "操作失败");
    }

    @PostMapping("/emsUnitPrice/delete")
    public Response delete(@RequestBody EmsUnitPriceDeleteDTO emsUnitPriceDeleteDTO) {
        BeanValidators.validateWithException(validator, emsUnitPriceDeleteDTO);
        Boolean result = emsUnitPriceBizService.delete(emsUnitPriceDeleteDTO);
        return respResult(result, "操作成功", "操作失败");
    }

    @PostMapping("/getEmsUnitPriceLineList")
    public Response getEmsUnitPriceLineList(@RequestBody GetEmsUnitPriceDTO getEmsUnitPriceDTO) {
        StringUtils.doTrim(getEmsUnitPriceDTO);
        BeanValidators.validateWithException(validator, getEmsUnitPriceDTO);
        Object result = emsUnitPriceBizService.getEmsUnitPriceLineList(getEmsUnitPriceDTO);
        return respSuccessResult(result, "查询成功");
    }
}
