
package cec.jiutian.ems.controller;

import cec.jiutian.core.base.BaseController;
import cec.jiutian.ems.bo.MyBO;
import cec.jiutian.ems.constant.EmsConstant;
import cec.jiutian.ems.dto.GetEmsUseDataDTO;
import cec.jiutian.ems.dto.UsedDataOutDTO;
import cec.jiutian.ems.mapper.EmsChargeSubtotalMapper;
import cec.jiutian.ems.mapper.EmsUnitPriceMapper;
import cec.jiutian.ems.mapper.EmsUsedDataMapper;
import cec.jiutian.ems.mapper.EmsUsedDataParamMapper;
import cec.jiutian.ems.po.*;
import cec.jiutian.ems.query.dto.LocationTreeQueryDTO;
import cec.jiutian.ems.query.dto.UsedDataQueryDTO;
import cec.jiutian.ems.service.EmsUsedDataDaySummaryService;
import cec.jiutian.ems.service.EmsUsedDataMonthSummaryService;
import cec.jiutian.ems.service.business.EmsLocationTreeBizService;
import cec.jiutian.ems.service.business.EmsUsedDataBizService;
import cec.jiutian.ems.service.business.EmsUsedDataMonthSummaryBizService;
import cec.jiutian.ems.utils.Assert;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.persistence.Column;
import javax.ws.rs.core.Response;
import java.text.DecimalFormat;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * Ems Used Data;使用数据
 *
 * <AUTHOR> CodeGenerator
 * @date : 2023-5-29
 */
@Api(tags = "Ems Used DataAPI")
@RestController
@Slf4j
@AllArgsConstructor
public class EmsUsedDataController extends BaseController {
    private final EmsUsedDataBizService emsUsedDataBizService;
    private final EmsUsedDataDaySummaryService emsUsedDataDaySummaryService;
    private final EmsUsedDataMonthSummaryService emsUsedDataMonthSummaryService;
    private final EmsLocationTreeBizService emsLocationTreeBizService;



    @Autowired
    private EmsUnitPriceMapper mapper;
    @Autowired
    private EmsUsedDataMapper emsUsedDataMapper;
    @Autowired
    private EmsChargeSubtotalMapper emsChargeSubtotalMapper;
    @Autowired
    private EmsUsedDataMonthSummaryBizService emsUsedDataMonthSummaryBizService;

    @PostMapping("/insertMonth")
    public String in() {
        for (int i = 9; i <= 12; i++) {
            emsUsedDataMonthSummaryBizService.summarizeUsedData3(i);
        }
        return "ok";
    }

    @PostMapping("/cpd")
    public String calculatePricesDaily() {
        // 查询符合日期范围的尖峰平谷
        List<EmsUnitPricePO> currentDateUnitPrice = mapper.getCurrentDateUnitPrice();

        // 校验
        Assert.notNull("没有创建尖峰平谷时间段", currentDateUnitPrice);

        // 传递给 mapper 的数组
        List<MyBO> list = new ArrayList<>();

        // 单独处理为 NULL 的
        for (EmsUnitPricePO emsUnitPricePO : currentDateUnitPrice) {

            if(emsUnitPricePO.getSpecificTimePeriod() == null) {
                LocalDate currentDate = LocalDate.now().minusDays(1);
                EmsChargeSubtotalPO emsChargeSubtotalPO = new EmsChargeSubtotalPO();
                emsChargeSubtotalPO.setYear(currentDate.getYear());
                emsChargeSubtotalPO.setMonth(currentDate.getMonthValue());
                emsChargeSubtotalPO.setDay(currentDate.getDayOfMonth());
                emsChargeSubtotalPO.setDate(java.sql.Date.valueOf(currentDate));
                emsChargeSubtotalPO.setTotalUsage(0.0);
                emsChargeSubtotalPO.setUnitPrice(0.0); //单价
                emsChargeSubtotalPO.setTotalCharge(0.0);
                emsChargeSubtotalPO.setEnergyType("electricityConsumption");
                emsChargeSubtotalPO.setTimePeriod(emsUnitPricePO.getTimePeriod());
                // 暂不清楚用处
                emsChargeSubtotalPO.setLocationGid("102.1388.000000.000076");
                emsChargeSubtotalMapper.insert(emsChargeSubtotalPO);
            }
        }

        // 过滤掉为 NULL 的
        List<EmsUnitPricePO> collect = currentDateUnitPrice.stream().filter(item -> item.getSpecificTimePeriod() != null).collect(Collectors.toList());

        // 将数据库中的日期数据格式化成对象
        collect.forEach(item -> {
            if (item != null) {
                System.out.println();
                String[] split = item.getSpecificTimePeriod().split(",");
                for (String peakTime : split) {
                    String[] split1 = peakTime.split("-");
                    MyBO myBO = new MyBO();
                    myBO.setStart(split1[0]);
                    myBO.setEnd(split1[1]);
                    myBO.setDesc(item.getTimePeriod());
                    list.add(myBO);
                }
            }
        });

        // 分组求和
        List<GetEmsUseDataDTO> price = emsUsedDataBizService.getPrice(list);


        price.forEach(item -> {
            for (EmsUnitPricePO emsUnitPricePO : currentDateUnitPrice) {
                if (item.getDescription().equals(emsUnitPricePO.getTimePeriod())) {
                    // 第二天凌晨更新，记录昨天的日期
                    LocalDate currentDate = LocalDate.now().minusDays(1);

                    EmsChargeSubtotalPO emsChargeSubtotalPO = new EmsChargeSubtotalPO();
                    emsChargeSubtotalPO.setYear(currentDate.getYear());
                    emsChargeSubtotalPO.setMonth(currentDate.getMonthValue());
                    emsChargeSubtotalPO.setDay(currentDate.getDayOfMonth());
                    emsChargeSubtotalPO.setDate(java.sql.Date.valueOf(currentDate));
                    emsChargeSubtotalPO.setTotalUsage(item.getCount());
                    emsChargeSubtotalPO.setUnitPrice(emsUnitPricePO.getUnitPrice()); //单价

                    // 计算总价，保留两位小数
                    DecimalFormat decimalFormat = new DecimalFormat("#.##");
                    String formattedNumber = decimalFormat.format(item.getCount() * emsUnitPricePO.getUnitPrice());
                    double result = Double.parseDouble(formattedNumber);
                    // 修改总价
                    emsChargeSubtotalPO.setTotalCharge(result);
                    emsChargeSubtotalPO.setEnergyType(emsUnitPricePO.getEnergyTypeCode());
                    emsChargeSubtotalPO.setTimePeriod(item.getDescription());

                    // 暂不清楚用处
                    emsChargeSubtotalPO.setLocationGid("102.1388.000000.000076");
                    emsChargeSubtotalMapper.insert(emsChargeSubtotalPO);
                }
            }
        });
        return "执行成功啦";
    }

    /**
     * 构造虚拟电流数据
     */
    @PostMapping("/a")
    public void myInsert() {
        emsUsedDataBizService.myInsert();
    }

    /**
     * 构造虚拟电力数据
     */
    @PostMapping("/power")
    public void myInsert2() {
        emsUsedDataBizService.myInsert2();
    }

    /**
     * 实时数据-查询测点最新数据
     *
     * @param usedDataQueryDTO
     * @return
     */
    @PostMapping("getUsedDataByGId")
    public Response getUsedDataByLocId(@RequestBody UsedDataQueryDTO usedDataQueryDTO) {

        List<String> gid = emsUsedDataBizService.getChildrenInfo(usedDataQueryDTO);
        if (gid == null || gid.isEmpty()) {
            gid = new ArrayList<>();
            gid.add(usedDataQueryDTO.getGid());
        }
        List<UsedDataOutDTO> result = emsUsedDataBizService.getUsedDataByLocId(usedDataQueryDTO, gid);
        List<UsedDataOutDTO> data = new ArrayList<>();
        result.forEach(item -> {
            if (!Objects.equals(item.getDeviceName(), "null") &&
                    !Objects.equals(item.getDeviceName(), "hefq")
            ) {
                data.add(item);
            }
        });
        return respSuccessResult(data, "点位信息获取成功!");
    }

    @PostMapping("emsUsedData/getChildrenInfo")
    public Response getChildrenInfo(@RequestBody UsedDataQueryDTO usedDataQueryDTO) {

        List<String> gid = emsUsedDataBizService.getChildrenInfo(usedDataQueryDTO);
        if (gid == null || gid.size() == 0) {
            gid = new ArrayList<>();
            gid.add(usedDataQueryDTO.getGid());
        } else {
            gid.add(usedDataQueryDTO.getGid());
        }
        return respSuccessResult(gid, "点位信息获取成功!");
    }

    @PostMapping("/locationTree/getLocationsByPid")
    @ApiOperation("按顶级父节点查询")
    public Response getLocationsByTopParentGid(@RequestBody LocationTreeQueryDTO param) {
        Object result = emsLocationTreeBizService.getAllTrees(param);
        return respSuccessResult(result, "点位信息获取成功!");
    }

    @PostMapping("/locationTree/getUseDataByIDAndPoint")
    @ApiOperation("按顶级父节点查询")
    public Response getUseDataByIDAndPoint(@RequestBody UsedDataQueryDTO param) {
        Object result = null;
        if (param.getTimeType().equals(EmsConstant.timeType.DAY))
            result = emsUsedDataBizService.getUseDataByIDAndPoint(param);
        if (param.getTimeType().equals(EmsConstant.timeType.MONTH))
            result = emsUsedDataDaySummaryService.getUseDataByMonth(param);
        if (param.getTimeType().equals(EmsConstant.timeType.YEAR))
            result = emsUsedDataMonthSummaryService.getUseDataByYear(param);

        return respSuccessResult(result, "点位信息获取成功!");
    }


}
