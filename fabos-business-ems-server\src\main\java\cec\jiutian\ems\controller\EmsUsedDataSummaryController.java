
package cec.jiutian.ems.controller;

import cec.jiutian.core.base.BaseController;
import cec.jiutian.ems.service.business.EmsUsedDataDaySummaryBizService;
import io.swagger.annotations.Api;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;

/**
 * Ems Used Data Summary;使用信息汇总表
 *
 * <AUTHOR> CodeGenerator
 * @date : 2023-5-29
 */
@Api(tags = "Ems Used Data SummaryAPI")
@RestController
@Slf4j
@AllArgsConstructor
public class EmsUsedDataSummaryController extends BaseController {
    private final EmsUsedDataDaySummaryBizService emsUsedDataSummaryBizService;


}
