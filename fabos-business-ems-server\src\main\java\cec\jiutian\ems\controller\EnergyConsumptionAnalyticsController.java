
package cec.jiutian.ems.controller;

import cec.jiutian.core.base.BaseController;
import cec.jiutian.ems.dto.EnergyConsumptionAsStructureResultDTO;
import cec.jiutian.ems.dto.EnergyConsumptionSimpleResultDTO;
import cec.jiutian.ems.query.dto.ComprehensiveEnergyConsumptionQueryDTO;
import cec.jiutian.ems.service.business.EnergyConsumptionAnalyticsBizService;
import io.swagger.annotations.Api;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.ws.rs.core.Response;
import java.util.List;

/**
 * 能耗分析biz
 */
@Api(tags = "Energy Consumption Analytics")
@RestController
@Slf4j
@AllArgsConstructor
public class EnergyConsumptionAnalyticsController extends BaseController {
    private final EnergyConsumptionAnalyticsBizService energyConsumptionAnalyticsBizService;

    /**
     * 综合能耗日历看板
     */
    @PostMapping("getComprehensiveEnergyConsumptionAsCalendar")
    public Response getComprehensiveEnergyConsumptionAsCalendar(@RequestBody ComprehensiveEnergyConsumptionQueryDTO usedDataQueryDTO) {
        List<EnergyConsumptionSimpleResultDTO> result = energyConsumptionAnalyticsBizService.getComprehensiveEnergyConsumptionAsCalendar(usedDataQueryDTO);
        return respSuccessResult(result, "success");
    }


    /**
     * 综合能耗折线图
     */
    @PostMapping("getComprehensiveEnergyConsumptionAsLineChart")
    public Response getComprehensiveEnergyConsumptionAsLineChart(@RequestBody ComprehensiveEnergyConsumptionQueryDTO usedDataQueryDTO) {
        List<EnergyConsumptionSimpleResultDTO> result = energyConsumptionAnalyticsBizService.getComprehensiveEnergyConsumptionAsLineChart(usedDataQueryDTO);
        return respSuccessResult(result, "success");
    }


    /**
     * 综合能耗 能源结构
     */
    @PostMapping("getComprehensiveEnergyConsumptionAsStructure")
    public Response getComprehensiveEnergyConsumptionAsStructure(@RequestBody ComprehensiveEnergyConsumptionQueryDTO usedDataQueryDTO) {
        EnergyConsumptionAsStructureResultDTO result = energyConsumptionAnalyticsBizService.getComprehensiveEnergyConsumptionAsStructure(usedDataQueryDTO);
        return respSuccessResult(result, "success");
    }

}
