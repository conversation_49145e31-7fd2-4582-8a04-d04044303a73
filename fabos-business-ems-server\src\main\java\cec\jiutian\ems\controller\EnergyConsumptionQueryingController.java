
package cec.jiutian.ems.controller;

import cec.jiutian.core.base.BaseController;
import cec.jiutian.ems.dto.EnergyConsumptionSimpleResultDTO;
import cec.jiutian.ems.dto.EnergyConsumptionUsageResultDTO;
import cec.jiutian.ems.query.dto.EnergyConsumptionByDateRangeQueryDTO;
import cec.jiutian.ems.query.dto.EnergyConsumptionQueryDTO;
import cec.jiutian.ems.service.business.EnergyConsumptionQueryingBizService;
import io.swagger.annotations.Api;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.ws.rs.core.Response;
import java.util.List;

/**
 * 用能查询
 */
@Api(tags = "Energy Consumption Analytics")
@RestController
@Slf4j
@AllArgsConstructor
public class EnergyConsumptionQueryingController extends BaseController {
    private final EnergyConsumptionQueryingBizService energyConsumptionQueryingBizService;

    /**
     * 用能查询
     */
    @PostMapping("/getEnergyConsumptionUsageByLocGidAndType")
    public Response getEnergyConsumptionUsageByLocGidAndType(@RequestBody EnergyConsumptionQueryDTO queryDto) {
        List<EnergyConsumptionUsageResultDTO> result = energyConsumptionQueryingBizService.getEnergyConsumptionUsageByLocGidAndType(queryDto);
        return respSuccessResult(result, "success");
    }

    /**
     * 能耗对比
     * @param queryDto
     * @return
     */
    @PostMapping("/getEnergyConsumptionUsageByLocGidAndTypeAndDateRange")
    public Response getEnergyConsumptionUsageByLocGidAndTypeAndDateRange(@RequestBody EnergyConsumptionByDateRangeQueryDTO queryDto) {
        EnergyConsumptionSimpleResultDTO result = energyConsumptionQueryingBizService.getEnergyConsumptionUsageByLocGidAndTypeAndDateRange(queryDto);
        return respSuccessResult(result, "success");
    }

    /**
     * 用能查询
     * @param queryDto
     * @return
     */
    @PostMapping("/getEnergyConsumptionSelect")
    public Response getEnergyConsumptionSelect(@RequestBody EnergyConsumptionQueryDTO queryDto) {
        Object result = energyConsumptionQueryingBizService.getEnergyConsumptionSelect(queryDto);
        return respSuccessResult(result, "success");
    }

}
