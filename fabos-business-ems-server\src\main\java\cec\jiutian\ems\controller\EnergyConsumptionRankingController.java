
package cec.jiutian.ems.controller;

import cec.jiutian.core.base.BaseController;
import cec.jiutian.ems.dto.EnergyConsumptionRankingResultDTO;
import cec.jiutian.ems.query.dto.EnergyConsumptionQueryDTO;
import cec.jiutian.ems.service.business.EnergyConsumptionRankingBizService;
import io.swagger.annotations.Api;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.ws.rs.core.Response;
import java.util.List;

/**
 * 能耗排行
 */
@Api(tags = "Energy Consumption Ranking")
@RestController
@Slf4j
@AllArgsConstructor
public class EnergyConsumptionRankingController extends BaseController {
    private final EnergyConsumptionRankingBizService energyConsumptionRankingBizService;

    /**
     * 能耗排行 等级1（厂区
     */
    @PostMapping("/getEnergyConsumptionRankingLvl1")
    public Response getEnergyConsumptionRankingLvl1(@RequestBody EnergyConsumptionQueryDTO usedDataQueryDTO) {
        List<EnergyConsumptionRankingResultDTO> result = energyConsumptionRankingBizService.getEnergyConsumptionRankingLvl1(usedDataQueryDTO);
        return respSuccessResult(result, "success");
    }

    /**
     * 能耗排行 等级2（产线
     */
    @PostMapping("/getEnergyConsumptionRankingLvl2")
    public Response getEnergyConsumptionRankingLvl2(@RequestBody EnergyConsumptionQueryDTO usedDataQueryDTO) {
        List<EnergyConsumptionRankingResultDTO> result = energyConsumptionRankingBizService.getEnergyConsumptionRankingLvl2(usedDataQueryDTO);
        return respSuccessResult(result, "success");
    }

    /**
     * 能耗排行 等级3（设备段
     */
    @PostMapping("/getEnergyConsumptionRankingLvl3")
    public Response getEnergyConsumptionRankingLvl3(@RequestBody EnergyConsumptionQueryDTO usedDataQueryDTO) {
        List<EnergyConsumptionRankingResultDTO> result = energyConsumptionRankingBizService.getEnergyConsumptionRankingLvl3(usedDataQueryDTO);
        return respSuccessResult(result, "success");
    }

    /**
     * 能耗排行 等级4（设备
     */
    @PostMapping("/getEnergyConsumptionRankingLvl4")
    public Response getEnergyConsumptionRankingLvl4(@RequestBody EnergyConsumptionQueryDTO usedDataQueryDTO) {
        List<EnergyConsumptionRankingResultDTO> result = energyConsumptionRankingBizService.getEnergyConsumptionRankingLvl4(usedDataQueryDTO);
        return respSuccessResult(result, "success");
    }
}
