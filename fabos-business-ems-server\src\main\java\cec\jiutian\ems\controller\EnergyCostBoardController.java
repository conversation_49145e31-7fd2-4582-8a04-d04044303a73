
package cec.jiutian.ems.controller;

import cec.jiutian.core.base.BaseController;
import cec.jiutian.ems.dto.EnergyCostBoardTotalChargeResultDTO;
import cec.jiutian.ems.dto.EnergyCostBoardUsageResultDTO;
import cec.jiutian.ems.query.dto.EnergyConsumptionQueryDTO;
import cec.jiutian.ems.service.business.EnergyCostBoardBizService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.ws.rs.core.Response;
import java.util.List;
import java.util.Map;

/**
 * 能源费用看板
 */
@Api(tags = "能源费用看板")
@RestController
@Slf4j
@AllArgsConstructor
public class EnergyCostBoardController extends BaseController {
    private EnergyCostBoardBizService energyCostBoardBizService;

    /**
     * 查询能源费用看板
     */
    @PostMapping("/getTotalElectricityCostOverview")
    @ApiOperation(value = "查询单位能耗趋势折线图")
    public Response getTotalElectricityCostOverview(@RequestBody EnergyConsumptionQueryDTO queryDTO) {
        Map<String, EnergyCostBoardTotalChargeResultDTO> result = energyCostBoardBizService.getTotalElectricityCostOverview(queryDTO);
        return respSuccessResult(result, "操作成功");
    }

    /**
     * 查询能源费用电费电量
     */
    @PostMapping("/getTotalElectricityCostLineChart")
    @ApiOperation(value = "查询单位能耗趋势折线图")
    public Response getTotalElectricityCostLineChart(@RequestBody EnergyConsumptionQueryDTO queryDTO) {
        List<EnergyCostBoardUsageResultDTO> result = energyCostBoardBizService.getTotalElectricityCostLineChart(queryDTO);
        return respSuccessResult(result, "操作成功");
    }

}
