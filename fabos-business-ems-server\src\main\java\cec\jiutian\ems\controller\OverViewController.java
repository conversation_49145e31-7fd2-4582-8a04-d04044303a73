
package cec.jiutian.ems.controller;

import cec.jiutian.core.base.BaseController;
import cec.jiutian.core.comn.util.BeanValidators;
import cec.jiutian.ems.query.dto.OverViewQueryDTO;
import cec.jiutian.ems.service.business.OverViewBizService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.ws.rs.core.Response;

/**
 * Ems Used Data;使用数据
 * <AUTHOR> CodeGenerator
 * @date : 2023-5-29
 */
@Api(tags = "总览页面")
@RestController
@Slf4j
@AllArgsConstructor
@RequestMapping("/overView")
public class OverViewController extends BaseController {
    private OverViewBizService overViewBizService;

    /**
     * 查询用能排行
     *
     * @param queryDTO
     * @return
     */
    @PostMapping("/getEnergySort")
    @ApiOperation(value = "用能排行", notes = "相关表：ems_used_data")
    public Response gey(@RequestBody OverViewQueryDTO queryDTO) {
        Object result= overViewBizService.getEnergySort(queryDTO);
        return respSuccessResult(result,"操作成功");
    }
    /**
     * 全厂用能总览
     *
     * @param queryDTO
     * @return
     */
    @PostMapping("/getFactoryEnergy")
    @ApiOperation(value = "全厂用能总览", notes = "相关表：ems_used_data")
    public Response getFactoryEnergy(@RequestBody OverViewQueryDTO queryDTO) {
        Object result= overViewBizService.getFactoryEnergy(queryDTO);
        return respSuccessResult(result,"操作成功");
    }

    /**
     * 全厂用能总览
     *
     * @param queryDTO
     * @return
     */
    @PostMapping("/getCarbonEmission")
    @ApiOperation(value = "碳排放", notes = "相关表：ems_used_data")
    public Response getCarbonEmission(@RequestBody OverViewQueryDTO queryDTO) {
        BeanValidators.validateWithException(validator, queryDTO);
        Object result= overViewBizService.getCarbonEmission(queryDTO);
        return respSuccessResult(result,"操作成功");
    }
    @PostMapping("/getTotalLoadTrend")
    @ApiOperation(value = "总负荷趋势", notes = "相关表：ems_used_data")
    public Response getTotalLoadTrend(@RequestBody OverViewQueryDTO queryDTO) {
        BeanValidators.validateWithException(validator, queryDTO);
        Object result= overViewBizService.getTotalLoadTrend(queryDTO);
        return respSuccessResult(result,"操作成功");
    }

    @PostMapping("/getConsumptionTrend")
    @ApiOperation(value = "能耗趋势", notes = "相关表：ems_used_data")
    public Response getConsumptionTrend(@RequestBody OverViewQueryDTO queryDTO) {
        BeanValidators.validateWithException(validator, queryDTO);
        Object result= overViewBizService.getConsumptionTrend(queryDTO);
        return respSuccessResult(result,"操作成功");
    }

    @PostMapping("/getEnergyBalance")
    @ApiOperation(value = "能量平衡图", notes = "相关表：ems_used_data")
    public Response getEnergyBalance(@RequestBody OverViewQueryDTO queryDTO) {
//        BeanValidators.validateWithException(validator, queryDTO);
        Object result= overViewBizService.getEnergyBalance(queryDTO);
        return respSuccessResult(result,"操作成功");
    }


    @PostMapping("/getFactory")
    @ApiOperation(value = "厂区下拉列表", notes = "相关表：ems_location_tree")
    public Response getFactory() {
        Object result= overViewBizService.getFactory();
        return respSuccessResult(result,"操作成功");
    }


}
