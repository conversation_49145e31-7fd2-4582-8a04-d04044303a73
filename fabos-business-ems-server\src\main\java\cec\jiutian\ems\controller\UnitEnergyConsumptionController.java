
package cec.jiutian.ems.controller;

import cec.jiutian.core.base.BaseController;
import cec.jiutian.ems.dto.EnergyConsumptionSimpleResultDTO;
import cec.jiutian.ems.dto.EnergyUnitConsumptionRecordResultDTO;
import cec.jiutian.ems.query.dto.EnergyConsumptionQueryDTO;
import cec.jiutian.ems.service.business.UnitEnergyConsumptionBizService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.ws.rs.core.Response;
import java.util.List;

/**
 * 单位能耗
 */
@Api(tags = "单位能耗")
@RestController
@Slf4j
@AllArgsConstructor
public class UnitEnergyConsumptionController extends BaseController {
    private UnitEnergyConsumptionBizService unitEnergyConsumptionBizService;

    /**
     * 查询单位能耗趋势折线图
     */
    @PostMapping("/getUnitEnergyConsumptionTrend")
    @ApiOperation(value = "查询单位能耗趋势折线图", notes = "相关表：ems_used_data_day_summary、ems_used_data_month_summary、ems_yield_data")
    public Response getUnitEnergyConsumptionTrend(@RequestBody EnergyConsumptionQueryDTO queryDTO) {
        List<EnergyConsumptionSimpleResultDTO> result = unitEnergyConsumptionBizService.getUnitEnergyConsumptionTrend(queryDTO);
        return respSuccessResult(result, "操作成功");
    }

    /**
     * 查询单位能耗表格
     */
    @PostMapping("/getUnitEnergyConsumptionRecord")
    @ApiOperation(value = "查询单位能耗表格", notes = "相关表：ems_used_data_day_summary、ems_used_data_month_summary、ems_yield_data")
    public Response getUnitEnergyConsumptionRecord(@RequestBody EnergyConsumptionQueryDTO queryDTO) {
        List<EnergyUnitConsumptionRecordResultDTO> result = unitEnergyConsumptionBizService.getUnitEnergyConsumptionRecord(queryDTO);
        return respSuccessResult(result, "操作成功");
    }
}
