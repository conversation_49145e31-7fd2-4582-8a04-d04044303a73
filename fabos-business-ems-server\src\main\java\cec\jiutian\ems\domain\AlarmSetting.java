package cec.jiutian.ems.domain;

import cec.jiutian.core.comn.util.BeanUtils;
import cec.jiutian.core.ext.base.TrxnDomain;
import cec.jiutian.ems.po.AlarmSettingPO;

/**
 * Alarm Setting;报警设置
 *
 * <AUTHOR> CodeGenerator
 * @date : 2023-6-7
 */
public class AlarmSetting extends TrxnDomain<AlarmSettingPO> {
    public AlarmSetting() {
        super(new AlarmSettingPO());
    }

    public AlarmSetting(AlarmSettingPO entity) {
        super(entity);
    }

    public void init(Object dto) {
        BeanUtils.copyProperties(dto, getEntity());
        setAuditField();
    }

    public void setAlarmCondition(String alarmCondition) {
        getEntity().setAlarmCondition(alarmCondition);
    }

    public void setNormalFlag(String normalFlag) {
        getEntity().setNormalFlag(normalFlag);
    }

    public void setAlarmContent(String alarmContent) {
        getEntity().setAlarmContent(alarmContent);
    }

    public void setPushUser(String pushUser) {
        getEntity().setPushUser(pushUser);
    }

    public void setPushMethod(String pushMethod) {
        getEntity().setPushMethod(pushMethod);
    }


    public String getGid() {
        return getEntity().getGid();
    }

    public String getAlarmTypeCode() {
        return getEntity().getAlarmTypeCode();
    }

    public String getAlarmName() {
        return getEntity().getAlarmName();
    }
}