package cec.jiutian.ems.domain;
import java.io.Serializable;

import cec.jiutian.base.entity.TrxnEntity;
import cec.jiutian.core.comn.util.BeanUtils;
import cec.jiutian.core.entity.AbstractDomain;
import cec.jiutian.core.ext.base.TrxnDomain;
import cec.jiutian.ems.po.AlarmSettingParameterRelationPO;

/**
 * Alarm Setting Parameter Relation;报警设置点位绑定
 * <AUTHOR> CodeGenerator
 * @date : 2023-6-7
 */
public class AlarmSettingParameterRelation extends TrxnDomain<AlarmSettingParameterRelationPO> {
    public AlarmSettingParameterRelation() {
        super(new AlarmSettingParameterRelationPO());
    }
    public AlarmSettingParameterRelation(AlarmSettingParameterRelationPO entity) {
        super(entity);
    }

    public void init(Object dto) {
        BeanUtils.copyProperties(dto, getEntity());
        setAuditField();
    }

    public void setAlarmSettingGid(String alarmSettingGid) {
        getEntity().setAlarmSettingGid(alarmSettingGid);
    }

}
