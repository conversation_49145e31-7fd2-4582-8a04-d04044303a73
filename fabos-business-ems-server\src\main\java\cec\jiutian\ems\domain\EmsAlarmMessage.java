package cec.jiutian.ems.domain;

import cec.jiutian.core.comn.util.BeanUtils;
import cec.jiutian.core.ext.base.TrxnDomain;
import cec.jiutian.ems.dto.EmsAlarmMessageCreateDTO;
import cec.jiutian.ems.po.EmsAlarmMessagePO;

/**
 * EMS Alarm Message;报警信息
 * <AUTHOR> CodeGenerator
 * @date : 2023-6-12
 */
public class EmsAlarmMessage extends TrxnDomain<EmsAlarmMessagePO> {
    public EmsAlarmMessage() {
        super(new EmsAlarmMessagePO());
    }
    public EmsAlarmMessage(EmsAlarmMessagePO entity) {
        super(entity);
    }
    public void init(EmsAlarmMessageCreateDTO emsAlarmMessageCreateDTO) {
        BeanUtils.copyProperties(emsAlarmMessageCreateDTO, getEntity());
    }

    public void setAlarmStatus(String alarmStatus) {
        getEntity().setAlarmStatus(alarmStatus);
    }

    public void setAlarmContent(String alarmContent) {
        getEntity().setAlarmContent(alarmContent);
    }

    public void setAlarmCondition(String alarmCondition) {
        getEntity().setAlarmCondition(alarmCondition);
    }

    public void setCurrentValue(Double currentValue) {
        getEntity().setCurrentValue(currentValue);
    }



}