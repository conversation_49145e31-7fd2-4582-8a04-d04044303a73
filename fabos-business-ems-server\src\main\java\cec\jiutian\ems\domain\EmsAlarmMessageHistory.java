package cec.jiutian.ems.domain;
import java.io.Serializable;

import cec.jiutian.core.comn.util.BeanUtils;
import cec.jiutian.core.entity.AbstractDomain;
import cec.jiutian.core.ext.base.TrxnDomain;
import cec.jiutian.ems.dto.EmsAlarmMessageCreateDTO;
import cec.jiutian.ems.po.EmsAlarmMessageHistoryPO;

/**
 * EMS Alarm Message History;报警历史
 * <AUTHOR> CodeGenerator
 * @date : 2023-6-12
 */
public class EmsAlarmMessageHistory extends TrxnDomain<EmsAlarmMessageHistoryPO> {
    public EmsAlarmMessageHistory() {
        super(new EmsAlarmMessageHistoryPO());
    }
    public EmsAlarmMessageHistory(EmsAlarmMessageHistoryPO entity) {
        super(entity);
    }
    public void init(EmsAlarmMessageCreateDTO emsAlarmMessageCreateDTO) {
        BeanUtils.copyProperties(emsAlarmMessageCreateDTO, getEntity());
    }

    public void setAlarmStatus(String alarmStatus) {
        getEntity().setAlarmStatus(alarmStatus);
    }

    public void setAlarmContent(String alarmContent) {
        getEntity().setAlarmContent(alarmContent);
    }

    public void setAlarmCondition(String alarmCondition) {
        getEntity().setAlarmCondition(alarmCondition);
    }

    public void setCurrentValue(Double currentValue) {
        getEntity().setCurrentValue(currentValue);
    }
}
