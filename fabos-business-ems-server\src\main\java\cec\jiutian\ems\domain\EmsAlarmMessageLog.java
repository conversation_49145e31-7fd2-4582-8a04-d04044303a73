package cec.jiutian.ems.domain;
import java.io.Serializable;

import cec.jiutian.core.comn.util.BeanUtils;
import cec.jiutian.core.entity.AbstractDomain;
import cec.jiutian.core.ext.base.TrxnDomain;
import cec.jiutian.ems.dto.EmsAlarmMessageCreateDTO;
import cec.jiutian.ems.po.EmsAlarmMessageLogPO;

/**
 * EMS Alarm Message Log;报警日志
 * <AUTHOR> CodeGenerator
 * @date : 2023-6-12
 */
public class EmsAlarmMessageLog extends TrxnDomain<EmsAlarmMessageLogPO> {
    public EmsAlarmMessageLog() {
        super(new EmsAlarmMessageLogPO());
    }
    public EmsAlarmMessageLog(EmsAlarmMessageLogPO entity) {
        super(entity);
    }
    public void init(EmsAlarmMessageCreateDTO emsAlarmMessageCreateDTO) {
        BeanUtils.copyProperties(emsAlarmMessageCreateDTO, getEntity());
    }

    public void setAlarmStatus(String alarmStatus) {
        getEntity().setAlarmStatus(alarmStatus);
    }

    public void setAlarmContent(String alarmContent) {
        getEntity().setAlarmContent(alarmContent);
    }

    public void setAlarmCondition(String alarmCondition) {
        getEntity().setAlarmCondition(alarmCondition);
    }

    public void setCurrentValue(Double currentValue) {
        getEntity().setCurrentValue(currentValue);
    }
}
