package cec.jiutian.ems.domain;

import cec.jiutian.core.ext.base.TrxnDomain;
import cec.jiutian.ems.po.EmsConfigPO;

/**
 * Ems Config;
 *
 * <AUTHOR> CodeGenerator
 * @date : 2023-6-15
 */
public class EmsConfig extends TrxnDomain<EmsConfigPO> {
    public static final String CONVERSION_COEFFICIENT = "conversionCoefficient"; //折算系数
    public static final String CII_COEFFICIENT = "ciiCoefficient"; //碳强度系数
    public static final String CARBON_QUOTA = "carbonQuota"; //碳配额

    public EmsConfig() {
        super(new EmsConfigPO());
    }

    public EmsConfig(EmsConfigPO entity) {
        super(entity);
    }

}