package cec.jiutian.ems.domain;

import cec.jiutian.core.comn.util.BeanUtils;
import cec.jiutian.core.ext.base.TrxnDomain;
import cec.jiutian.ems.dto.IndexSettingCreateDTO;
import cec.jiutian.ems.po.EmsIndexSettingPO;

/**
 * Ems Index Setting;指标设置表
 *
 * <AUTHOR> CodeGenerator
 * @date : 2023-6-19
 */
public class EmsIndexSetting extends TrxnDomain<EmsIndexSettingPO> {
    public EmsIndexSetting() {
        super(new EmsIndexSettingPO());
    }

    public EmsIndexSetting(EmsIndexSettingPO entity) {
        super(entity);
    }

    public void create(IndexSettingCreateDTO opDTO) {
        BeanUtils.copyProperties(opDTO, getEntity());
    }

    public String getEnergyType() {
        return getEntity().getEnergyType();
    }

    public Integer getYear() {
        return getEntity().getYear();
    }

    public String getLocationTreeGid() {
        return getEntity().getLocationTreeGid();
    }
}