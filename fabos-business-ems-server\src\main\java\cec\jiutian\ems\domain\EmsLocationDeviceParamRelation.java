
package cec.jiutian.ems.domain;

import cec.jiutian.core.ext.base.TrxnDomain;
import cec.jiutian.ems.po.EmsLocationDeviceParamRelationPO;
import cec.jiutian.ems.po.EmsParamInfoPO;

/**
 * EMS Location Device Param Relation;位置树与测点关系
 *
 * <AUTHOR> CodeGenerator
 * @date : 2023-5-29
 */
public class EmsLocationDeviceParamRelation extends TrxnDomain<EmsLocationDeviceParamRelationPO> {
    public EmsLocationDeviceParamRelation() {
        super(new EmsLocationDeviceParamRelationPO());
    }

    public EmsLocationDeviceParamRelation(EmsLocationDeviceParamRelationPO entity) {
        super(entity);
    }

    public void setFieldsFrom(EmsParamInfoPO param) {
        EmsLocationDeviceParamRelationPO po = getEntity();
        po.setModelCode(param.getModelCode());
        po.setDeviceCode(param.getDeviceCode());
        po.setUnit(param.getUnit());
        po.setParamType(param.getParamType());
        po.setParamValueType(param.getParamValueType());
        po.setParamInfoGid(param.getGid());

        po.setParamName(param.getParamName());
    }

    public void setFieldsFrom(EmsLocationTree locationTree) {
        EmsLocationDeviceParamRelationPO po = getEntity();
        po.setLocationGid(locationTree.getId());
        po.setTopParentGid(locationTree.getTopParentGid());
    }

    public EmsLocationDeviceParamRelation setBindType(String type) {
        getEntity().setBindType(type);
        return this;
    }
}