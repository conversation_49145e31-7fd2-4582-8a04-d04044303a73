
package cec.jiutian.ems.domain;

import cec.jiutian.core.ext.base.TrxnDomain;
import cec.jiutian.ems.dto.EmsLocationTreeResultDTO;
import cec.jiutian.ems.po.EmsLocationTreePO;

import java.util.*;

/**
 * EMS location tree;位置树表
 * <AUTHOR> CodeGenerator
 * @date : 2023-5-29
 */
public class EmsLocationTree extends TrxnDomain<EmsLocationTreePO> {
    public EmsLocationTree() {
        super(new EmsLocationTreePO());
    }

    public EmsLocationTree(EmsLocationTreePO entity) {
        super(entity);
    }

    public int getLocationLevel() {
        return Integer.parseInt(getEntity().getLocationLevel());
    }

    public String getNextLevelAsString() {
        return String.valueOf((1 + getLocationLevel()));
    }

    public String getTopParentGid() {
        return getEntity().getTopParentGid();
    }

    public String getParentGid() {
        return getEntity().getParentGid();
    }



    public static List<EmsLocationTreeResultDTO> convertTreesWithRecurse(List<EmsLocationTreeResultDTO> treeList) {
        List<EmsLocationTreeResultDTO> trees = new ArrayList<>();
        for (EmsLocationTreeResultDTO tree : treeList) {
            // 找出父节点
            if ("0".equals(tree.getParentGid()) ) {
                // 调用递归方法填充子节点列表
                trees.add(findChildren(tree, treeList));
            }
        }
        return trees;
    }
    public static EmsLocationTreeResultDTO findChildren(EmsLocationTreeResultDTO tree, List<EmsLocationTreeResultDTO> treeList) {
        for (EmsLocationTreeResultDTO node : treeList) {
            if (tree.getGid().equals(node.getParentGid())) {
                if (tree.getChildren() == null) {
                    tree.setChildren(new ArrayList<>());
                }
                // 递归 调用自身
                tree.getChildren().add(findChildren(node, treeList));
            }
        }
        return tree;
    }


}

