package cec.jiutian.ems.domain;

import cec.jiutian.core.comn.util.BeanUtils;
import cec.jiutian.core.ext.base.TrxnDomain;
import cec.jiutian.ems.po.EmsUnitPricePO;

/**
 * 能源单价;
 * <AUTHOR> CodeGenerator
 * @date : 2023-6-19
 */
public class EmsUnitPrice extends TrxnDomain<EmsUnitPricePO> {
    public EmsUnitPrice() {
        super(new EmsUnitPricePO());
    }
    public EmsUnitPrice(EmsUnitPricePO entity) {
        super(entity);
    }

    public void init(Object dto) {
        BeanUtils.copyProperties(dto, getEntity());
        setAuditField();
    }

    public void setTimePeriod(String timePeriod){
        getEntity().setTimePeriod(timePeriod);
    }

    public void setSpecificTimePeriod(String specificTimePeriod){
        getEntity().setSpecificTimePeriod(specificTimePeriod);
    }

    public void setUnitPrice(Double unitPrice){
        getEntity().setUnitPrice(unitPrice);
    }
}
