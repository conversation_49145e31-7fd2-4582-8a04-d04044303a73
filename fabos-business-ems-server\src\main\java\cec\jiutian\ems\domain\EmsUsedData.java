
package cec.jiutian.ems.domain;

import cec.jiutian.core.ext.base.TrxnDomain;
import cec.jiutian.ems.dto.UsedDataOutDTO;
import cec.jiutian.ems.po.EmsUsedDataPO;
import cec.jiutian.ems.query.dto.UsedDataQueryDTO;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 * Ems Used Data;使用数据
 * <AUTHOR> CodeGenerator
 * @date : 2023-5-29
 */
public class EmsUsedData extends TrxnDomain<EmsUsedDataPO> {
    public EmsUsedData() {
        super(new EmsUsedDataPO());
    }
    public EmsUsedData(EmsUsedDataPO entity) {
        super(entity);
    }
    public static List<UsedDataOutDTO> poConvertToOutDtoByDeviceName(List<EmsUsedDataPO> convert,UsedDataQueryDTO usedDataQueryDTO) {
//        locationGid

        HashMap<String, ArrayList> convertMap = new HashMap<>();
        for (EmsUsedDataPO emsUsedDataPO : convert) {
            ArrayList list = convertMap.get(emsUsedDataPO.getDeviceName()+","+emsUsedDataPO.getLocationGid());
            if (list==null)
            {
                ArrayList<Object> objects = new ArrayList<>();
                objects.add(emsUsedDataPO);
                convertMap.put(emsUsedDataPO.getDeviceName() +","+emsUsedDataPO.getLocationGid(),objects);
            }else
            {
                list.add(emsUsedDataPO);
                convertMap.put(emsUsedDataPO.getDeviceName() +","+emsUsedDataPO.getLocationGid(),list);
            }

        }
        List<UsedDataOutDTO> result = new ArrayList<>();
        for (String key : convertMap.keySet()) {
            UsedDataOutDTO usedDataOutDTO = new UsedDataOutDTO().setDeviceName(key.split(",")[0])
                    .setEmsUsedDatas(convertMap.get(key)).setGid(usedDataQueryDTO.getGid());

            result.add(usedDataOutDTO);
        }

        return result;
    }

}
