package cec.jiutian.ems.facade.dto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2023/5/22
 */
@Data
@ApiModel("MessageCreateDTO")
public class MessageCreateDTO {

    /**
     * 推送方式
     */
    @ApiModelProperty("推送方式")
    private String dispatchWay;

    /**
     * 推送人员
     */
    @ApiModelProperty("推送人员")
    private String dispatchUser;

    /**
     * 升级次数
     */
    @ApiModelProperty("升级次数")
    private Long upNumber;

    /**
     * 升级间隔时间(分)
     */
    @ApiModelProperty("升级间隔时间(分)")
    private Long upIntervalMinute;

    /**
     * 消息组id
     */
    @ApiModelProperty("消息组id")
    private String messageGroupGid;

    /**
     * 消息模板id
     */
    @ApiModelProperty("消息模板id")
    private String messageTemplateGid;

    /**
     * 消息内容
     */
    @NotNull
    @ApiModelProperty("消息内容")
    private String content;

    /**
     * 是否需要处理
     */
    @ApiModelProperty("是否需要处理")
    private String isProcess;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String remark;

    private String sysName;
    private String authKey;
}

