package cec.jiutian.ems.facade.feign;

import cec.jiutian.ems.facade.dto.MessageCreateDTO;
import cec.jiutian.ems.facade.feign.fallback.MessageRequestFallBack;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(value = "fabos-business-ecs", contextId = "MessageRequestFeign", fallback = MessageRequestFallBack.class)
public interface MessageRequestFeign {
    @PostMapping("/remote/message/receiveMsg")
    Boolean receiveMsg(@RequestBody MessageCreateDTO messageCreateDTO);
}
