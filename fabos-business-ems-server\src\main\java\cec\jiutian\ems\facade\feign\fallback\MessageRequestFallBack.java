package cec.jiutian.ems.facade.feign.fallback;

import cec.jiutian.core.comn.errorCode.BaseErrorCode;
import cec.jiutian.core.exception.MesErrorCodeException;
import cec.jiutian.ems.facade.dto.MessageCreateDTO;
import cec.jiutian.ems.facade.feign.MessageRequestFeign;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class MessageRequestFallBack implements MessageRequestFeign {
    @Override
    public Boolean receiveMsg(MessageCreateDTO messageCreateDTO) {
        MesErrorCodeException exception = new MesErrorCodeException(BaseErrorCode.FEIGN_GET_NAME_FAIL.getCode());
        throw exception;
    }
}
