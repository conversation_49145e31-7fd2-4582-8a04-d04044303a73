package cec.jiutian.ems.mapper;

import cec.jiutian.core.base.BaseMapper;
import cec.jiutian.ems.dto.GetAlarmSettingByParamDTO;
import cec.jiutian.ems.dto.GetAlarmSettingByParamResultDTO;
import cec.jiutian.ems.dto.GetAlarmSettingDTO;
import cec.jiutian.ems.po.AlarmSettingPO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * Alarm Setting;报警设置
 * <AUTHOR> CodeGenerator
 * @date : 2023-6-7
 */
@Mapper
public interface AlarmSettingMapper extends BaseMapper<AlarmSettingPO> {
      List<AlarmSettingPO> getAlarmSettingList(GetAlarmSettingDTO getAlarmSettingDTO);
      List<GetAlarmSettingByParamResultDTO> getAlarmSettingByParamList(GetAlarmSettingByParamDTO getAlarmSettingByParamDTO);
}
