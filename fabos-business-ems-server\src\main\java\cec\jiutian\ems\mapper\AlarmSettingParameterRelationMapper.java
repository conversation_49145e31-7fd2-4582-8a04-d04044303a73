package cec.jiutian.ems.mapper;

import cec.jiutian.core.base.BaseMapper;
import cec.jiutian.ems.dto.GetAlarmSettingDTO;
import cec.jiutian.ems.dto.GetAlarmSettingParameterRelationDTO;
import cec.jiutian.ems.po.AlarmSettingParameterRelationPO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * Alarm Setting Parameter Relation;报警设置点位绑定
 * <AUTHOR> CodeGenerator
 * @date : 2023-6-7
 */
@Mapper
public interface AlarmSettingParameterRelationMapper extends BaseMapper<AlarmSettingParameterRelationPO> {
    List<AlarmSettingParameterRelationPO> getAlarmSettingParameterRelationList(GetAlarmSettingParameterRelationDTO getAlarmSettingParameterRelationDTO);
}
