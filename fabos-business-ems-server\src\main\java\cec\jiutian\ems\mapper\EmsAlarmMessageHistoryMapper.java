package cec.jiutian.ems.mapper;
import cec.jiutian.core.base.BaseMapper;
import cec.jiutian.ems.dto.GetEmsAlarmMessageHistoryByEventAggregationResultDTO;
import cec.jiutian.ems.dto.GetEmsAlarmMessageHistoryDTO;
import cec.jiutian.ems.po.EmsAlarmMessageHistoryPO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * EMS Alarm Message History;报警历史
 * <AUTHOR> CodeGenerator
 * @date : 2023-6-12
 */
@Mapper
public interface EmsAlarmMessageHistoryMapper extends BaseMapper<EmsAlarmMessageHistoryPO> {
    List<EmsAlarmMessageHistoryPO> getEmsAlarmMessageHistoryList(GetEmsAlarmMessageHistoryDTO getEmsAlarmMessageHistoryDTO);
    List<GetEmsAlarmMessageHistoryByEventAggregationResultDTO> getEmsAlarmMessageHistoryByEventAggregationList(GetEmsAlarmMessageHistoryDTO getEmsAlarmMessageHistoryDTO);
}