package cec.jiutian.ems.mapper;

import cec.jiutian.core.base.BaseMapper;
import cec.jiutian.ems.dto.GetEmsAlarmMessageDTO;
import cec.jiutian.ems.dto.GetEmsAlarmMessageHistoryDTO;
import cec.jiutian.ems.po.EmsAlarmMessagePO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * EMS Alarm Message;报警信息
 * <AUTHOR> CodeGenerator
 * @date : 2023-6-12
 */
@Mapper
public interface EmsAlarmMessageMapper extends BaseMapper<EmsAlarmMessagePO> {
    List<GetEmsAlarmMessageDTO> getEmsAlarmMessageList(GetEmsAlarmMessageHistoryDTO getEmsAlarmMessageHistoryDTO);
}