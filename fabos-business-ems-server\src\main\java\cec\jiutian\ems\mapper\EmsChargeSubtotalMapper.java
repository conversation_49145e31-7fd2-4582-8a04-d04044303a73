package cec.jiutian.ems.mapper;

import cec.jiutian.core.base.BaseMapper;
import cec.jiutian.ems.dto.*;
import cec.jiutian.ems.po.EmsChargeSubtotalPO;
import cec.jiutian.ems.query.dto.EnergyConsumptionQueryDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface EmsChargeSubtotalMapper extends BaseMapper<EmsChargeSubtotalPO> {
    Double getTotalChargeList(GetTotalChargeDTO getTotalChargeDTO);
    Double getTotalChargePercentList(GetTotalChargeDTO getTotalChargeDTO);
    List<GetEmsChargeSubtotalForBarResultDTO> getEmsChargeSubtotalForBarList(GetTotalChargeDTO getTotalChargeDTO);
    List<GetEmsChargeSubtotalForPieResultDTO> getEmsChargeSubtotalForPieList(GetTotalChargeDTO getTotalChargeDTO);
    List<EnergyCostBoardTotalChargeResultDTO> getTotalElectricityCostOverview(EnergyConsumptionQueryDTO queryDTO);

    List<EmsChargeSubtotalPO> getTotalElectricityCostLineChartByMonth(EnergyConsumptionQueryDTO queryDTO);

    List<EmsChargeSubtotalPO> getTotalElectricityCostLineChartByYear(EnergyConsumptionQueryDTO queryDTO);

    List<GetEmsChargeSubtotalForTableResultDTO> getEmsChargeSubtotalForTableList(GetTotalChargeDTO getTotalChargeDTO);


    void myInsert(@Param("obj") EmsChargeSubtotalPO emsChargeSubtotalPO);
}
