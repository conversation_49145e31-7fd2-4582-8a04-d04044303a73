package cec.jiutian.ems.mapper;

import cec.jiutian.core.base.BaseMapper;
import cec.jiutian.ems.po.EmsConfigPO;
import cec.jiutian.ems.query.dto.EmsConfigQueryDTO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * ems config
 *
 * <AUTHOR> CodeGenerator
 * @date : 2023-6-15
 */
@Mapper
public interface EmsConfigMapper extends BaseMapper<EmsConfigPO> {

    List<EmsConfigPO> getByConditions(EmsConfigQueryDTO queryDTO);
}
