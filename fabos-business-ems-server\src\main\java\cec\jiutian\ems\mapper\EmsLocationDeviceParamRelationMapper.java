
package cec.jiutian.ems.mapper;

import cec.jiutian.core.base.BaseMapper;
import cec.jiutian.ems.dto.GetRelationshipListDTO;
import cec.jiutian.ems.po.EmsLocationDeviceParamRelationPO;
import cec.jiutian.ems.query.dto.LocationDeviceParamRelationQueryDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * EMS Location Device Param Relation;位置树与测点关系
 *
 * <AUTHOR> CodeGenerator
 * @date : 2023-5-29
 */
@Mapper
public interface EmsLocationDeviceParamRelationMapper extends BaseMapper<EmsLocationDeviceParamRelationPO> {
    List<EmsLocationDeviceParamRelationPO> getRelationshipList(LocationDeviceParamRelationQueryDTO param);

    List<GetRelationshipListDTO> getRelationshipListCreateTime(LocationDeviceParamRelationQueryDTO param);

    @Select("select count(0) from ems_location_device_param_relation r where r.location_gid = #{locationGid}")
    Integer countByLocationGid(String locationGid);

    @Select("select count(0) from ems_location_device_param_relation r where r.param_info_gid = #{paramGid}")
    Integer countByParamGid(String paramGid);
}