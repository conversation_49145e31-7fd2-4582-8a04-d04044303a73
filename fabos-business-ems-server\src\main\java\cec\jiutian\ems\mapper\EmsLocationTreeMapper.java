package cec.jiutian.ems.mapper;

import cec.jiutian.core.base.BaseMapper;
import cec.jiutian.ems.dto.*;
import cec.jiutian.ems.po.EmsLocationTreePO;
import cec.jiutian.ems.query.dto.LocationTreeQueryDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * EMS location tree;位置树表
 *
 * <AUTHOR> CodeGenerator
 * @date : 2023-5-29
 */
@Mapper
public interface EmsLocationTreeMapper extends BaseMapper<EmsLocationTreePO> {

    List<EmsLocationTreeResultDTO> getAllTrees(LocationTreeQueryDTO param);
    List<EmsLocationTreeResultDTO> getLocationTree(LocationTreeQueryDTO param);

    @Select("select max(sort_seq) from ems_location_tree where parent_gid = #{locationPid}")
    Integer getMaxSortSeqByLocationPid(@Param("locationPid") String locationPid);

    @Select("select count(0) from ems_location_tree where parent_gid = #{locationGid}")
    Integer countByParentGid(@Param("locationGid") String locationGid);

    @Select("select count(0) from ems_location_tree where location_name = #{name}")
    Integer countByLocationName(@Param("name") String name);

    List<GetLocationTypeDTO> getLocationType();

    List<GetEnergyTypeDTO> getEnergyType();

    List<GetElectricTopologyDTO> getElectricTopology(GetElectricTopologyDTO getElectricTopologyDTO);

    List<WaterTopologyDTO> getWaterTopologyDTO(  @Param("locationType") String locationType );


}
