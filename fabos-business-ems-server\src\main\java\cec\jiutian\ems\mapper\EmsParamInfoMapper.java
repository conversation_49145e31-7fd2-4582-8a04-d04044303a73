package cec.jiutian.ems.mapper;

import cec.jiutian.core.base.BaseMapper;
import cec.jiutian.ems.dto.GetDeviceParamDTO;
import cec.jiutian.ems.dto.GetDeviceParamResultDTO;
import cec.jiutian.ems.dto.GetParamInfoDTO;
import cec.jiutian.ems.dto.GetUsedParamResultDTO;
import cec.jiutian.ems.po.EmsParamInfoPO;
import cec.jiutian.ems.query.dto.ParamInfoQueryDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * Ems Param Info;模型参数信息表
 *
 * <AUTHOR> CodeGenerator
 * @date : 2023-5-29
 */
@Mapper
public interface EmsParamInfoMapper extends BaseMapper<EmsParamInfoPO> {
    List<EmsParamInfoPO> getParamInfoList(ParamInfoQueryDTO dto);

    List<GetParamInfoDTO> getParamInfoListCreateTime(ParamInfoQueryDTO dto);

    List<EmsParamInfoPO> getParamInfoListByIdList(List<String> list);

    @Select("select count(0) from ems_param_info where param_name = #{name}")
    int getCountByName(@Param("name") String name);

    List<GetDeviceParamResultDTO> getDeviceParamList(GetDeviceParamDTO getDeviceParamDTO);

    List<GetUsedParamResultDTO> getUsedParamList();

    List<EmsParamInfoPO> getUnboundedList(ParamInfoQueryDTO dto);

    List<GetParamInfoDTO> getUnboundedListCreateTime(ParamInfoQueryDTO dto);
}