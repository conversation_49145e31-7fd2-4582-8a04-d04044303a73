package cec.jiutian.ems.mapper;

import cec.jiutian.ems.dto.GetEmsAlarmClassSummaryDTO;
import cec.jiutian.ems.dto.GetEmsAlarmFactorySummaryDTO;
import cec.jiutian.ems.dto.GetEmsAlarmLevelSummaryDTO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * EMS Alarm Message History;报警历史
 * <AUTHOR> CodeGenerator
 * @date : 2023-6-12
 */
@Mapper
public interface EmsRealTimeAlarmMessageMapper {
    GetEmsAlarmLevelSummaryDTO getEmsAlarmLevelSummary();
    GetEmsAlarmClassSummaryDTO getEmsAlarmClassSummary();
    List<GetEmsAlarmFactorySummaryDTO> getEmsAlarmFactorySummary();
}