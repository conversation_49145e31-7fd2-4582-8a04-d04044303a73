package cec.jiutian.ems.mapper;
import cec.jiutian.core.base.BaseMapper;
import cec.jiutian.ems.dto.GetEmsUnitPriceDTO;
import cec.jiutian.ems.dto.GetEmsUnitPriceLineResultDTO;
import cec.jiutian.ems.dto.GetEmsUnitPriceResultDTO;
import cec.jiutian.ems.po.EmsUnitPricePO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 能源单价;
 * <AUTHOR> CodeGenerator
 * @date : 2023-6-19
 */
@Mapper
public interface EmsUnitPriceMapper extends BaseMapper<EmsUnitPricePO> {
    List<GetEmsUnitPriceResultDTO> getEmsUnitPriceList(GetEmsUnitPriceDTO getEmsUnitPriceDTO);
    List<GetEmsUnitPriceLineResultDTO> getEmsUnitPriceLineList(GetEmsUnitPriceDTO getEmsUnitPriceDTO);

    /**
     * 获取当前日期的尖峰平谷，四种单价，包括时间段
     * @return
     */
    List<EmsUnitPricePO> getCurrentDateUnitPrice();

    List<EmsUnitPricePO> getCurrentDateUnitPriceOfWater();

    List<EmsUnitPricePO> getCurrentDateUnitPriceOfGas();
}
