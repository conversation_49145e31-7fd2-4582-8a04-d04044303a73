package cec.jiutian.ems.mapper;

import cec.jiutian.core.base.BaseMapper;
import cec.jiutian.ems.dto.EnergyConsumptionRankingResultDTO;
import cec.jiutian.ems.dto.EnergyConsumptionSimpleResultDTO;
import cec.jiutian.ems.dto.EnergyConsumptionUsageResultDTO;
import cec.jiutian.ems.po.EmsUsedDataDaySummaryPO;
import cec.jiutian.ems.po.EmsUsedDataDaySummaryParamPO;
import cec.jiutian.ems.query.dto.ComprehensiveEnergyConsumptionQueryDTO;
import cec.jiutian.ems.query.dto.EnergyConsumptionByDateRangeQueryDTO;
import cec.jiutian.ems.query.dto.EnergyConsumptionQueryDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.ArrayList;
import java.util.List;

/**
 * Ems Used Data Summary;使用信息汇总表
 *
 * <AUTHOR> CodeGenerator
 * @date : 2023-5-29
 */
@Mapper
public interface EmsUsedDataDaySummaryMapper extends BaseMapper<EmsUsedDataDaySummaryPO> {
    List<EmsUsedDataDaySummaryPO> getSummarizedByDateAndType(@Param("energyType") String energyType,
                                                             @Param("year") Integer year,
                                                             @Param("month") Integer month);

    List<EnergyConsumptionSimpleResultDTO> getSummarizedByDate(@Param("year") Integer year,
                                                               @Param("month") Integer month,
                                                               @Param("day") Integer day);

    List<EmsUsedDataDaySummaryPO> getDataByMonth(@Param("year") Integer year,
                                                 @Param("month") Integer month);

    List<EnergyConsumptionUsageResultDTO> getSummarizedByLocationGidListAndYearMonth(EnergyConsumptionQueryDTO dto);

    List<EnergyConsumptionSimpleResultDTO> getUnitEnergyConsumptionTrend(EnergyConsumptionQueryDTO queryDTO);

    List<EmsUsedDataDaySummaryPO> getSummarizedByDateRange(ComprehensiveEnergyConsumptionQueryDTO queryDTO);

    List<EnergyConsumptionRankingResultDTO> getEnergyConsumptionRankingLvl4(EnergyConsumptionQueryDTO queryDTO);

    List<EnergyConsumptionRankingResultDTO> getEnergyConsumptionRankingLvl3(EnergyConsumptionQueryDTO queryDTO);

    List<EnergyConsumptionRankingResultDTO> getEnergyConsumptionRankingLvl2(EnergyConsumptionQueryDTO queryDTO);

    List<EnergyConsumptionRankingResultDTO> getEnergyConsumptionRankingLvl1(EnergyConsumptionQueryDTO queryDTO);

    List<EnergyConsumptionSimpleResultDTO> getByLocGidAndTypeAndDateRange(EnergyConsumptionByDateRangeQueryDTO queryDto);

    List<EmsUsedDataDaySummaryPO> getByMonthAndTypes(String locationName, Integer year, Integer month, List<String> energyTypeList);

    void dayInsert(@Param("item") EmsUsedDataDaySummaryPO item);

    void dayParamInsert(@Param("item") EmsUsedDataDaySummaryParamPO item);

    List<EmsUsedDataDaySummaryPO> getPointDataByNameAndDate(@Param("list") List<String> list, @Param("year") int year, @Param("month")int month, @Param("day")int day);

    void update(@Param("udsp") EmsUsedDataDaySummaryPO emsUsedDataDaySummaryPO);
}