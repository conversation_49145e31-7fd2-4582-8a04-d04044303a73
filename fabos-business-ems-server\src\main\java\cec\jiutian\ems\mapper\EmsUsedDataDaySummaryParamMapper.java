package cec.jiutian.ems.mapper;

import cec.jiutian.core.base.BaseMapper;
import cec.jiutian.ems.dto.EnergyConsumptionRankingResultDTO;
import cec.jiutian.ems.dto.EnergyConsumptionSimpleResultDTO;
import cec.jiutian.ems.dto.EnergyConsumptionUsageResultDTO;
import cec.jiutian.ems.po.EmsUsedDataDaySummaryPO;
import cec.jiutian.ems.po.EmsUsedDataDaySummaryParamPO;
import cec.jiutian.ems.query.dto.ComprehensiveEnergyConsumptionQueryDTO;
import cec.jiutian.ems.query.dto.EnergyConsumptionByDateRangeQueryDTO;
import cec.jiutian.ems.query.dto.EnergyConsumptionQueryDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Ems Used Data Summary;使用信息汇总表
 *
 * <AUTHOR> CodeGenerator
 * @date : 2023-5-29
 */
@Mapper
public interface EmsUsedDataDaySummaryParamMapper extends BaseMapper<EmsUsedDataDaySummaryParamPO> {

    void dayInsert(@Param("item") EmsUsedDataDaySummaryPO item);

    void dayParamInsert(@Param("item") EmsUsedDataDaySummaryParamPO item);
}