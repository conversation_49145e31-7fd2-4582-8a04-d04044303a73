package cec.jiutian.ems.mapper;

import cec.jiutian.core.base.BaseMapper;
import cec.jiutian.ems.bo.MyBO;
import cec.jiutian.ems.dto.*;
import cec.jiutian.ems.po.EmsUsedDataPO;
import cec.jiutian.ems.po.EmsUsedDataPO2;
import cec.jiutian.ems.po.EmsUsedDataParamPO;
import cec.jiutian.ems.query.dto.ComprehensiveEnergyConsumptionQueryDTO;
import cec.jiutian.ems.query.dto.EnergyConsumptionByDateRangeQueryDTO;
import cec.jiutian.ems.query.dto.EnergyConsumptionQueryDTO;
import cec.jiutian.ems.query.dto.UsedDataQueryDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Ems Used Data;使用数据
 *
 * <AUTHOR> CodeGenerator
 * @date : 2023-5-29
 */
@Mapper
public interface EmsUsedDataMapper extends BaseMapper<EmsUsedDataPO> {

    List<EmsUsedDataPO> getUsedDataByLocId(UsedDataQueryDTO usedDataQueryDTO);

    List<EmsUsedDataPO> getUsedDataByDay(@Param("year") Integer year, @Param("month") Integer month, @Param("day") Integer day);

    List<EnergyConsumptionSimpleResultDTO> getGroupedUsedDataByHours(ComprehensiveEnergyConsumptionQueryDTO usedDataQueryDTO);

    List<EnergyConsumptionSimpleResultDTO> getGroupedUsedDataByHoursAndType(ComprehensiveEnergyConsumptionQueryDTO usedDataQueryDTO);

    List<EnergyConsumptionUsageResultDTO> getGroupedByLocationGidListAndYearMonthDay(EnergyConsumptionQueryDTO dto);

    List<EnergyConsumptionRankingResultDTO> getEnergyConsumptionRankingLvl4(EnergyConsumptionQueryDTO queryDTO);

    List<EnergyConsumptionRankingResultDTO> getEnergyConsumptionRankingLvl3(EnergyConsumptionQueryDTO queryDTO);

    List<EnergyConsumptionRankingResultDTO> getEnergyConsumptionRankingLvl2(EnergyConsumptionQueryDTO queryDTO);

    List<EnergyConsumptionRankingResultDTO> getEnergyConsumptionRankingLvl1(EnergyConsumptionQueryDTO queryDTO);

    List<EmsUsedDataPO> getUseDataByIDAndPoint(UsedDataQueryDTO usedDataQueryDTO);

    List<EmsUsedDataPO> getUsedDataByParamList(UsedDataQueryDTO usedDataQueryDTO);

    List<EnergyConsumptionSimpleResultDTO> getByLocGidAndTypeAndDateRange(EnergyConsumptionByDateRangeQueryDTO queryDto);

    List<RecursiveLocationTreeDTO> getChildrenInfo(UsedDataQueryDTO usedDataQueryDTO);


    List<GetFactoryDTO> getFactory();

    void myInsert(@Param("po") EmsUsedDataPO emsUsedDataPO);

    void newInsert(@Param("list") List<EmsUsedDataPO> list);

    void dayParamBatchInsert(@Param("list") List<EmsUsedDataParamPO> list);

    List<GetEmsUseDataDTO> getPrice(@Param("list") List<MyBO> list);

    List<EmsUsedDataPO> getYesterdayWaterData(@Param("year")int year, @Param("month")int month, @Param("day")int day);

    List<EmsUsedDataPO> getYesterdayGasData(@Param("year")Integer year, @Param("month")Integer month, @Param("day")Integer day);
}
