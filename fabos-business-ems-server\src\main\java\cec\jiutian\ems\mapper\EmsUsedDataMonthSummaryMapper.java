package cec.jiutian.ems.mapper;

import cec.jiutian.core.base.BaseMapper;
import cec.jiutian.ems.dto.EnergyConsumptionRankingResultDTO;
import cec.jiutian.ems.dto.EnergyConsumptionSimpleResultDTO;
import cec.jiutian.ems.dto.EnergyConsumptionUsageResultDTO;
import cec.jiutian.ems.po.EmsUsedDataMonthSummaryPO;
import cec.jiutian.ems.po.EmsUsedDataMonthSummaryParamPO;
import cec.jiutian.ems.query.dto.EnergyConsumptionQueryDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Ems Used Data Summary;使用信息汇总表
 *
 * <AUTHOR> CodeGenerator
 * @date : 2023-5-29
 */
@Mapper
public interface EmsUsedDataMonthSummaryMapper extends BaseMapper<EmsUsedDataMonthSummaryPO> {

    List<EnergyConsumptionSimpleResultDTO> getSummarizedByDateAndType(@Param("energyType") String energyType,
                                                                      @Param("year") Integer year,
                                                                      @Param("month") Integer month);

    List<EnergyConsumptionSimpleResultDTO> getSummarizedByDate(@Param("year") Integer year);

    List<EnergyConsumptionUsageResultDTO> getSummarizedByLocationGidListAndYear(EnergyConsumptionQueryDTO dto);

    List<EnergyConsumptionSimpleResultDTO> getUnitEnergyConsumptionTrend(EnergyConsumptionQueryDTO queryDTO);

    List<EnergyConsumptionRankingResultDTO> getEnergyConsumptionRankingLvl4(EnergyConsumptionQueryDTO queryDTO);

    List<EnergyConsumptionRankingResultDTO> getEnergyConsumptionRankingLvl3(EnergyConsumptionQueryDTO queryDTO);

    List<EnergyConsumptionRankingResultDTO> getEnergyConsumptionRankingLvl2(EnergyConsumptionQueryDTO queryDTO);

    List<EnergyConsumptionRankingResultDTO> getEnergyConsumptionRankingLvl1(EnergyConsumptionQueryDTO queryDTO);

    List<EmsUsedDataMonthSummaryPO> getByYearAndTypes(String locationName, Integer year, List<String> energyTypeList);

    void myInsert(@Param("item") EmsUsedDataMonthSummaryPO item);

    void monthParamInsert(@Param("item") EmsUsedDataMonthSummaryParamPO item);
}