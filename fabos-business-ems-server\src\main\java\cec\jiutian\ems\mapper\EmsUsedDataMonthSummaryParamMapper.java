package cec.jiutian.ems.mapper;

import cec.jiutian.core.base.BaseMapper;
import cec.jiutian.ems.dto.EnergyConsumptionRankingResultDTO;
import cec.jiutian.ems.dto.EnergyConsumptionSimpleResultDTO;
import cec.jiutian.ems.dto.EnergyConsumptionUsageResultDTO;
import cec.jiutian.ems.po.EmsUsedDataMonthSummaryPO;
import cec.jiutian.ems.po.EmsUsedDataMonthSummaryParamPO;
import cec.jiutian.ems.query.dto.EnergyConsumptionQueryDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Ems Used Data Summary;使用信息汇总表
 *
 * <AUTHOR> CodeGenerator
 * @date : 2023-5-29
 */
@Mapper
public interface EmsUsedDataMonthSummaryParamMapper extends BaseMapper<EmsUsedDataMonthSummaryParamPO> {

    void myInsert(@Param("item") EmsUsedDataMonthSummaryPO item);

    void monthParamInsert(@Param("item") EmsUsedDataMonthSummaryParamPO item);
}