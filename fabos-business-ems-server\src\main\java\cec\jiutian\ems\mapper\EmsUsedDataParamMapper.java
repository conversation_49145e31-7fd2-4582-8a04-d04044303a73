package cec.jiutian.ems.mapper;

import cec.jiutian.core.base.BaseMapper;
import cec.jiutian.ems.bo.MyBO;
import cec.jiutian.ems.dto.GetEmsUseDataDTO;
import cec.jiutian.ems.po.EmsUsedDataPO;
import cec.jiutian.ems.po.EmsUsedDataParamPO;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.concurrent.atomic.AtomicReference;

public interface EmsUsedDataParamMapper extends BaseMapper<EmsUsedDataParamPO> {


    void myInsert(@Param("po") EmsUsedDataParamPO EmsUsedDataParamPO);

    List<GetEmsUseDataDTO> getPrice(@Param("list") List<MyBO> list);

}
