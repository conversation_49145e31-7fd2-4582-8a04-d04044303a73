package cec.jiutian.ems.mapper;

import cec.jiutian.core.base.BaseMapper;
import cec.jiutian.ems.query.dto.*;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * Ems Used Data;使用数据
 *
 * <AUTHOR> CodeGenerator
 * @date : 2023-5-29
 */
@Mapper
public interface OverViewMapper extends BaseMapper<EnergyUsageDTO> {
    List<EnergyUsageDTO> getEnergySort(OverViewQueryDTO queryDTO);

    List<EnergyUsageDTO> getFactoryEnergy(OverViewQueryDTO queryDTO);

    List<CarbonEmissionDTO> getCarbonEmission(OverViewQueryDTO queryDTO);

    List<LoadTrendDTO> getTotalLoadTrendYear(OverViewQueryDTO queryDTO);

    List<LoadTrendDTO> getTotalLoadTrendMonth(OverViewQueryDTO queryDTO);

    List<LoadTrendDTO> getTotalLoadTrendDay(OverViewQueryDTO queryDTO);

    List<ConsumptionTrendDTO> getConsumptionTrendYear(OverViewQueryDTO queryDTO);

    List<ConsumptionTrendDTO> getConsumptionTrendMonth(OverViewQueryDTO queryDTO);

    List<ConsumptionTrendDTO> getConsumptionTrendDay(OverViewQueryDTO queryDTO);

    List<EnergyBalanceDTO> getEnergyBalance(OverViewQueryDTO queryDTO);

    Double selTotal();
}
