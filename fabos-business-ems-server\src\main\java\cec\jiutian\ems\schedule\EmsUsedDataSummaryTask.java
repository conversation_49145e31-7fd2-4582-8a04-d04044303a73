
package cec.jiutian.ems.schedule;

import cec.jiutian.core.service.TokenAnalysisService;
import cec.jiutian.ems.bo.MyBO;
import cec.jiutian.ems.dto.GetEmsUseDataDTO;
import cec.jiutian.ems.mapper.EmsChargeSubtotalMapper;
import cec.jiutian.ems.mapper.EmsUnitPriceMapper;
import cec.jiutian.ems.mapper.EmsUsedDataMapper;
import cec.jiutian.ems.po.EmsChargeSubtotalPO;
import cec.jiutian.ems.po.EmsUnitPricePO;
import cec.jiutian.ems.po.EmsUsedDataPO;
import cec.jiutian.ems.po.EmsUsedDataPO2;
import cec.jiutian.ems.service.business.EmsAlarmMessageBizService;
import cec.jiutian.ems.service.business.EmsUsedDataBizService;
import cec.jiutian.ems.service.business.EmsUsedDataDaySummaryBizService;
import cec.jiutian.ems.service.business.EmsUsedDataMonthSummaryBizService;
import cec.jiutian.ems.utils.Assert;
import cn.hutool.core.lang.Snowflake;
import cn.hutool.core.util.IdUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.text.DecimalFormat;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;


@Slf4j
@Component
@EnableScheduling
@AllArgsConstructor
public class EmsUsedDataSummaryTask {

    private final EmsUsedDataBizService emsUsedDataBizService;
    private final EmsUnitPriceMapper mapper;
    private final EmsChargeSubtotalMapper emsChargeSubtotalMapper;
    private final EmsUsedDataMapper emsUsedDataMapper;
    private final TokenAnalysisService tokenAnalysisService;

    private final EmsUsedDataDaySummaryBizService emsUsedDataSummaryBizService;
    private final EmsUsedDataMonthSummaryBizService emsUsedDataMonthSummaryBizService;
    private final EmsAlarmMessageBizService emsAlarmMessageBizService;

    /**
     * 每天0点统计水费用
     * //
     */
    @Scheduled(cron = "0 0 0 * * ?")
//    @Scheduled(cron = "0 0/1 * * * ?")
    public void getWaterPrice() {
        // 令牌 - 核心
        tokenAnalysisService.genToken("60f7363e-ec33-4cf1-a596-54ced9ecfdeb");

        // 查询昨日的时间
        LocalDate currentDate = LocalDate.now().minusDays(1);

        // 获取年、月和日
        Integer year = currentDate.getYear();
        Integer month = currentDate.getMonthValue();
        Integer day = currentDate.getDayOfMonth();

        // 在 data 表中查询昨日的水数据
        List<EmsUsedDataPO> yesterdayWaterData = emsUsedDataMapper.getYesterdayWaterData(year, month, day);

        // 校验
        Assert.notNull("没有昨天的数据呢〰️", yesterdayWaterData);

        // 查询符合日期范围的水费单价
        List<EmsUnitPricePO> waterPrice = mapper.getCurrentDateUnitPriceOfWater();

        // 计算昨日总用水量
        double sum = yesterdayWaterData.stream().mapToDouble(EmsUsedDataPO::getTotalValue).sum();

        // 计算昨日用水总价
        double price = sum * waterPrice.get(0).getUnitPrice();

        // 添加数据
        insertData(currentDate,sum,"waterConsumption",waterPrice,price);
    }

    /**
     * 每天 0 点统计气费
     */
    @Scheduled(cron = "0 0 0 * * ?")
    public void getGasPrice() {

        tokenAnalysisService.genToken("60f7363e-ec33-4cf1-a596-54ced9ecfdeb");

        // 查询昨日的时间
        LocalDate currentDate = LocalDate.now().minusDays(1);

        // 获取年、月和日
        int year = currentDate.getYear();
        int month = currentDate.getMonthValue();
        int day = currentDate.getDayOfMonth();

        // 在 data 表中查询昨日的气数据
        List<EmsUsedDataPO> yesterdayGasData = emsUsedDataMapper.getYesterdayGasData(year, month, day);

        // 校验
        Assert.notNull("没有昨天的数据呢〰️", yesterdayGasData);

        // 查询符合日期范围的气费单价
        List<EmsUnitPricePO> gasPrice = mapper.getCurrentDateUnitPriceOfGas();

        // 计算昨日总用气量
        double sum = yesterdayGasData.stream().mapToDouble(EmsUsedDataPO::getTotalValue).sum();

        // 计算昨日用气总价
        double price = sum * gasPrice.get(0).getUnitPrice();

        // 添加数据
        insertData(currentDate,sum,"gasConsumption",gasPrice,price);
    }

    /**
     * 每天查询0点查询昨日尖峰平谷的用电量等信息
     * //    @Scheduled(cron = "0 0/2 * * * ?")
     */
    @Scheduled(cron = "0 0 0 * * ?")
    public void getPrice() {

        // 查询符合日期范围的尖峰平谷
        List<EmsUnitPricePO> currentDateUnitPrice = mapper.getCurrentDateUnitPrice();

        // 校验
        Assert.notNull("没有创建尖峰平谷时间段", currentDateUnitPrice);

        // 传递给 mapper 的数组，用于 foreach 标签遍历
        List<MyBO> list = new ArrayList<>();

        // 单独处理为 NULL 的，为 NULL 直接添加为 0 的即可
        currentDateUnitPrice.forEach(item -> {
            if(item.getSpecificTimePeriod() == null) {
                LocalDate currentDate = LocalDate.now().minusDays(1);
                EmsChargeSubtotalPO emsChargeSubtotalPO = new EmsChargeSubtotalPO();
                emsChargeSubtotalPO.setYear(currentDate.getYear());
                emsChargeSubtotalPO.setMonth(currentDate.getMonthValue());
                emsChargeSubtotalPO.setDay(currentDate.getDayOfMonth());
                emsChargeSubtotalPO.setDate(java.sql.Date.valueOf(currentDate));
                emsChargeSubtotalPO.setTotalUsage(0.0);
                emsChargeSubtotalPO.setUnitPrice(0.0); //单价
                emsChargeSubtotalPO.setTotalCharge(0.0);
                emsChargeSubtotalPO.setEnergyType("electricityConsumption");
                emsChargeSubtotalPO.setTimePeriod(item.getTimePeriod());
                // 暂不清楚用处
                emsChargeSubtotalPO.setLocationGid("102.1388.000000.000076");
                emsChargeSubtotalMapper.insert(emsChargeSubtotalPO);
            }
        });

        // 过滤掉为 NULL 的，不过滤会空指针异常
        List<EmsUnitPricePO> collect = currentDateUnitPrice.stream().filter(item -> item.getSpecificTimePeriod() != null).collect(Collectors.toList());

        // 将数据库中的日期数据格式化成对象
        collect.forEach(item -> {
            if (item != null) {
                System.out.println();
                String[] split = item.getSpecificTimePeriod().split(",");
                for (String peakTime : split) {
                    String[] split1 = peakTime.split("-");
                    MyBO myBO = new MyBO();
                    myBO.setStart(split1[0]);
                    myBO.setEnd(split1[1]);
                    myBO.setDesc(item.getTimePeriod());
                    list.add(myBO);
                }
            }
        });

        // 分组求和
        List<GetEmsUseDataDTO> price = emsUsedDataBizService.getPrice(list);
        price.forEach(item -> {
            for (EmsUnitPricePO emsUnitPricePO : currentDateUnitPrice) {
                if (item.getDescription().equals(emsUnitPricePO.getTimePeriod())) {
                    // 第二天凌晨更新，记录昨天的日期
                    LocalDate currentDate = LocalDate.now().minusDays(1);
                    EmsChargeSubtotalPO emsChargeSubtotalPO = new EmsChargeSubtotalPO();
                    emsChargeSubtotalPO.setYear(currentDate.getYear());
                    emsChargeSubtotalPO.setMonth(currentDate.getMonthValue());
                    emsChargeSubtotalPO.setDay(currentDate.getDayOfMonth());
                    emsChargeSubtotalPO.setDate(java.sql.Date.valueOf(currentDate));
                    emsChargeSubtotalPO.setTotalUsage(item.getCount());
                    emsChargeSubtotalPO.setUnitPrice(emsUnitPricePO.getUnitPrice()); //单价
                    // 计算总价，保留两位小数
                    DecimalFormat decimalFormat = new DecimalFormat("#.##");
                    String formattedNumber = decimalFormat.format(item.getCount() * emsUnitPricePO.getUnitPrice());
                    double result = Double.parseDouble(formattedNumber);
                    // 修改总价
                    emsChargeSubtotalPO.setTotalCharge(result);
                    emsChargeSubtotalPO.setTimePeriod(item.getDescription());
                    // 暂不清楚用处
                    emsChargeSubtotalPO.setLocationGid("102.1388.000000.000076");
                    emsChargeSubtotalPO.setEnergyType("electricityConsumption");
                    emsChargeSubtotalMapper.insert(emsChargeSubtotalPO);
                }
            }
        });
    }

    /**
     * 月表能耗计算，每小时执行一次
     */
//    @Scheduled(cron = "0 0 */1 * * ?")
    @Scheduled(cron = "0 0 * * * ?")
    public void summarizeUsedData() {
        emsUsedDataSummaryBizService.summarizeUsedData();
    }

    @Scheduled(cron = "0 0 4 1 * ?")
    public void summarizeUsedDataToMonth() {
        emsUsedDataMonthSummaryBizService.summarizeUsedData();
    }

    @Scheduled(cron = "0 0 8 * * ?")
    public Boolean autoAlarmTrigger() {
        return emsAlarmMessageBizService.autoAlarmTrigger();
    }

    private void insertData(LocalDate currentDate, double sum, String energyType,List<EmsUnitPricePO> powerPrice, double price) {
        EmsChargeSubtotalPO emsChargeSubtotalPO = new EmsChargeSubtotalPO();
        emsChargeSubtotalPO.setYear(currentDate.getYear());
        emsChargeSubtotalPO.setMonth(currentDate.getMonthValue());
        emsChargeSubtotalPO.setDay(currentDate.getDayOfMonth());
        emsChargeSubtotalPO.setDate(java.sql.Date.valueOf(currentDate));
        emsChargeSubtotalPO.setTotalUsage(sum);
        emsChargeSubtotalPO.setUnitPrice(powerPrice.get(0).getUnitPrice());
        emsChargeSubtotalPO.setTotalCharge(price);
        emsChargeSubtotalPO.setEnergyType(energyType);
        emsChargeSubtotalPO.setCreateUser("Automatic scheduled tasks");
        emsChargeSubtotalPO.setLastTrxnUser("Automatic scheduled tasks");
        emsChargeSubtotalPO.setCreateTs(java.sql.Date.valueOf(currentDate.plusDays(1)));
        emsChargeSubtotalPO.setLastTrxnTs(java.sql.Date.valueOf(currentDate.plusDays(1)));
        emsChargeSubtotalMapper.insert(emsChargeSubtotalPO);
    }
}
