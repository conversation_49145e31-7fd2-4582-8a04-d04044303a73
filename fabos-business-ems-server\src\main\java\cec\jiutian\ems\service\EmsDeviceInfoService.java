
package cec.jiutian.ems.service;

import cec.jiutian.core.base.BaseDomainService;
import cec.jiutian.ems.domain.EmsDeviceInfo;
import cec.jiutian.ems.mapper.EmsDeviceInfoMapper;
import cec.jiutian.ems.po.EmsDeviceInfoPO;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
/**
 * Ems Device Info;设备信息表
 * <AUTHOR> CodeGenerator
 * @date : 2023-5-29
 */
@Slf4j
@Service
@Transactional
@AllArgsConstructor
public class EmsDeviceInfoService extends BaseDomainService<EmsDeviceInfoPO, EmsDeviceInfo, String> {

    private final EmsDeviceInfoMapper emsDeviceInfoMapper;



}
