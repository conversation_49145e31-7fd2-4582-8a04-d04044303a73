package cec.jiutian.ems.service;

import cec.jiutian.core.base.BaseDomainService;
import cec.jiutian.core.exception.MesErrorCodeException;
import cec.jiutian.ems.domain.EmsIndexSetting;
import cec.jiutian.ems.dto.IndexSettingCreateDTO;
import cec.jiutian.ems.mapper.EmsIndexSettingMapper;
import cec.jiutian.ems.po.EmsIndexSettingPO;
import cec.jiutian.ems.query.dto.EnergyConsumptionIndexQueryDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.weekend.WeekendSqls;

import java.util.List;

/**
 * Ems Index Setting;指标设置表
 *
 * <AUTHOR> CodeGenerator
 * @date : 2023-6-19
 */
@Slf4j
@Service
@Transactional
public class EmsIndexSettingService extends BaseDomainService<EmsIndexSettingPO, EmsIndexSetting, String> {
    private final EmsIndexSettingMapper emsIndexSettingMapper;

    public EmsIndexSettingService(EmsIndexSettingMapper emsIndexSettingMapper) {
        this.emsIndexSettingMapper = emsIndexSettingMapper;
    }

    public void create(IndexSettingCreateDTO opDTO) {
        EmsIndexSetting indexSetting = new EmsIndexSetting();
        indexSetting.create(opDTO);
        checkUniqueColumns(indexSetting);
        indexSetting.save();
    }

    public void update(IndexSettingCreateDTO opDTO) {
        EmsIndexSetting indexSetting = checkExistById(opDTO.getGid());
        indexSetting.create(opDTO);
        indexSetting.update();
        if (getSettingByCondition(indexSetting.getEnergyType(), indexSetting.getLocationTreeGid(), indexSetting.getYear()).size() > 1) {
            throw new MesErrorCodeException("该区域、年份、能源类型，已设定指标，不可重复！");
        }
    }

    public List<EmsIndexSettingPO> getSettingByCondition(String energyType, String locationGid, Integer year) {
        return emsIndexSettingMapper.selectByExample(new Example.Builder(EmsIndexSettingPO.class)
                .where(WeekendSqls.<EmsIndexSettingPO>custom()
                        .andEqualTo(EmsIndexSettingPO::getEnergyType, energyType)
                        .andEqualTo(EmsIndexSettingPO::getLocationTreeGid, locationGid)
                        .andEqualTo(EmsIndexSettingPO::getYear, year)).build());
    }

    public EmsIndexSettingPO getSetting(EnergyConsumptionIndexQueryDTO queryDTO) {
        List<EmsIndexSettingPO> list = emsIndexSettingMapper.selectByExample(new Example.Builder(EmsIndexSettingPO.class)
                .where(WeekendSqls.<EmsIndexSettingPO>custom()
                        .andEqualTo(EmsIndexSettingPO::getEnergyType, queryDTO.getEnergyType())
                        .andEqualTo(EmsIndexSettingPO::getLocationTreeGid, queryDTO.getLocationTreeGid())
                        .andEqualTo(EmsIndexSettingPO::getYear, queryDTO.getYear())).build());
        return CollectionUtils.isNotEmpty(list) ? list.get(0) : null;
    }

    public List<EmsIndexSettingPO> getSettingList(EnergyConsumptionIndexQueryDTO queryDTO) {
        return emsIndexSettingMapper.selectByExample(new Example.Builder(EmsIndexSettingPO.class)
                .where(WeekendSqls.<EmsIndexSettingPO>custom()
                        .andEqualTo(EmsIndexSettingPO::getEnergyType, queryDTO.getEnergyType())
                        .andIn(EmsIndexSettingPO::getLocationTreeGid, queryDTO.getLocationGidList())
                        .andEqualTo(EmsIndexSettingPO::getYear, queryDTO.getYear())).build());
    }
}