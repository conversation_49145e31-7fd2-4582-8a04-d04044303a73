
package cec.jiutian.ems.service;

import cec.jiutian.core.base.BaseDomainService;
import cec.jiutian.core.exception.MesErrorCodeException;
import cec.jiutian.ems.domain.EmsLocationDeviceParamRelation;
import cec.jiutian.ems.domain.EmsLocationTree;
import cec.jiutian.ems.dto.GetRelationshipListDTO;
import cec.jiutian.ems.mapper.EmsLocationDeviceParamRelationMapper;
import cec.jiutian.ems.po.EmsLocationDeviceParamRelationPO;
import cec.jiutian.ems.po.EmsParamInfoPO;
import cec.jiutian.ems.query.dto.LocationDeviceParamRelationQueryDTO;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.weekend.Weekend;
import tk.mybatis.mapper.weekend.WeekendCriteria;

import java.util.List;
import java.util.stream.Collectors;

/**
 * EMS Location Device Param Relation;位置树与测点关系
 *
 * <AUTHOR> CodeGenerator
 * @date : 2023-5-29
 */
@Slf4j
@Service
@Transactional
public class EmsLocationDeviceParamRelationService extends BaseDomainService<EmsLocationDeviceParamRelationPO, EmsLocationDeviceParamRelation, String> {
    private final EmsLocationDeviceParamRelationMapper emsLocationDeviceParamRelationMapper;

    public EmsLocationDeviceParamRelationService(EmsLocationDeviceParamRelationMapper emsLocationDeviceParamRelationMapper) {
        this.emsLocationDeviceParamRelationMapper = emsLocationDeviceParamRelationMapper;
    }

    public Object getRelationshipList(LocationDeviceParamRelationQueryDTO param) {
        startPage(param.getPageNum(), param.getPageSize());
        List<EmsLocationDeviceParamRelationPO> list = emsLocationDeviceParamRelationMapper.getRelationshipList(param);
        PageInfo<EmsLocationDeviceParamRelationPO> pageInfo = new PageInfo<>(list);
        return param.getPageSize() == 0 ? pageInfo.getList() : pageInfo;
    }

    public Object getRelationshipListCreateTime(LocationDeviceParamRelationQueryDTO param) {
        startPage(param.getPageNum(), param.getPageSize());
        List<GetRelationshipListDTO> list = emsLocationDeviceParamRelationMapper.getRelationshipListCreateTime(param);
        PageInfo<GetRelationshipListDTO> pageInfo = new PageInfo<>(list);
        return param.getPageSize() == 0 ? pageInfo.getList() : pageInfo;
    }
    public List<String> getRelationshipByParamAndLocGid(String gid,List<String>  collet) {

        Weekend<EmsLocationDeviceParamRelationPO> weekend=new Weekend<>(EmsLocationDeviceParamRelationPO.class);
        WeekendCriteria<EmsLocationDeviceParamRelationPO, Object> criteria = weekend.weekendCriteria();
        criteria.andEqualTo(EmsLocationDeviceParamRelationPO::getLocationGid, gid).andIn(EmsLocationDeviceParamRelationPO::getParamName,collet.size()==0?null:collet);
        List<EmsLocationDeviceParamRelationPO> emsLocationDeviceParamRelationPOS = emsLocationDeviceParamRelationMapper.selectByExample(weekend);
        List<String> collect = emsLocationDeviceParamRelationPOS.stream().map(x -> x.getParamName()).collect(Collectors.toList());
        return collect;
    }
    public void bindToLocation(EmsLocationTree locationTree, List<EmsParamInfoPO> paramInfoPOS, String bindType) {
        paramInfoPOS.forEach(p -> {
            Integer count = emsLocationDeviceParamRelationMapper.countByParamGid(p.getGid());
            if (count != null && count > 0) {
                throw new MesErrorCodeException("测点" + p.getParamName() + "已经被绑定");
            }
        });
        if (CollectionUtils.isNotEmpty(paramInfoPOS)) {
            List<EmsLocationDeviceParamRelation> assembledRelationships = paramInfoPOS.stream().map(param -> {
                EmsLocationDeviceParamRelation relation = new EmsLocationDeviceParamRelation();
                relation.setFieldsFrom(param);
                relation.setFieldsFrom(locationTree);
                return relation.setBindType(bindType);
            }).collect(Collectors.toList());
            saveBatch(assembledRelationships);
        }
    }

    public void unbindFromLocation(String gid) {
        EmsLocationDeviceParamRelation paramRelation = checkExistById(gid);
        paramRelation.delete();
    }

    public void unbindAllFromLocation(String locationGid) {
        LocationDeviceParamRelationQueryDTO queryDTO = new LocationDeviceParamRelationQueryDTO();
        queryDTO.setLocationGid(locationGid);
        List<EmsLocationDeviceParamRelationPO> relationshipList = emsLocationDeviceParamRelationMapper.getRelationshipList(queryDTO);
        if (CollectionUtils.isNotEmpty(relationshipList)) {
            deleteBatch(relationshipList.stream().map(EmsLocationDeviceParamRelation::new).collect(Collectors.toList()));
        }
    }

    /**
     * check if any location id bound to a parameter info
     *
     * @param paramGid parameter info gid
     * @return true if relationship exists (there is a relationship between parameter info and location)
     */
    public boolean checkBindingExistsByParamGid(String paramGid) {
        return 0 != emsLocationDeviceParamRelationMapper.countByParamGid(paramGid);
    }

    /**
     * check if any location id bound to a parameter info
     *
     * @param locationGid location tree gid
     * @return true if relationship exists (there is a relationship between parameter info and location)
     */
    public boolean checkBindingExistsByLocationGid(String locationGid) {
        return 0 != emsLocationDeviceParamRelationMapper.countByLocationGid(locationGid);
    }


}
