package cec.jiutian.ems.service;

import cec.jiutian.core.base.BaseDomainService;
import cec.jiutian.core.exception.MesErrorCodeException;
import cec.jiutian.ems.domain.EmsLocationTree;
import cec.jiutian.ems.dto.*;
import cec.jiutian.ems.mapper.EmsLocationTreeMapper;
import cec.jiutian.ems.po.EmsLocationTreePO;
import cec.jiutian.ems.query.dto.LocationTreeQueryDTO;
import cec.jiutian.ems.service.business.definition.BusinessDefinition;
import cec.jiutian.ems.utils.Assert;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.weekend.WeekendSqls;

import java.util.*;
import java.util.stream.Collectors;

/**
 * EMS location tree;位置树表
 *
 * <AUTHOR> CodeGenerator
 * @date : 2023-5-29
 */
@Slf4j
@Service
@Transactional
public class EmsLocationTreeService extends BaseDomainService<EmsLocationTreePO, EmsLocationTree, String> {
    private final EmsLocationTreeMapper emsLocationTreeMapper;

    public EmsLocationTreeService(EmsLocationTreeMapper emsLocationTreeMapper) {
        this.emsLocationTreeMapper = emsLocationTreeMapper;
    }

    public List<EmsLocationTreeResultDTO> getLocationTree(LocationTreeQueryDTO param) {

        List<EmsLocationTreeResultDTO> locationTree = emsLocationTreeMapper.getLocationTree(param);
        if (CollectionUtils.isNotEmpty(locationTree)) {
            locationTree = locationTree.stream().peek(EmsLocationTreeResultDTO::setHasChildrenByCount).collect(Collectors.toList());
        }
        return locationTree;
    }

    public void removeLocationTree(LocationTreeModDTO param) {
        this.deleteById(param.getGid());
    }

    public void updateLocationTreeName(LocationTreeModDTO param) {
        EmsLocationTree locationTree = checkExistById(param.getGid());
        if (Objects.isNull(locationTree)) {
            throw new MesErrorCodeException(BusinessDefinition.LocationTreeErrors.LOCATION_GID_NOT_EXISTS);
        } else {
            locationTree.getEntity().setLocationName(param.getLocationName());
            locationTree.update();
        }
    }

    public void saveLocationTree(EmsLocationTreePO po) {
        if (0 != emsLocationTreeMapper.countByLocationName(po.getLocationName())) {
            throw new MesErrorCodeException(BusinessDefinition.LocationTreeErrors.LOCATION_NAME_ALREADY_EXISTS);
        }
        if (Objects.isNull(po.getParentGid())) {
            // 空则说明是创建顶级Location
            po.setParentGid("0");
            po.setLocationLevel("1");
            po.setTopParentGid("0");
        } else {
            EmsLocationTree parentLocation = checkExistById(po.getParentGid());
            po.setLocationLevel(parentLocation.getNextLevelAsString());
            po.setTopParentGid(parentLocation.getTopParentGid());
            switch (parentLocation.getLocationLevel()) {
                case 0:
                    break;
                case 1:
                    po.setLevel1LocationGid(parentLocation.getId());
                    break;
                case 2:
                    po.setLevel2LocationGid(parentLocation.getId());
                    po.setLevel1LocationGid(parentLocation.getParentGid());
                    break;
                case 3:
                    po.setLevel3LocationGid(parentLocation.getId());
                    po.setLevel2LocationGid(parentLocation.getParentGid());
                    EmsLocationTree grandParentLocation = this.getById(parentLocation.getParentGid());
                    po.setLevel1LocationGid(grandParentLocation.getParentGid());
                    break;
                default: // case 4: 大于4级，只填4级
                    po.setLevel4LocationGid(parentLocation.getId());
                    po.setLevel3LocationGid(parentLocation.getParentGid());
                    EmsLocationTree grandParentLocation4 = this.getById(parentLocation.getParentGid());
                    po.setLevel2LocationGid(grandParentLocation4.getParentGid());
                    EmsLocationTree grandGrandParentLocation = this.getById(grandParentLocation4.getParentGid());
                    po.setLevel1LocationGid(grandGrandParentLocation.getParentGid());
                    break;
            }
        }
        Integer maxSortSeq = emsLocationTreeMapper.getMaxSortSeqByLocationPid(po.getParentGid());
        po.setSortSeq(1 + (Objects.isNull(maxSortSeq) ? 0 : maxSortSeq));
        new EmsLocationTree(po).save();
    }

    public void updateSortSeq(LocationTreeModDTO dto) {
        LocationTreeQueryDTO param = new LocationTreeQueryDTO();
        param.setParentGid(dto.getParentGid());
        List<EmsLocationTreeResultDTO> locationTreePOList = getLocationTree(param);
        if (CollectionUtils.isNotEmpty(locationTreePOList)) {
            List<EmsLocationTree> modifiedTrees = locationTreePOList.stream()
                    .map(tree -> {
                        tree.setSortSeq(dto.getSortSeqMap().get(tree.getGid()));
                        return new EmsLocationTree();
                    })
                    .collect(Collectors.toList());
            updateBatch(modifiedTrees);
        }
    }

    public void checkChildNodeExists(String gid) {
        Assert.equalTo("由于存在子节点，无法删除", 0, emsLocationTreeMapper.countByParentGid(gid));
    }

    public Object getAllTrees(LocationTreeQueryDTO param) {
        List<EmsLocationTreeResultDTO> allTrees = emsLocationTreeMapper.getAllTrees(param);
        allTrees.forEach(item -> {
            if(Integer.parseInt(item.getLevel()) <= 2) {
                item.setIsExpanded(true);
            }
        });
        List<EmsLocationTreeResultDTO> resultDTOS = EmsLocationTree.convertTreesWithRecurse(allTrees);



        return resultDTOS;
    }


    public List<EmsLocationTreePO> getByGids(List<String> locationGidList) {
        return emsLocationTreeMapper.selectByExample(new Example.Builder(EmsLocationTreePO.class)
                .where(WeekendSqls.<EmsLocationTreePO>custom()
                        .andIn(EmsLocationTreePO::getGid, locationGidList)).build());
    }

    public Object getTreeChild(LocationTreeQueryDTO param) {

        return null;
    }

    public Object getLocationType() {
        List<GetLocationTypeDTO> result = emsLocationTreeMapper.getLocationType();
        return result;
    }

    public Object getEnergyType() {
        List<GetEnergyTypeDTO> result = emsLocationTreeMapper.getEnergyType();
        return result;
    }

    public List<GetElectricTopologyDTO> getElectricTopology(GetElectricTopologyDTO getElectricTopologyDTO) {
        List<GetElectricTopologyDTO> result = emsLocationTreeMapper.getElectricTopology(getElectricTopologyDTO);
        return result;
    }



    private List<WaterTopologyDTO> getTreeByLocationType(GetWaterTopologyDTO getWaterTopologyDTO) {
        //对应类型的所有数据
        List<WaterTopologyDTO> list = emsLocationTreeMapper.getWaterTopologyDTO(getWaterTopologyDTO.getLocationType());

        // 存储进map结构
        Map<String,WaterTopologyDTO> map = new HashMap<>();
        for (WaterTopologyDTO item : list) {
            map.put(item.getGid() , item );
        }

        // 定义要返回的集合 - 它放的是一级类型
        List<WaterTopologyDTO> result = new ArrayList<>();

        // 循环
        for (WaterTopologyDTO item : list) {
            // 2.1 如果pid=0,将它放入要返回的集合中
            if(item.getPid().equals("0") ){
                result.add(item);
            }else{
                // 从map中取得父类型   时间复杂度:2n次
                WaterTopologyDTO parent = map.get(item.getPid());
                parent.getChildren().add(item);
            }
        }

        System.out.println(result);
        return result;
    }

    public List<WaterTopologyDTO> getMapByLocationType(GetWaterTopologyDTO getWaterTopologyDTO) {
            return getTreeByLocationType(getWaterTopologyDTO);
    }
}
