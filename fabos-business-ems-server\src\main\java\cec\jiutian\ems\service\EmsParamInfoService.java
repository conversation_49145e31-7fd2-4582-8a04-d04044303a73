
package cec.jiutian.ems.service;

import cec.jiutian.core.base.BaseDomainService;
import cec.jiutian.core.exception.MesErrorCodeException;
import cec.jiutian.ems.domain.EmsParamInfo;
import cec.jiutian.ems.dto.GetDeviceParamDTO;
import cec.jiutian.ems.dto.GetDeviceParamResultDTO;
import cec.jiutian.ems.dto.GetParamInfoDTO;
import cec.jiutian.ems.dto.GetUsedParamResultDTO;
import cec.jiutian.ems.mapper.EmsParamInfoMapper;
import cec.jiutian.ems.po.EmsParamInfoPO;
import cec.jiutian.ems.query.dto.ParamInfoQueryDTO;
import cec.jiutian.ems.service.business.definition.BusinessDefinition;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.List;

/**
 * Ems Param Info;模型参数信息表
 *
 * <AUTHOR> CodeGenerator
 * @date : 2023-5-29
 */
@Slf4j
@Service
@Transactional
public class EmsParamInfoService extends BaseDomainService<EmsParamInfoPO, EmsParamInfo, String> {
    private final EmsParamInfoMapper emsParamInfoMapper;

    public EmsParamInfoService(EmsParamInfoMapper emsParamInfoMapper) {
        this.emsParamInfoMapper = emsParamInfoMapper;
    }

    public void saveParamInfo(EmsParamInfoPO po) {
        if (0 < emsParamInfoMapper.getCountByName(po.getParamName())) {
            throw new MesErrorCodeException(BusinessDefinition.ParamInfoErrors.PARAM_NAME_ALREADY_EXISTS);
        }
        new EmsParamInfo(po).save();
    }

    public void updateParamInfo(EmsParamInfoPO po) {
        checkExistById(po.getGid());
        new EmsParamInfo(po).update();
    }

    public void removeParamInfo(EmsParamInfoPO po) {
        new EmsParamInfo(po).delete();
    }

    public Object getParamInfoList(ParamInfoQueryDTO dto) {
        startPage(dto.getPageNum(), dto.getPageSize());
        List<EmsParamInfoPO> list = dto.isNeedUnbounded() ? emsParamInfoMapper.getUnboundedList(dto) : emsParamInfoMapper.getParamInfoList(dto);
        PageInfo<EmsParamInfoPO> pageInfo = new PageInfo<>(list);
        return dto.getPageSize() == 0 ? pageInfo.getList() : pageInfo;
    }

    public Object getParamInfoListCreateTime(ParamInfoQueryDTO dto) {
        startPage(dto.getPageNum(), dto.getPageSize());
        List<GetParamInfoDTO> list = dto.isNeedUnbounded() ? emsParamInfoMapper.getUnboundedListCreateTime(dto) : emsParamInfoMapper.getParamInfoListCreateTime(dto);
        PageInfo<GetParamInfoDTO> pageInfo = new PageInfo<>(list);
        return dto.getPageSize() == 0 ? pageInfo.getList() : pageInfo;
    }

    public List<EmsParamInfoPO> getParamInfoListNotByPage(ParamInfoQueryDTO dto) {
        List<EmsParamInfoPO> list = emsParamInfoMapper.getParamInfoList(dto);
        return list;
    }


    public List<EmsParamInfoPO> getParamInfoListByIdList(List<String> list) {
        return CollectionUtils.isNotEmpty(list)
                ? emsParamInfoMapper.getParamInfoListByIdList(list)
                : Collections.emptyList();
    }

    public Object getDeviceParamList(GetDeviceParamDTO getDeviceParamDTO) {
        startPage(getDeviceParamDTO.getPageNum(), getDeviceParamDTO.getPageSize());
        List<GetDeviceParamResultDTO> list = emsParamInfoMapper.getDeviceParamList(getDeviceParamDTO);
        PageInfo<GetDeviceParamResultDTO> result = new PageInfo<>(list);
        return getDeviceParamDTO.getPageSize() == 0 ? result.getList() : result;
    }

    public List<GetUsedParamResultDTO> getUsedParamList() {
        return emsParamInfoMapper.getUsedParamList();
    }
}
