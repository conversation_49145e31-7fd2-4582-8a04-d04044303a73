package cec.jiutian.ems.service;

import cec.jiutian.ems.dto.GetEmsAlarmClassSummaryDTO;
import cec.jiutian.ems.dto.GetEmsAlarmFactorySummaryDTO;
import cec.jiutian.ems.dto.GetEmsAlarmLevelSummaryDTO;
import cec.jiutian.ems.mapper.EmsRealTimeAlarmMessageMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.ParseException;
import java.util.List;


@Slf4j
@Service
@Transactional
public class EmsRealTimeAlarmMessageService {

    private final EmsRealTimeAlarmMessageMapper emsRealTimeAlarmMessageMapper;

    public EmsRealTimeAlarmMessageService(EmsRealTimeAlarmMessageMapper emsRealTimeAlarmMessageMapper) {
        this.emsRealTimeAlarmMessageMapper = emsRealTimeAlarmMessageMapper;
    }

    public Object getEmsAlarmLevelSummary() throws ParseException {
        GetEmsAlarmLevelSummaryDTO result = emsRealTimeAlarmMessageMapper.getEmsAlarmLevelSummary();
        return result;
    }

    public Object getEmsAlarmClassSummary() throws ParseException {
        GetEmsAlarmClassSummaryDTO result = emsRealTimeAlarmMessageMapper.getEmsAlarmClassSummary();
        return result;
    }

    public Object getEmsAlarmFactorySummary() throws ParseException {
        List<GetEmsAlarmFactorySummaryDTO> result = emsRealTimeAlarmMessageMapper.getEmsAlarmFactorySummary();
        return result;
    }
}
