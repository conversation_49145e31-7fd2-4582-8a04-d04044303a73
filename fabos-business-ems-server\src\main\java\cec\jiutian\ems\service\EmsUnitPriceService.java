package cec.jiutian.ems.service;

import cec.jiutian.core.base.BaseDomainService;
import cec.jiutian.core.comn.util.StringUtils;
import cec.jiutian.core.exception.MesErrorCodeException;
import cec.jiutian.ems.domain.EmsUnitPrice;
import cec.jiutian.ems.dto.EmsUnitPriceCreateDTO;
import cec.jiutian.ems.dto.EmsUnitPriceDeleteDTO;
import cec.jiutian.ems.dto.EmsUnitPriceUpdateDTO;
import cec.jiutian.ems.dto.GetEmsUnitPriceDTO;
import cec.jiutian.ems.dto.GetEmsUnitPriceLineResultDTO;
import cec.jiutian.ems.dto.GetEmsUnitPriceResultDTO;
import cec.jiutian.ems.mapper.EmsUnitPriceMapper;
import cec.jiutian.ems.po.EmsUnitPricePO;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.weekend.WeekendSqls;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 能源单价;
 *
 * <AUTHOR> CodeGenerator
 * @date : 2023-6-19
 */
@Slf4j
@Service
@Transactional
public class EmsUnitPriceService extends BaseDomainService<EmsUnitPricePO, EmsUnitPrice, String> {
    private final EmsUnitPriceMapper emsUnitPriceMapper;

    public EmsUnitPriceService(EmsUnitPriceMapper emsUnitPriceMapper) {
        this.emsUnitPriceMapper = emsUnitPriceMapper;
    }


    public Object getEmsUnitPriceList(GetEmsUnitPriceDTO getEmsUnitPriceDTO) {
        startPage(getEmsUnitPriceDTO.getPageNum(), getEmsUnitPriceDTO.getPageSize());
        List<GetEmsUnitPriceResultDTO> list = emsUnitPriceMapper.getEmsUnitPriceList(getEmsUnitPriceDTO);
        PageInfo<GetEmsUnitPriceResultDTO> result = new PageInfo<>(list);
        return getEmsUnitPriceDTO.getPageSize() == 0 ? result.getList() : result;
    }

    public Boolean create(EmsUnitPriceCreateDTO emsUnitPriceCreateDTO) {
        emsUnitPriceCreateDTO.setEndPriceValidity(DateUtils.addDays(emsUnitPriceCreateDTO.getEndPriceValidity(), -1));
        // 根据类型获取已设定数据，校验有效期是否重合
        List<EmsUnitPricePO> unitPricePOS = getByType(emsUnitPriceCreateDTO.getEnergyTypeCode());
        if (CollectionUtils.isNotEmpty(unitPricePOS)) {
            List<EmsUnitPricePO> list = unitPricePOS.stream().filter(x -> x.getStartPriceValidity().compareTo(emsUnitPriceCreateDTO.getEndPriceValidity()) <= 0 &&
                    x.getEndPriceValidity().compareTo(emsUnitPriceCreateDTO.getStartPriceValidity()) >= 0).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(list)) {
                throw new MesErrorCodeException(emsUnitPriceCreateDTO.getEnergyTypeCode() + "价格有效期重复，请重新设置");
            }
        }

        if (StringUtils.equals(emsUnitPriceCreateDTO.getEnergyTypeCode(), "electricity")) {
            List<EmsUnitPrice> emsUnitPriceList = new ArrayList<>();
            EmsUnitPrice emsUnitPrice = new EmsUnitPrice();
            emsUnitPrice.init(emsUnitPriceCreateDTO);
            StringBuilder stringBuilder = new StringBuilder();
            List<String> pointedList = emsUnitPriceCreateDTO.getPointedList();
            emsUnitPrice.setTimePeriod("尖");
            if (CollectionUtils.isNotEmpty(pointedList)) {
                for (int i = 1; i < pointedList.size(); i++) {
                    stringBuilder.append(pointedList.get(i));
                    if (i != pointedList.size() - 1) {
                        stringBuilder.append(",");
                    }
                }
                emsUnitPrice.setSpecificTimePeriod(stringBuilder.toString());
                emsUnitPrice.setUnitPrice(Double.parseDouble(pointedList.get(0)));
            }
            EmsUnitPrice emsUnitPrice1 = new EmsUnitPrice();
            emsUnitPrice1.init(emsUnitPriceCreateDTO);
            StringBuilder stringBuilder1 = new StringBuilder();
            List<String> peakList = emsUnitPriceCreateDTO.getPeakList();
            emsUnitPrice1.setTimePeriod("峰");
            if (CollectionUtils.isNotEmpty(peakList)) {
                for (int i = 1; i < peakList.size(); i++) {
                    stringBuilder1.append(peakList.get(i));
                    if (i != peakList.size() - 1) {
                        stringBuilder1.append(",");
                    }
                }
                emsUnitPrice1.setSpecificTimePeriod(stringBuilder1.toString());
                emsUnitPrice1.setUnitPrice(Double.parseDouble(peakList.get(0)));
            }
            EmsUnitPrice emsUnitPrice2 = new EmsUnitPrice();
            emsUnitPrice2.init(emsUnitPriceCreateDTO);
            StringBuilder stringBuilder2 = new StringBuilder();
            List<String> flatList = emsUnitPriceCreateDTO.getFlatList();
            emsUnitPrice2.setTimePeriod("平");
            if (CollectionUtils.isNotEmpty(flatList)) {
                for (int i = 1; i < flatList.size(); i++) {
                    stringBuilder2.append(flatList.get(i));
                    if (i != flatList.size() - 1) {
                        stringBuilder2.append(",");
                    }
                }
                emsUnitPrice2.setSpecificTimePeriod(stringBuilder2.toString());
                emsUnitPrice2.setUnitPrice(Double.parseDouble(flatList.get(0)));
            }
            EmsUnitPrice emsUnitPrice3 = new EmsUnitPrice();
            emsUnitPrice3.init(emsUnitPriceCreateDTO);
            StringBuilder stringBuilder3 = new StringBuilder();
            List<String> valleyList = emsUnitPriceCreateDTO.getValleyList();
            emsUnitPrice3.setTimePeriod("谷");
            if (CollectionUtils.isNotEmpty(valleyList)) {
                for (int i = 1; i < valleyList.size(); i++) {
                    stringBuilder3.append(valleyList.get(i));
                    if (i != valleyList.size() - 1) {
                        stringBuilder3.append(",");
                    }
                }
                emsUnitPrice3.setSpecificTimePeriod(stringBuilder3.toString());
                emsUnitPrice3.setUnitPrice(Double.parseDouble(valleyList.get(0)));
            }
            emsUnitPriceList.add(emsUnitPrice);
            emsUnitPriceList.add(emsUnitPrice1);
            emsUnitPriceList.add(emsUnitPrice2);
            emsUnitPriceList.add(emsUnitPrice3);
            this.checkUniqueColumns(emsUnitPrice);
            this.checkUniqueColumns(emsUnitPrice1);
            this.checkUniqueColumns(emsUnitPrice2);
            this.checkUniqueColumns(emsUnitPrice3);
            saveBatch(emsUnitPriceList);
        } else {
            EmsUnitPrice emsUnitPrice = new EmsUnitPrice(new EmsUnitPricePO());
            emsUnitPrice.init(emsUnitPriceCreateDTO);
            emsUnitPrice.setTimePeriod("全部");
            this.checkUniqueColumns(emsUnitPrice);
            emsUnitPrice.save();
        }
        return true;
    }

    public List<EmsUnitPricePO> getByType(String energyTypeCode) {
        return emsUnitPriceMapper.selectByExample(new Example.Builder(EmsUnitPricePO.class)
                .where(WeekendSqls.<EmsUnitPricePO>custom()
                        .andEqualTo(EmsUnitPricePO::getEnergyTypeCode, energyTypeCode)).build());
    }

    public Boolean update(EmsUnitPriceUpdateDTO emsUnitPriceUpdateDTO) {
        EmsUnitPrice emsUnitPrice = checkExistById(emsUnitPriceUpdateDTO.getGid());
        emsUnitPrice.init(emsUnitPriceUpdateDTO);
        if (StringUtils.equals(emsUnitPriceUpdateDTO.getEnergyTypeCode(), "electricity")) {
            StringBuilder stringBuilder = new StringBuilder();
            List<String> electricityList = emsUnitPriceUpdateDTO.getElectricityList();
            if (CollectionUtils.isNotEmpty(electricityList)) {
                for (int i = 0; i < electricityList.size(); i++) {
                    stringBuilder.append(electricityList.get(i));
                    if (i != electricityList.size() - 1) {
                        stringBuilder.append(",");
                    }
                }
                emsUnitPrice.setSpecificTimePeriod(stringBuilder.toString());
            }
        }
        update(emsUnitPrice);
        return true;
    }

    public Boolean delete(EmsUnitPriceDeleteDTO emsUnitPriceDeleteDTO) {
        EmsUnitPrice emsUnitPrice = checkExistById(emsUnitPriceDeleteDTO.getGid());
        delete(emsUnitPrice);
        return true;
    }


    public Object getEmsUnitPriceLineList(GetEmsUnitPriceDTO getEmsUnitPriceDTO) {
        startPage(getEmsUnitPriceDTO.getPageNum(), getEmsUnitPriceDTO.getPageSize());
        List<GetEmsUnitPriceLineResultDTO> list = emsUnitPriceMapper.getEmsUnitPriceLineList(getEmsUnitPriceDTO);
        PageInfo<GetEmsUnitPriceLineResultDTO> result = new PageInfo<>(list);
        return getEmsUnitPriceDTO.getPageSize() == 0 ? result.getList() : result;
    }


}
