
package cec.jiutian.ems.service;

import cec.jiutian.core.base.BaseDomainService;
import cec.jiutian.ems.domain.EmsUsedDataDaySummary;
import cec.jiutian.ems.dto.EnergyConsumptionRankingResultDTO;
import cec.jiutian.ems.dto.EnergyConsumptionSimpleResultDTO;
import cec.jiutian.ems.dto.EnergyConsumptionUsageResultDTO;
import cec.jiutian.ems.mapper.EmsUsedDataDaySummaryMapper;
import cec.jiutian.ems.mapper.EmsUsedDataDaySummaryParamMapper;
import cec.jiutian.ems.po.EmsParamInfoPO;
import cec.jiutian.ems.po.EmsUsedDataDaySummaryPO;
import cec.jiutian.ems.po.EmsUsedDataDaySummaryParamPO;
import cec.jiutian.ems.query.dto.*;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.weekend.Weekend;
import tk.mybatis.mapper.weekend.WeekendCriteria;

import javax.annotation.Resource;
import java.util.Calendar;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Ems Used Data Summary;使用信息汇总表
 *
 * <AUTHOR> CodeGenerator
 * @date : 2023-5-29
 */
@Slf4j
@Service
@Transactional
@AllArgsConstructor
public class EmsUsedDataDaySummaryService extends BaseDomainService<EmsUsedDataDaySummaryPO, EmsUsedDataDaySummary, String> {
    private final EmsUsedDataDaySummaryMapper emsUsedDataSummaryMapper;
    private final  EmsParamInfoService  emsParamInfoService;
    private final  EmsLocationDeviceParamRelationService emsLocationDeviceParamRelationService;

    @Resource
    private EmsUsedDataDaySummaryParamMapper emsUsedDataDaySummaryParamMapper;

    /**
     * 按日期汇总，group by 日期
     */
    public List<EmsUsedDataDaySummaryPO> getSummarizedByDateAndType(String energyType, Integer year, Integer month) {
        return emsUsedDataSummaryMapper.getSummarizedByDateAndType(energyType, year, month);
    }

    public List<EmsUsedDataDaySummaryPO> getSummarizedByDateRange(ComprehensiveEnergyConsumptionQueryDTO queryDTO) {
        return emsUsedDataSummaryMapper.getSummarizedByDateRange(queryDTO);
    }

    public List<EnergyConsumptionSimpleResultDTO> getSummarizedByDate(Integer year, Integer month, Integer day) {
        return emsUsedDataSummaryMapper.getSummarizedByDate(year, month, day);
    }

    public List<EnergyConsumptionSimpleResultDTO> getSummarizedByDate(Integer year, Integer month) {
        return this.getSummarizedByDate(year, month, null);
    }

    public List<EnergyConsumptionUsageResultDTO> getSummarizedByLocationGidListAndYearMonth(EnergyConsumptionQueryDTO dto) {
        return emsUsedDataSummaryMapper.getSummarizedByLocationGidListAndYearMonth(dto);
    }

    public List<EmsUsedDataDaySummaryPO> getThisMonthAllData() {

        Calendar cal = Calendar.getInstance();
        return emsUsedDataSummaryMapper.getDataByMonth(cal.get(Calendar.YEAR), cal.get(Calendar.MONTH) + 1);
    }

    public List<EmsUsedDataDaySummaryPO> getByMonthAndTypes(String locationName, Integer year, Integer month, List<String> energyTypeList) {
//        Example example = new Example(EmsUsedDataDaySummaryPO.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("year", year)
//                .andEqualTo("month", month)
//                .andIn("paramType", energyTypeList);
        return emsUsedDataSummaryMapper.getByMonthAndTypes(locationName,year,month,energyTypeList);
    }

    public List<EnergyConsumptionSimpleResultDTO> getUnitEnergyConsumptionTrend(EnergyConsumptionQueryDTO queryDTO) {
        return emsUsedDataSummaryMapper.getUnitEnergyConsumptionTrend(queryDTO);
    }

    public List<EnergyConsumptionRankingResultDTO> getEnergyConsumptionRankingLvl4(EnergyConsumptionQueryDTO queryDTO) {
        return emsUsedDataSummaryMapper.getEnergyConsumptionRankingLvl4(queryDTO);
    }

    public List<EnergyConsumptionRankingResultDTO> getEnergyConsumptionRankingLvl3(EnergyConsumptionQueryDTO queryDTO) {
        return emsUsedDataSummaryMapper.getEnergyConsumptionRankingLvl3(queryDTO);
    }

    public List<EnergyConsumptionRankingResultDTO> getEnergyConsumptionRankingLvl2(EnergyConsumptionQueryDTO queryDTO) {
        return emsUsedDataSummaryMapper.getEnergyConsumptionRankingLvl2(queryDTO);
    }

    public List<EnergyConsumptionRankingResultDTO> getEnergyConsumptionRankingLvl1(EnergyConsumptionQueryDTO queryDTO) {
        return emsUsedDataSummaryMapper.getEnergyConsumptionRankingLvl1(queryDTO);
    }

    public List<EnergyConsumptionSimpleResultDTO> getByLocGidAndTypeAndDateRange(EnergyConsumptionByDateRangeQueryDTO queryDto) {
        return emsUsedDataSummaryMapper.getByLocGidAndTypeAndDateRange(queryDto);
    }

    public Object getUseDataByMonth(UsedDataQueryDTO usedDataQueryDTO) {
        // 获取当前日期的实例
        Calendar calendar = Calendar.getInstance();

        // 当前日期+0 无用代码
        calendar.add(Calendar.DAY_OF_MONTH,0);

        // 原始代码
        //usedDataQueryDTO.setYear(calendar.get(Calendar.YEAR)).setMonth(calendar.get(Calendar.MONTH)+1).setDay(calendar.get(Calendar.DAY_OF_MONTH));

        // 设置年月日
        String pointWeekOrMouth = usedDataQueryDTO.getPointWeekOrMouth();
        String[] split = pointWeekOrMouth.split("-");
        usedDataQueryDTO.setYear(Integer.parseInt(split[0]))
                .setMonth(Integer.parseInt(split[1]))
                .setDay(Integer.parseInt(split[2]));

        // setKey
        setKey(usedDataQueryDTO);

        // 为空就返回
        if(usedDataQueryDTO.getKeys()==null || usedDataQueryDTO.getKeys().isEmpty()) {
            return null;
        }

        // 构造MP
        Weekend<EmsUsedDataDaySummaryParamPO> weekend=new Weekend<>(EmsUsedDataDaySummaryParamPO.class);
        WeekendCriteria<EmsUsedDataDaySummaryParamPO, Object> criteria = weekend.weekendCriteria();

        // 构造where条件
        criteria.andEqualTo(EmsUsedDataDaySummaryParamPO::getParamName,usedDataQueryDTO.getParamName());
        criteria.andEqualTo(EmsUsedDataDaySummaryParamPO::getYear,usedDataQueryDTO.getYear());
        criteria.andEqualTo(EmsUsedDataDaySummaryParamPO::getMonth,usedDataQueryDTO.getMonth());
//        criteria.andEqualTo(EmsUsedDataDaySummaryParamPO::getParamName,usedDataQueryDTO.getMonth());
        weekend.setOrderByClause("used_date asc");

        // 查询
        List<EmsUsedDataDaySummaryParamPO> emsUsedDataDaySummaryPOS = emsUsedDataDaySummaryParamMapper.selectByExample(weekend);
        emsUsedDataDaySummaryPOS.forEach(item -> {
            item.setTimeStr( item.getDay().toString() + '日');
        });
        return emsUsedDataDaySummaryPOS;
    }


    public void setKey(UsedDataQueryDTO usedDataQueryDTO)
    {
        List<EmsParamInfoPO> paramInfoList = emsParamInfoService.getParamInfoListNotByPage(new ParamInfoQueryDTO().setParamType(usedDataQueryDTO.getPointGroup()));
        List<String> collect = paramInfoList.stream().map(x -> x.getParamName()).collect(Collectors.toList());
        if (collect != null&&collect.size()!=0) {
            List<String> keys=  emsLocationDeviceParamRelationService.getRelationshipByParamAndLocGid(usedDataQueryDTO.getGid(),collect);
            usedDataQueryDTO.setKeys(keys);

        }
    }


}
