package cec.jiutian.ems.service;

import cec.jiutian.core.base.BaseDomainService;
import cec.jiutian.ems.domain.EmsUsedDataMonthSummary;
import cec.jiutian.ems.dto.EnergyConsumptionRankingResultDTO;
import cec.jiutian.ems.dto.EnergyConsumptionSimpleResultDTO;
import cec.jiutian.ems.dto.EnergyConsumptionUsageResultDTO;
import cec.jiutian.ems.mapper.EmsUsedDataMonthSummaryMapper;
import cec.jiutian.ems.mapper.EmsUsedDataMonthSummaryParamMapper;
import cec.jiutian.ems.po.EmsParamInfoPO;
import cec.jiutian.ems.po.EmsUsedDataMonthSummaryPO;
import cec.jiutian.ems.po.EmsUsedDataMonthSummaryParamPO;
import cec.jiutian.ems.query.dto.EnergyConsumptionQueryDTO;
import cec.jiutian.ems.query.dto.ParamInfoQueryDTO;
import cec.jiutian.ems.query.dto.UsedDataQueryDTO;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.weekend.Weekend;
import tk.mybatis.mapper.weekend.WeekendCriteria;
import tk.mybatis.mapper.weekend.WeekendSqls;

import javax.annotation.Resource;
import java.util.Calendar;
import java.util.List;
import java.util.stream.Collectors;


@Slf4j
@Service
@Transactional
@Data
public class EmsUsedDataMonthSummaryService extends BaseDomainService<EmsUsedDataMonthSummaryPO, EmsUsedDataMonthSummary, String> {

    private final EmsUsedDataMonthSummaryMapper emsUsedDataMonthSummaryMapper;
    private final  EmsParamInfoService  emsParamInfoService;
    private final  EmsLocationDeviceParamRelationService emsLocationDeviceParamRelationService;

    @Resource
    private EmsUsedDataMonthSummaryParamMapper emsUsedDataMonthSummaryParamMapper;

    public List<EnergyConsumptionSimpleResultDTO> getSummarizedByDateAndType(String energyType, Integer year, Integer month) {
        return emsUsedDataMonthSummaryMapper.getSummarizedByDateAndType(energyType, year, month);
    }

    public List<EnergyConsumptionSimpleResultDTO> getSummarizedByDate(Integer year) {
        return emsUsedDataMonthSummaryMapper.getSummarizedByDate(year);
    }

    public List<EnergyConsumptionUsageResultDTO> getSummarizedByLocationGidListAndYear(EnergyConsumptionQueryDTO dto) {
        return emsUsedDataMonthSummaryMapper.getSummarizedByLocationGidListAndYear(dto);
    }

    public List<EnergyConsumptionSimpleResultDTO> getUnitEnergyConsumptionTrend(EnergyConsumptionQueryDTO queryDTO) {
        return emsUsedDataMonthSummaryMapper.getUnitEnergyConsumptionTrend(queryDTO);
    }

    public List<EmsUsedDataMonthSummaryPO> getByYearAndTypes(String locationName, Integer year, List<String> energyTypeList) {
//        Example example = new Example(EmsUsedDataMonthSummaryPO.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("year", year)
//                .andIn("paramType", energyTypeList);
//        return emsUsedDataMonthSummaryMapper.selectByExample(example);
        return emsUsedDataMonthSummaryMapper.getByYearAndTypes(locationName,year,energyTypeList);

    }
    public List<EnergyConsumptionRankingResultDTO> getEnergyConsumptionRankingLvl4(EnergyConsumptionQueryDTO queryDTO) {
        return emsUsedDataMonthSummaryMapper.getEnergyConsumptionRankingLvl4(queryDTO);
    }

    public List<EnergyConsumptionRankingResultDTO> getEnergyConsumptionRankingLvl3(EnergyConsumptionQueryDTO queryDTO) {
        return emsUsedDataMonthSummaryMapper.getEnergyConsumptionRankingLvl3(queryDTO);
    }

    public List<EnergyConsumptionRankingResultDTO> getEnergyConsumptionRankingLvl2(EnergyConsumptionQueryDTO queryDTO) {
        return emsUsedDataMonthSummaryMapper.getEnergyConsumptionRankingLvl2(queryDTO);
    }

    public List<EnergyConsumptionRankingResultDTO> getEnergyConsumptionRankingLvl1(EnergyConsumptionQueryDTO queryDTO) {
        return emsUsedDataMonthSummaryMapper.getEnergyConsumptionRankingLvl1(queryDTO);
    }

    public List<EmsUsedDataMonthSummaryPO> getByYearAndTypeAndLocations(String energyType, Integer year, List<String> locationGidList) {
        return emsUsedDataMonthSummaryMapper.selectByExample(new Example.Builder(EmsUsedDataMonthSummaryPO.class)
                .where(WeekendSqls.<EmsUsedDataMonthSummaryPO>custom()
                        .andEqualTo(EmsUsedDataMonthSummaryPO::getParamType, energyType)
                        .andIn(EmsUsedDataMonthSummaryPO::getLocationTreeGid, locationGidList)
                        .andEqualTo(EmsUsedDataMonthSummaryPO::getYear, year)).build());
    }

    public Object getUseDataByYear(UsedDataQueryDTO usedDataQueryDTO) {

        // 获取当前时间的Calendar实例
        Calendar calendar = Calendar.getInstance();

        // 这一行将日期加0天 毫无作用
        calendar.add(Calendar.DAY_OF_MONTH,0);

        // 设置dto中的参数
        // 原始代码： usedDataQueryDTO.setYear(calendar.get(Calendar.YEAR)).setMonth(calendar.get(Calendar.MONTH)+1).setDay(calendar.get(Calendar.DAY_OF_MONTH));
        usedDataQueryDTO.setYear(usedDataQueryDTO.getYear())
                .setMonth(null)
                .setDay(null);
        setKey(usedDataQueryDTO);
        if(usedDataQueryDTO.getKeys()==null||usedDataQueryDTO.getKeys().size()==0) {
            return null;
        }

        // 用于构建 MyBatis 查询条件。EmsUsedDataMonthSummaryPO.class 是查询的目标实体类。
        Weekend<EmsUsedDataMonthSummaryParamPO> weekend=new Weekend<>(EmsUsedDataMonthSummaryParamPO.class);
        WeekendCriteria<EmsUsedDataMonthSummaryParamPO, Object> criteria = weekend.weekendCriteria();

        // 具体查询条件
        criteria.andEqualTo(EmsUsedDataMonthSummaryParamPO::getParamName,usedDataQueryDTO.getParamName());
        criteria.andEqualTo(EmsUsedDataMonthSummaryParamPO::getYear,Integer.parseInt(usedDataQueryDTO.getPointYear()));
        weekend.setOrderByClause("month asc");
//        List<EmsUsedDataMonthSummaryPO> emsUsedDataDaySummaryPOS = emsUsedDataMonthSummaryMapper.selectByExample(weekend);
        List<EmsUsedDataMonthSummaryParamPO> emsUsedDataDaySummaryPOS = emsUsedDataMonthSummaryParamMapper.selectByExample(weekend);
        emsUsedDataDaySummaryPOS.forEach(item -> {
            item.setTimeStr( item.getMonth().toString() + '月');
        });
        return emsUsedDataDaySummaryPOS;
    }

    public void setKey(UsedDataQueryDTO usedDataQueryDTO)
    {
        List<EmsParamInfoPO> paramInfoList = emsParamInfoService.getParamInfoListNotByPage(new ParamInfoQueryDTO().setParamType(usedDataQueryDTO.getPointGroup()));
        List<String> collect = paramInfoList.stream().map(x -> x.getParamName()).collect(Collectors.toList());
        if (collect != null&&collect.size()!=0) {
            List<String> keys=  emsLocationDeviceParamRelationService.getRelationshipByParamAndLocGid(usedDataQueryDTO.getGid(),collect);
            usedDataQueryDTO.setKeys(keys);

        }
    }


}
