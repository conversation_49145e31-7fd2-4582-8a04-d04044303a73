
package cec.jiutian.ems.service;

import cec.jiutian.core.base.BaseDomainService;
import cec.jiutian.ems.bo.MyBO;
import cec.jiutian.ems.domain.EmsChargeSubtotal;
import cec.jiutian.ems.domain.EmsUsedData;
import cec.jiutian.ems.dto.*;
import cec.jiutian.ems.mapper.EmsUsedDataDaySummaryMapper;
import cec.jiutian.ems.mapper.EmsUsedDataMapper;
import cec.jiutian.ems.mapper.EmsUsedDataMonthSummaryMapper;
import cec.jiutian.ems.mapper.OverViewMapper;
import cec.jiutian.ems.po.*;
import cec.jiutian.ems.query.dto.CarbonEmissionDTO;
import cec.jiutian.ems.query.dto.ComprehensiveEnergyConsumptionQueryDTO;
import cec.jiutian.ems.query.dto.ConsumptionTrendDTO;
import cec.jiutian.ems.query.dto.EnergyBalanceDTO;
import cec.jiutian.ems.query.dto.EnergyConsumptionByDateRangeQueryDTO;
import cec.jiutian.ems.query.dto.EnergyConsumptionQueryDTO;
import cec.jiutian.ems.query.dto.EnergyUsageDTO;
import cec.jiutian.ems.query.dto.LoadTrendDTO;
import cec.jiutian.ems.query.dto.OverViewQueryDTO;
import cec.jiutian.ems.query.dto.UsedDataQueryDTO;
import cec.jiutian.ems.utils.DateUtils;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.xmlbeans.impl.xb.xsdschema.Public;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.wiring.BeanWiringInfo;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.text.DecimalFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.YearMonth;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Ems Used Data;使用数据
 *
 * <AUTHOR> CodeGenerator
 * @date : 2023-5-29
 */
@Slf4j
@Service
@Transactional
@AllArgsConstructor
public class EmsUsedDataService extends BaseDomainService<EmsUsedDataPO, EmsUsedData, String> {
    private final EmsUsedDataMapper emsUsedDataMapper;
    private final OverViewMapper overViewMapper;

    @Resource
    private EmsUsedDataDaySummaryMapper emsUsedDataDaySummaryMapper;

    @Resource
    private EmsUsedDataMonthSummaryMapper emsUsedDataMonthSummaryMapper;


    public List<EnergyUsageDTO> getEnergySort(OverViewQueryDTO queryDTO) {
        return overViewMapper.getEnergySort(queryDTO);
    }

    public List<EnergyUsageDTO> getFactoryEnergy(OverViewQueryDTO queryDTO) {
        return overViewMapper.getFactoryEnergy(queryDTO);
//        try {
//            List<EnergyUsageDTO> factoryEnergy = overViewMapper.getFactoryEnergy(queryDTO);
//            double i = overViewMapper.selTotal();
//            factoryEnergy.get(0).setElectricityConsumption(i);
//            return factoryEnergy;
//        } catch (Exception e) {
//            System.out.println(e);
//        }
//        return new ArrayList<EnergyUsageDTO>();
    }

    public List<CarbonEmissionDTO> getCarbonEmission(OverViewQueryDTO queryDTO) {
        return overViewMapper.getCarbonEmission(queryDTO);
    }

    public List<LoadTrendDTO> getTotalLoadTrendYear(OverViewQueryDTO queryDTO) {
        return overViewMapper.getTotalLoadTrendYear(queryDTO);

    }

    public List<LoadTrendDTO> getTotalLoadTrendMonth(OverViewQueryDTO queryDTO) {
        return overViewMapper.getTotalLoadTrendMonth(queryDTO);
    }

    public List<LoadTrendDTO> getTotalLoadTrendDay(OverViewQueryDTO queryDTO) {
        return overViewMapper.getTotalLoadTrendDay(queryDTO);
    }

    public List<ConsumptionTrendDTO> getConsumptionTrendYear(OverViewQueryDTO queryDTO) {
        return overViewMapper.getConsumptionTrendYear(queryDTO);
    }

    public List<ConsumptionTrendDTO> getConsumptionTrendMonth(OverViewQueryDTO queryDTO) {
        return overViewMapper.getConsumptionTrendMonth(queryDTO);
    }

    public List<ConsumptionTrendDTO> getConsumptionTrendDay(OverViewQueryDTO queryDTO) {
        return overViewMapper.getConsumptionTrendDay(queryDTO);
    }

    public List<EnergyBalanceDTO> getEnergyBalance(OverViewQueryDTO queryDTO) {
        return overViewMapper.getEnergyBalance(queryDTO);
    }

    public List<UsedDataOutDTO> getUsedDataByLocId(UsedDataQueryDTO usedDataQueryDTO) {
        List<EmsUsedDataPO> convertList = emsUsedDataMapper.getUsedDataByLocId(usedDataQueryDTO);
        List<UsedDataOutDTO> usedDataOutDTOS = EmsUsedData.poConvertToOutDtoByDeviceName(convertList, usedDataQueryDTO);
        return usedDataOutDTOS;
    }

    public List<EnergyConsumptionSimpleResultDTO> getGroupedUsedDataByHours(ComprehensiveEnergyConsumptionQueryDTO usedDataQueryDTO) {
        return emsUsedDataMapper.getGroupedUsedDataByHours(usedDataQueryDTO);
    }

    public List<EnergyConsumptionSimpleResultDTO> getGroupedUsedDataByHoursAndType(ComprehensiveEnergyConsumptionQueryDTO usedDataQueryDTO) {
        return emsUsedDataMapper.getGroupedUsedDataByHoursAndType(usedDataQueryDTO);
    }

    public List<EmsUsedDataPO> getTodayAllUsedData(int year, int month, int day, int offset) {
//        Calendar calendar = Calendar.getInstance();
//        calendar.add(Calendar.DAY_OF_MONTH, offset);
//        return emsUsedDataMapper.getUsedDataByDay(calendar.get(Calendar.YEAR), calendar.get(Calendar.MONTH) + 1, calendar.get(Calendar.DAY_OF_MONTH));
        return emsUsedDataMapper.getUsedDataByDay(year, month, day);
    }

    public List<EmsUsedDataPO> getTodayAllUsedData(int year, int month, int day) {
        return this.getTodayAllUsedData(year, month, day, 0);
    }

    public List<EnergyConsumptionUsageResultDTO> getGroupedByLocationGidListAndYearMonthDay(EnergyConsumptionQueryDTO dto) {
        return emsUsedDataMapper.getGroupedByLocationGidListAndYearMonthDay(dto);
    }

    public List<EnergyConsumptionRankingResultDTO> getEnergyConsumptionRankingLvl4(EnergyConsumptionQueryDTO queryDTO) {
        return emsUsedDataMapper.getEnergyConsumptionRankingLvl4(queryDTO);
    }

    public List<EnergyConsumptionRankingResultDTO> getEnergyConsumptionRankingLvl3(EnergyConsumptionQueryDTO queryDTO) {
        return emsUsedDataMapper.getEnergyConsumptionRankingLvl3(queryDTO);
    }

    public List<EnergyConsumptionRankingResultDTO> getEnergyConsumptionRankingLvl2(EnergyConsumptionQueryDTO queryDTO) {
        return emsUsedDataMapper.getEnergyConsumptionRankingLvl2(queryDTO);
    }

    public List<EnergyConsumptionRankingResultDTO> getEnergyConsumptionRankingLvl1(EnergyConsumptionQueryDTO queryDTO) {
        return emsUsedDataMapper.getEnergyConsumptionRankingLvl1(queryDTO);
    }

    public List<EmsUsedDataPO> getUseDataByIDAndPoint(UsedDataQueryDTO usedDataQueryDTO) {
        //Calendar calendar = Calendar.getInstance();
        //calendar.add(Calendar.DAY_OF_MONTH,0);
        String[] array = usedDataQueryDTO.getPointDay().split("-");
        usedDataQueryDTO.setYear(Integer.parseInt(array[0])).setMonth(Integer.parseInt(array[1])).setDay(Integer.parseInt(array[2]));

        if (usedDataQueryDTO.getTimeDensity().equals("halfHour")) {
            usedDataQueryDTO.setTimeTypeList(new ArrayList<>()).getTimeTypeList().add("30");
        }
        if (usedDataQueryDTO.getTimeDensity().equals("hour")) {
            usedDataQueryDTO.setTimeTypeList(new ArrayList<>()).getTimeTypeList().add("60");
        }
        if (usedDataQueryDTO.getTimeDensity().equals("quarterHour")) {
            usedDataQueryDTO.setTimeTypeList(new ArrayList<>()).getTimeTypeList().add("15");
        }
        if (usedDataQueryDTO.getKeys() == null || usedDataQueryDTO.getKeys().size() == 0) {
            return null;
        }
        return emsUsedDataMapper.getUseDataByIDAndPoint(usedDataQueryDTO);

    }

    public List<UsedDataOutDTO> getUsedDataByParamList(UsedDataQueryDTO usedDataQueryDTO) {
        List<EmsUsedDataPO> convertList = emsUsedDataMapper.getUsedDataByParamList(usedDataQueryDTO);
        List<UsedDataOutDTO> usedDataOutDTOS = EmsUsedData.poConvertToOutDtoByDeviceName(convertList, usedDataQueryDTO);
        return usedDataOutDTOS;
    }

    public List<EnergyConsumptionSimpleResultDTO> getByLocGidAndTypeAndDateRange(EnergyConsumptionByDateRangeQueryDTO queryDto) {
        return emsUsedDataMapper.getByLocGidAndTypeAndDateRange(queryDto);
    }

    public List<String> getChildrenInfo(UsedDataQueryDTO usedDataQueryDTO) {

        List<RecursiveLocationTreeDTO> re = emsUsedDataMapper.getChildrenInfo(usedDataQueryDTO);
        List<String> childrens = re.stream().map(x -> x.getGid()).collect(Collectors.toList());

        List<String> parentGids = re.stream().map(x -> x.getParentGid()).collect(Collectors.toList());
        List<String> resultList = new ArrayList<>();
        childrens.forEach(x ->
        {
            Boolean isIn = false;
            for (int i = 0; i < parentGids.size(); i++) {
                if (x.equals(parentGids.get(i))) {
                    isIn = true;
                }
            }
            if (isIn == false) resultList.add(x);
        });

        return resultList.size() == 0 ? null : resultList;
    }

    public Object getFactory() {
        List<GetFactoryDTO> result = emsUsedDataMapper.getFactory();
        return result;
    }

    /**
     * 使用项目 724
     * 自动生成测点数据
     */
    public void myInsert() {

        String pointName = "WaferFabrication-587-a";  // 测点名称
        String selgid = "102.1101.000000.000103";          // 测点父级的 gid
        String deviceName = "WaferFabrication";                    // 父级名称
        String type = "electricityConsumption";
        String unit = "A";  // 单位
        double minValueParam = 7.6;  // 最小
        double maxValueParam = 18.2; // 最大

//        double minValueParam = 8.2;  // 最小
//        double maxValueParam = 15.4; // 最大

        // 初始日期 --核心
        LocalDateTime startTime = LocalDateTime.of(2023, 12, 1, 0, 0);

        // 截止日期 不会到 13号
        LocalDateTime endTime = LocalDateTime.of(2023, 12, 25, 0, 0);

        Double minDay15 = null;
        Double maxDay15 = null;
        double avgDay15 = 0.0;  // 15分钟平均值
        Double minMonth = null;
        Double maxMonth = null;
        double avgMonth = 0.0;  // 一个月平均值
        Double minYear = null;
        Double maxYear = null;
        double avgYear = 0.0;   // 一年平均值

        Double minDay30 = null;
        Double maxDay30 = null;
        double avgDay30 = 0.0;  // 30分钟平均值

        Double minDay60 = null;
        Double maxDay60 = null;
        double avgDay60 = 0.0;  // 60分钟平均值

        // 一批添加的use_data数据集合
        List<EmsUsedDataParamPO> maxCountList = new ArrayList<>();

        // 日期格式
//        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        // 刚到第15分钟 0 - 14 分钟
        LocalDateTime endTimeDay15 = startTime.plusMinutes(15).minusMinutes(1);

        LocalDateTime endTimeDay30 = startTime.plusMinutes(30).minusMinutes(1);

        LocalDateTime endTimeDay60 = startTime.plusMinutes(60).minusMinutes(1);

        LocalDateTime endTimeMonth = startTime.plusDays(1).minusMinutes(1);

        int yearCount = 0;
        LocalDateTime endTimeYear = startTime.plusMonths(1).withDayOfMonth(1).withHour(0).withMinute(0).withSecond(0).minusMinutes(1);

        // 初始日期没到截止日期 就一直循环添加... 到了就跳出循环 59结束
        while (startTime.isBefore(endTime)) {

            // 随机值
            double v = Math.random() * (maxValueParam - minValueParam) + minValueParam; // =================================================================================

            // 拆分日期
//            String[] split = startTime.format(formatter).split(" ");
//            String[] split1 = split[0].split("-");
//            String[] split2 = split[1].split(":");

            // 获取日期
            Date date = Date.from(startTime.atZone(ZoneId.systemDefault()).toInstant());

            avgDay15 += v;
            avgYear += v;

            // 找出 min max
            if (minDay15 == null || minDay15 > v) {
                minDay15 = v;
            }
            if (minDay30 == null || minDay30 > v) {
                minDay30 = v;
            }
            if (minDay60 == null || minDay60 > v) {
                minDay60 = v;
            }
            if (maxDay15 == null || maxDay15 < v) {
                maxDay15 = v;
            }
            if (maxDay30 == null || maxDay30 < v) {
                maxDay30 = v;
            }
            if (maxDay60 == null || maxDay60 < v) {
                maxDay60 = v;
            }
            if (maxMonth == null || maxMonth < v) {
                maxMonth = v;
            }
            if (maxYear == null || maxYear < v) {
                maxYear = v;
            }
            if (minYear == null || minYear > v) {
                minYear = v;
            }
            if (minMonth == null || minMonth > v) {
                minMonth = v;
            }

            // 创建 usedata 对象
            EmsUsedDataParamPO po = new EmsUsedDataParamPO();
            po.setUsedDate(date);
            po.setTriggerTime(date);
            po.setMyTimeType(0);
            po.setParamName(pointName);
            po.setMinValue(v);
            po.setMaxValue(v);
            po.setAverageValue(v);
            po.setTotalValue(po.getAverageValue());
            po.setYear(startTime.getYear());
            po.setMonth(startTime.getMonthValue());
            po.setDay(startTime.getDayOfMonth());
            po.setHour(startTime.getHour());
            po.setLocationGid(selgid);
            po.setCreateTs(date);
            po.setCreateUser("hefq");
            po.setTimeStr(startTime.getHour() + ":" + startTime.getMinute());
            po.setDeviceName(deviceName);
            po.setUnit(unit);

            // 加入集合，准备批量添加
            maxCountList.add(po);

            // 十五分钟后
            if (startTime.isEqual(endTimeDay15)) {

                avgDay30 += avgDay15;

                EmsUsedDataParamPO emsUsedDataParamPO = new EmsUsedDataParamPO();
                emsUsedDataParamPO.setMinValue(minDay15);
                minDay15 = null;
                emsUsedDataParamPO.setMaxValue(maxDay15);
                maxDay15 = null;
                emsUsedDataParamPO.setAverageValue(avgDay15 / 15);
                emsUsedDataParamPO.setTotalValue(avgDay15);
                avgDay15 = 0.0;
                emsUsedDataParamPO.setMyTimeType(15);
                emsUsedDataParamPO.setUsedDate(date);
                emsUsedDataParamPO.setTriggerTime(date);
                emsUsedDataParamPO.setParamName(pointName);
                emsUsedDataParamPO.setHour(startTime.getHour());
                emsUsedDataParamPO.setLocationGid(selgid);
                emsUsedDataParamPO.setCreateTs(date);
                emsUsedDataParamPO.setCreateUser("hefq");
                emsUsedDataParamPO.setTimeStr(startTime.getHour() + ":" + startTime.getMinute());
                emsUsedDataParamPO.setYear(startTime.getYear());
                emsUsedDataParamPO.setMonth(startTime.getMonthValue());
                emsUsedDataParamPO.setDay(startTime.getDayOfMonth());
                emsUsedDataParamPO.setDeviceName(deviceName);
                emsUsedDataParamPO.setUnit(unit);
                maxCountList.add(emsUsedDataParamPO);

                endTimeDay15 = endTimeDay15.plusMinutes(15);
            }

            // 30分钟后
            if (startTime.isEqual(endTimeDay30)) {

                avgDay60 += avgDay30;

                EmsUsedDataParamPO emsUsedDataParamPO = new EmsUsedDataParamPO();
                emsUsedDataParamPO.setMinValue(minDay30);
                minDay30 = null;
                emsUsedDataParamPO.setMaxValue(maxDay30);
                maxDay30 = null;
                emsUsedDataParamPO.setAverageValue(avgDay30 / 30);
                emsUsedDataParamPO.setTotalValue(avgDay30);
                avgDay30 = 0.0;
                emsUsedDataParamPO.setMyTimeType(30);
                emsUsedDataParamPO.setUsedDate(date);
                emsUsedDataParamPO.setTriggerTime(date);
                emsUsedDataParamPO.setParamName(pointName);
                emsUsedDataParamPO.setHour(startTime.getHour());
                emsUsedDataParamPO.setLocationGid(selgid);
                emsUsedDataParamPO.setCreateTs(date);
                emsUsedDataParamPO.setCreateUser("hefq");
                emsUsedDataParamPO.setTimeStr(startTime.getHour() + ":" + startTime.getMinute());
                emsUsedDataParamPO.setYear(startTime.getYear());
                emsUsedDataParamPO.setMonth(startTime.getMonthValue());
                emsUsedDataParamPO.setDay(startTime.getDayOfMonth());
                emsUsedDataParamPO.setDeviceName(deviceName);
                emsUsedDataParamPO.setUnit(unit);
                maxCountList.add(emsUsedDataParamPO);

                endTimeDay30 = endTimeDay30.plusMinutes(30);
            }

            // 60分钟后
            if (startTime.isEqual(endTimeDay60)) {
                avgMonth += avgDay60;
                EmsUsedDataParamPO emsUsedDataParamPO = new EmsUsedDataParamPO();
                emsUsedDataParamPO.setMinValue(minDay60);
                minDay60 = null;
                emsUsedDataParamPO.setMaxValue(maxDay60);
                maxDay60 = null;
                emsUsedDataParamPO.setAverageValue(avgDay60 / 60);
                emsUsedDataParamPO.setTotalValue(avgDay60);
                avgDay60 = 0.0;
                emsUsedDataParamPO.setMyTimeType(60);
                emsUsedDataParamPO.setUsedDate(date);
                emsUsedDataParamPO.setTriggerTime(date);
                emsUsedDataParamPO.setParamName(pointName);
                emsUsedDataParamPO.setHour(startTime.getHour());
                emsUsedDataParamPO.setLocationGid(selgid);
                emsUsedDataParamPO.setCreateTs(date);
                emsUsedDataParamPO.setCreateUser("hefq");
                emsUsedDataParamPO.setTimeStr(startTime.getHour() + ":" + startTime.getMinute());
                emsUsedDataParamPO.setYear(startTime.getYear());
                emsUsedDataParamPO.setMonth(startTime.getMonthValue());
                emsUsedDataParamPO.setDay(startTime.getDayOfMonth());
                emsUsedDataParamPO.setDeviceName(deviceName);
                emsUsedDataParamPO.setUnit(unit);
                maxCountList.add(emsUsedDataParamPO);

                endTimeDay60 = endTimeDay60.plusMinutes(60);
            }

            // 一天后
            if (startTime.isEqual(endTimeMonth)) {

                yearCount++;

                EmsUsedDataDaySummaryParamPO emsUsedDataDaySummaryParamPO = new EmsUsedDataDaySummaryParamPO();
                emsUsedDataDaySummaryParamPO.setMinValue(minMonth);
                minDay60 = null;
                emsUsedDataDaySummaryParamPO.setMaxValue(maxMonth);
                maxDay60 = null;
                emsUsedDataDaySummaryParamPO.setAverageValue(avgMonth / 1440);
                emsUsedDataDaySummaryParamPO.setTotalValue(avgMonth);
                avgMonth = 0.0;
                emsUsedDataDaySummaryParamPO.setUsedDate(date);
                emsUsedDataDaySummaryParamPO.setLocationTreeGid(selgid);
                emsUsedDataDaySummaryParamPO.setCreateTs(date);
                emsUsedDataDaySummaryParamPO.setCreateUser("hefq");
                emsUsedDataDaySummaryParamPO.setTimeStr(startTime.getHour() + ":" + startTime.getMinute());
                emsUsedDataDaySummaryParamPO.setYear(startTime.getYear());
                emsUsedDataDaySummaryParamPO.setMonth(startTime.getMonthValue());
                emsUsedDataDaySummaryParamPO.setDay(startTime.getDayOfMonth());
                emsUsedDataDaySummaryParamPO.setParamName(pointName);
                emsUsedDataDaySummaryParamPO.setParamType(type);
                emsUsedDataDaySummaryParamPO.setUnit(unit);
                //插入数据 弄个my Insert
                emsUsedDataDaySummaryMapper.dayParamInsert(emsUsedDataDaySummaryParamPO);
                endTimeMonth = endTimeMonth.plusDays(1);

            }

            // 一个月后
            if (startTime.isEqual(endTimeYear)) {
                EmsUsedDataMonthSummaryParamPO emsUsedDataMonthSummaryParamPO = new EmsUsedDataMonthSummaryParamPO();
                emsUsedDataMonthSummaryParamPO.setMinValue(minYear);
                minYear = null;
                emsUsedDataMonthSummaryParamPO.setMaxValue(maxYear);
                maxYear = null;
//                emsUsedDataMonthSummaryParamPO.setAverageValue(avgYear / (1440 * startTime.getDayOfMonth()));
                emsUsedDataMonthSummaryParamPO.setAverageValue(avgYear / (1440 * yearCount));
                yearCount = 0;
                emsUsedDataMonthSummaryParamPO.setTotalValue(avgYear);
                avgYear = 0.0;
                emsUsedDataMonthSummaryParamPO.setUsedDate(date);
                emsUsedDataMonthSummaryParamPO.setParamName(pointName);
                emsUsedDataMonthSummaryParamPO.setLocationTreeGid(selgid);
                emsUsedDataMonthSummaryParamPO.setCreateTs(date);
                emsUsedDataMonthSummaryParamPO.setCreateUser("hefq");
                emsUsedDataMonthSummaryParamPO.setTimeStr(startTime.getHour() + ":" + startTime.getMinute());
                emsUsedDataMonthSummaryParamPO.setYear(startTime.getYear());
                emsUsedDataMonthSummaryParamPO.setMonth(startTime.getMonthValue());
                emsUsedDataMonthSummaryParamPO.setParamName(pointName);
                emsUsedDataMonthSummaryParamPO.setParamType(type);
                emsUsedDataMonthSummaryParamPO.setUnit(unit);
                //插入当月数据
                emsUsedDataMonthSummaryMapper.monthParamInsert(emsUsedDataMonthSummaryParamPO);

                endTimeYear = endTimeYear.plusMonths(1);
            }

            // 批量新增
            if (maxCountList.size() >= 1500) {
                emsUsedDataMapper.dayParamBatchInsert(maxCountList);
                maxCountList.clear();
            }
            startTime = startTime.plusMinutes(1);
        }

        // 把还剩下的添加了，然后删掉
        if (!maxCountList.isEmpty()) {
            emsUsedDataMapper.dayParamBatchInsert(maxCountList);
            maxCountList.clear();
        }

        /**
         * 判断这个月走完没，没走完再年表中把月的数据补上
         */

        // 当月的最后一天日期
        int dayOfMonth = YearMonth.from(startTime.toLocalDate()).atEndOfMonth().getDayOfMonth();
        startTime = startTime.minusMinutes(1);
        // 现在的日期小于这个月的最大日期
        if (endTime.getDayOfMonth() <= dayOfMonth) {
            EmsUsedDataMonthSummaryParamPO emsUsedDataMonthSummaryParamPO = new EmsUsedDataMonthSummaryParamPO();
            emsUsedDataMonthSummaryParamPO.setMinValue(minYear);
            emsUsedDataMonthSummaryParamPO.setMaxValue(maxYear);
            emsUsedDataMonthSummaryParamPO.setAverageValue(avgYear / (1440 * yearCount));
            Date date = Date.from(startTime.minusMinutes(1).atZone(ZoneId.systemDefault()).toInstant());
            emsUsedDataMonthSummaryParamPO.setUsedDate(date);
            emsUsedDataMonthSummaryParamPO.setParamName(pointName);
            emsUsedDataMonthSummaryParamPO.setLocationTreeGid(selgid);
            emsUsedDataMonthSummaryParamPO.setCreateTs(date);
            emsUsedDataMonthSummaryParamPO.setCreateUser("hefq");
            startTime = startTime.minusMinutes(1);
            emsUsedDataMonthSummaryParamPO.setTimeStr(startTime.getHour() + ":" + startTime.getMinute());
            emsUsedDataMonthSummaryParamPO.setYear(startTime.getYear());
            emsUsedDataMonthSummaryParamPO.setMonth(startTime.getMonthValue());
            emsUsedDataMonthSummaryParamPO.setTotalValue((avgYear / dayOfMonth) * dayOfMonth * 1440);
            emsUsedDataMonthSummaryParamPO.setParamType(type);
            emsUsedDataMonthSummaryParamPO.setUnit(unit);
            emsUsedDataMonthSummaryMapper.monthParamInsert(emsUsedDataMonthSummaryParamPO);
        }


//        String pointName = "MCHTP001_echo_electric_current_b";  // 测点名称
//        String selgid = "102.1388.000000.000075";          // 测点父级的 gid
//        String deviceName = "MCHTP001";                    // 父级名称
//        String type = "electricityConsumption";
//        String unit = "A";  // 单位
//        double minValueParam = 7.5;  // 最小
//        double maxValueParam = 14.3; // 最大
//
////        double minValueParam = 8.2;  // 最小
////        double maxValueParam = 15.4; // 最大
//
//        // 初始日期 --核心
//        LocalDateTime startTime = LocalDateTime.of(2023, 11, 27, 0, 0);
//
//        // 截止日期 不会到 13号
//        LocalDateTime endTime = LocalDateTime.of(2023, 12, 18, 0, 0);
//
//        Double minDay15 = null;
//        Double maxDay15 = null;
//        double avgDay15 = 0.0;  // 15分钟平均值
//        Double minMonth = null;
//        Double maxMonth = null;
//        double avgMonth = 0.0;  // 一个月平均值
//        Double minYear = null;
//        Double maxYear = null;
//        double avgYear = 0.0;   // 一年平均值
//
//        Double minDay30 = null;
//        Double maxDay30 = null;
//        double avgDay30 = 0.0;  // 30分钟平均值
//
//        Double minDay60 = null;
//        Double maxDay60 = null;
//        double avgDay60 = 0.0;  // 60分钟平均值
//
//        // 一批添加的use_data数据集合
//        List<EmsUsedDataParamPO> maxCountList = new ArrayList<>();
//
//        // 日期格式
//        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
//
//        // 刚到第15分钟 0 - 14 分钟
//        LocalDateTime endTimeDay15 = startTime.plusMinutes(15).minusMinutes(1);
//
//        LocalDateTime endTimeDay30 = startTime.plusMinutes(30).minusMinutes(1);
//
//        LocalDateTime endTimeDay60 = startTime.plusMinutes(60).minusMinutes(1);
//
//        LocalDateTime endTimeMonth = startTime.plusDays(1).minusMinutes(1);
//
//        LocalDateTime endTimeYear = startTime.plusMonths(1).minusMinutes(1);
//
//        // 初始日期没到截止日期 就一直循环添加... 到了就跳出循环 59结束
//        while (startTime.isBefore(endTime)) {
//
//            // 随机值
//            double v = Math.random() * (maxValueParam - minValueParam) + minValueParam; // =================================================================================
//
//            // 拆分日期
//            String[] split = startTime.format(formatter).split(" ");
//            String[] split1 = split[0].split("-");
//            String[] split2 = split[1].split(":");
//
//            // 获取日期
//            Date date = Date.from(startTime.atZone(ZoneId.systemDefault()).toInstant());
//
//            avgDay15 += v;
//            avgYear += v;
//
//            // 找出 min max
//            if (minDay15 == null || minDay15 > v) {
//                minDay15 = v;
//            }
//            if (minDay30 == null || minDay30 > v) {
//                minDay30 = v;
//            }
//            if (minDay60 == null || minDay60 > v) {
//                minDay60 = v;
//            }
//            if (maxDay15 == null || maxDay15 < v) {
//                maxDay15 = v;
//            }
//            if (maxDay30 == null || maxDay30 < v) {
//                maxDay30 = v;
//            }
//            if (maxDay60 == null || maxDay60 < v) {
//                maxDay60 = v;
//            }
//            if (maxMonth == null || maxMonth < v) {
//                maxMonth = v;
//            }
//            if (maxYear == null || maxYear < v) {
//                maxYear = v;
//            }
//            if (minYear == null || minYear > v) {
//                minYear = v;
//            }
//            if (minMonth == null || minMonth > v) {
//                minMonth = v;
//            }
//
//            // 创建 usedata 对象
//            EmsUsedDataParamPO po = new EmsUsedDataParamPO();
//            po.setUsedDate(date);
//            po.setTriggerTime(date);
//            po.setMyTimeType(0);
//            po.setParamName(pointName);
//            po.setMinValue(v);
//            po.setMaxValue(v);
//            po.setAverageValue(v);
//            po.setTotalValue(po.getAverageValue());
//            po.setYear(Integer.parseInt(split1[0]));
//            po.setMonth(Integer.parseInt(split1[1]));
//            po.setDay(Integer.parseInt(split1[2]));
//            po.setHour(Integer.parseInt(split2[0]));
//            po.setLocationGid(selgid);
//            po.setCreateTs(date);
//            po.setCreateUser("hefq");
//            po.setTimeStr(split[1]);
//            po.setDeviceName(deviceName);
//            po.setUnit(unit);
//
//            // 加入集合，准备批量添加
//            maxCountList.add(po);
//
//            // 十五分钟后
//            if (startTime.isEqual(endTimeDay15)) {
//
//                avgDay30 += avgDay15;
//
//                EmsUsedDataParamPO emsUsedDataParamPO = new EmsUsedDataParamPO();
//                emsUsedDataParamPO.setMinValue(minDay15);
//                minDay15 = null;
//                emsUsedDataParamPO.setMaxValue(maxDay15);
//                maxDay15 = null;
//                emsUsedDataParamPO.setAverageValue(avgDay15 / 15);
//                emsUsedDataParamPO.setTotalValue(avgDay15);
//                avgDay15 = 0.0;
//                emsUsedDataParamPO.setMyTimeType(15);
//                emsUsedDataParamPO.setUsedDate(date);
//                emsUsedDataParamPO.setTriggerTime(date);
//                emsUsedDataParamPO.setParamName(pointName);
//                emsUsedDataParamPO.setHour(Integer.parseInt(split2[0]));
//                emsUsedDataParamPO.setLocationGid(selgid);
//                emsUsedDataParamPO.setCreateTs(date);
//                emsUsedDataParamPO.setCreateUser("hefq");
//                emsUsedDataParamPO.setTimeStr(split[1]);
//                emsUsedDataParamPO.setYear(Integer.parseInt(split1[0]));
//                emsUsedDataParamPO.setMonth(Integer.parseInt(split1[1]));
//                emsUsedDataParamPO.setDay(Integer.parseInt(split1[2]));
//                emsUsedDataParamPO.setDeviceName(deviceName);
//                emsUsedDataParamPO.setUnit(unit);
//                maxCountList.add(emsUsedDataParamPO);
//
//                endTimeDay15 = endTimeDay15.plusMinutes(15);
//            }
//
//            // 30分钟后
//            if (startTime.isEqual(endTimeDay30)) {
//
//                avgDay60 += avgDay30;
//
//                EmsUsedDataParamPO emsUsedDataParamPO = new EmsUsedDataParamPO();
//                emsUsedDataParamPO.setMinValue(minDay30);
//                minDay30 = null;
//                emsUsedDataParamPO.setMaxValue(maxDay30);
//                maxDay30 = null;
//                emsUsedDataParamPO.setAverageValue(avgDay30 / 30);
//                emsUsedDataParamPO.setTotalValue(avgDay30);
//                avgDay30 = 0.0;
//                emsUsedDataParamPO.setMyTimeType(30);
//                emsUsedDataParamPO.setUsedDate(date);
//                emsUsedDataParamPO.setTriggerTime(date);
//                emsUsedDataParamPO.setParamName(pointName);
//                emsUsedDataParamPO.setHour(Integer.parseInt(split2[0]));
//                emsUsedDataParamPO.setLocationGid(selgid);
//                emsUsedDataParamPO.setCreateTs(date);
//                emsUsedDataParamPO.setCreateUser("hefq");
//                emsUsedDataParamPO.setTimeStr(split[1]);
//                emsUsedDataParamPO.setYear(Integer.parseInt(split1[0]));
//                emsUsedDataParamPO.setMonth(Integer.parseInt(split1[1]));
//                emsUsedDataParamPO.setDay(Integer.parseInt(split1[2]));
//                emsUsedDataParamPO.setDeviceName(deviceName);
//                emsUsedDataParamPO.setUnit(unit);
//                maxCountList.add(emsUsedDataParamPO);
//
//                endTimeDay30 = endTimeDay30.plusMinutes(30);
//            }
//
//            // 60分钟后
//            if (startTime.isEqual(endTimeDay60)) {
//                avgMonth += avgDay60;
//                EmsUsedDataParamPO emsUsedDataParamPO = new EmsUsedDataParamPO();
//                emsUsedDataParamPO.setMinValue(minDay60);
//                minDay60 = null;
//                emsUsedDataParamPO.setMaxValue(maxDay60);
//                maxDay60 = null;
//                emsUsedDataParamPO.setAverageValue(avgDay60 / 60);
//                emsUsedDataParamPO.setTotalValue(avgDay60);
//                avgDay60 = 0.0;
//                emsUsedDataParamPO.setMyTimeType(60);
//                emsUsedDataParamPO.setUsedDate(date);
//                emsUsedDataParamPO.setTriggerTime(date);
//                emsUsedDataParamPO.setParamName(pointName);
//                emsUsedDataParamPO.setHour(Integer.parseInt(split2[0]));
//                emsUsedDataParamPO.setLocationGid(selgid);
//                emsUsedDataParamPO.setCreateTs(date);
//                emsUsedDataParamPO.setCreateUser("hefq");
//                emsUsedDataParamPO.setTimeStr(split[1]);
//                emsUsedDataParamPO.setYear(Integer.parseInt(split1[0]));
//                emsUsedDataParamPO.setMonth(Integer.parseInt(split1[1]));
//                emsUsedDataParamPO.setDay(Integer.parseInt(split1[2]));
//                emsUsedDataParamPO.setDeviceName(deviceName);
//                emsUsedDataParamPO.setUnit(unit);
//                maxCountList.add(emsUsedDataParamPO);
//
//                endTimeDay60 = endTimeDay60.plusMinutes(60);
//            }
//
//            // 一天后
//            if (startTime.isEqual(endTimeMonth)) {
//                EmsUsedDataDaySummaryParamPO emsUsedDataDaySummaryParamPO = new EmsUsedDataDaySummaryParamPO();
//                emsUsedDataDaySummaryParamPO.setMinValue(minMonth);
//                minDay60 = null;
//                emsUsedDataDaySummaryParamPO.setMaxValue(maxMonth);
//                maxDay60 = null;
//                emsUsedDataDaySummaryParamPO.setAverageValue(avgMonth / 1440);
//                emsUsedDataDaySummaryParamPO.setTotalValue(avgMonth);
//                avgMonth = 0.0;
//                emsUsedDataDaySummaryParamPO.setUsedDate(date);
//                emsUsedDataDaySummaryParamPO.setLocationTreeGid(selgid);
//                emsUsedDataDaySummaryParamPO.setCreateTs(date);
//                emsUsedDataDaySummaryParamPO.setCreateUser("hefq");
//                emsUsedDataDaySummaryParamPO.setTimeStr(split[1]);
//                emsUsedDataDaySummaryParamPO.setYear(Integer.parseInt(split1[0]));
//                emsUsedDataDaySummaryParamPO.setMonth(Integer.parseInt(split1[1]));
//                emsUsedDataDaySummaryParamPO.setDay(Integer.parseInt(split1[2]));
//                emsUsedDataDaySummaryParamPO.setParamName(pointName);
//                emsUsedDataDaySummaryParamPO.setParamType(type);
//                emsUsedDataDaySummaryParamPO.setUnit(unit);
//                //插入数据 弄个my Insert
//                emsUsedDataDaySummaryMapper.dayParamInsert(emsUsedDataDaySummaryParamPO);
//                endTimeMonth = endTimeMonth.plusDays(1);
//
//            }
//
//            // 一个月后
//            if (startTime.isEqual(endTimeYear)) {
//                EmsUsedDataMonthSummaryParamPO emsUsedDataMonthSummaryParamPO = new EmsUsedDataMonthSummaryParamPO();
//                emsUsedDataMonthSummaryParamPO.setMinValue(minYear);
//                minYear = null;
//                emsUsedDataMonthSummaryParamPO.setMaxValue(maxYear);
//                maxYear = null;
//                emsUsedDataMonthSummaryParamPO.setAverageValue(avgYear / (1440 * startTime.getDayOfMonth()));
//                emsUsedDataMonthSummaryParamPO.setTotalValue(avgYear);
//                avgYear = 0.0;
//                emsUsedDataMonthSummaryParamPO.setUsedDate(date);
//                emsUsedDataMonthSummaryParamPO.setParamName(pointName);
//                emsUsedDataMonthSummaryParamPO.setLocationTreeGid(selgid);
//                emsUsedDataMonthSummaryParamPO.setCreateTs(date);
//                emsUsedDataMonthSummaryParamPO.setCreateUser("hefq");
//                emsUsedDataMonthSummaryParamPO.setTimeStr(split[1]);
//                emsUsedDataMonthSummaryParamPO.setYear(Integer.parseInt(split1[0]));
//                emsUsedDataMonthSummaryParamPO.setMonth(Integer.parseInt(split1[1]));
//                emsUsedDataMonthSummaryParamPO.setParamName(pointName);
//                emsUsedDataMonthSummaryParamPO.setParamType(type);
//                emsUsedDataMonthSummaryParamPO.setUnit(unit);
//                //插入当月数据
//                emsUsedDataMonthSummaryMapper.monthParamInsert(emsUsedDataMonthSummaryParamPO);
//
//                endTimeYear = endTimeYear.plusMonths(1);
//            }
//
//            // 批量新增
//            if (maxCountList.size() >= 1500) {
//                emsUsedDataMapper.dayParamBatchInsert(maxCountList);
//                maxCountList.clear();
//            }
//            startTime = startTime.plusMinutes(1);
//        }
//
//        // 把还剩下的添加了，然后删掉
//        if (!maxCountList.isEmpty()) {
//            emsUsedDataMapper.dayParamBatchInsert(maxCountList);
//            maxCountList.clear();
//        }
//
//        /**
//         * 判断这个月走完没，没走完再年表中把月的数据补上
//         */
//
//        // 当月的最后一天日期
//        int dayOfMonth = YearMonth.from(startTime.toLocalDate()).atEndOfMonth().getDayOfMonth();
//        // 现在的日期小于这个月的最大日期
//        if (endTime.getDayOfMonth() <= dayOfMonth) {
//            EmsUsedDataMonthSummaryParamPO emsUsedDataMonthSummaryParamPO = new EmsUsedDataMonthSummaryParamPO();
//            emsUsedDataMonthSummaryParamPO.setMinValue(minYear);
//            emsUsedDataMonthSummaryParamPO.setMaxValue(maxYear);
//            emsUsedDataMonthSummaryParamPO.setAverageValue(avgYear / (1440 * startTime.getDayOfMonth()));
//            String[] split = startTime.format(formatter).split(" ");
//            String[] split1 = split[0].split("-");
//            Date date = Date.from(startTime.minusMinutes(1).atZone(ZoneId.systemDefault()).toInstant());
//            emsUsedDataMonthSummaryParamPO.setUsedDate(date);
//            emsUsedDataMonthSummaryParamPO.setParamName(pointName);
//            emsUsedDataMonthSummaryParamPO.setLocationTreeGid(selgid);
//            emsUsedDataMonthSummaryParamPO.setCreateTs(date);
//            emsUsedDataMonthSummaryParamPO.setCreateUser("hefq");
//            emsUsedDataMonthSummaryParamPO.setTimeStr(split[1]);
//            emsUsedDataMonthSummaryParamPO.setYear(Integer.parseInt(split1[0]));
//            emsUsedDataMonthSummaryParamPO.setMonth(Integer.parseInt(split1[1]));
//            emsUsedDataMonthSummaryParamPO.setTotalValue((avgYear / dayOfMonth) * dayOfMonth * 1440);
//            emsUsedDataMonthSummaryParamPO.setParamType(type);
//            emsUsedDataMonthSummaryParamPO.setUnit(unit);
//            emsUsedDataMonthSummaryParamPO.setTimeStr("23:59:00");
//            emsUsedDataMonthSummaryMapper.monthParamInsert(emsUsedDataMonthSummaryParamPO);
//        }


    }


    /**
     * 构造虚拟主变的数据
     */
    public void myInsert2() {
        // 测点名字
        String pointName = "Total_value.electricity.b1a";
        String selgid = "102.1477.000000.000096";
        String deviceName = "MCHFZ002";

        List<EmsUsedDataDaySummaryPO> halfMonth = new ArrayList<>();
        Double minDay15 = null;
        Double maxDay15 = null;
        double avgDay15 = 0.0;
        Double minMonth = null;
        Double maxMonth = null;
        double avgMonth = 0.0;
        Double minYear = null;
        Double maxYear = null;
        double avgYear = 0.0;
        int monthCount = 0;
        int yearCount = 0;
        Double minDay30 = null;
        Double maxDay30 = null;
        double avgDay30 = 0.0;
        Double minDay60 = null;
        Double maxDay60 = null;
        double avgDay60 = 0.0;
        // 一批次添加
        List<EmsUsedDataPO> maxCountList = new ArrayList<>();
        // 日期格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        // 初始日期 --核心
        LocalDateTime startTime = LocalDateTime.of(2023, 12, 1, 0, 0);
        // 六十分后
//        LocalDateTime endTimeDay60 = startTime.plusMinutes(60);
        // 一天后
        LocalDateTime endTimeMonth = startTime.plusDays(1);
        // 一个月后
        LocalDateTime endTimeYear = startTime.plusMonths(1);
        // 截止日期 --核心
        LocalDateTime endTime = LocalDateTime.of(2023, 12, 20, 0, 0);
        ;

        // 初始日期没到截止日期 就一直循环添加...
        while (startTime.isBefore(endTime)) {

            // 月份天数
            monthCount++;
            yearCount++;

            EmsUsedDataPO po = new EmsUsedDataPO();
            String[] split = startTime.format(formatter).split(" ");
            String[] split1 = split[0].split("-");
            Date date = Date.from(startTime.atZone(ZoneId.systemDefault()).toInstant());
            po.setUsedDate(date);
            po.setTriggerTime(date);
            po.setMyTimeType(60);
            po.setParamName(pointName);
            double v = Math.random() * 8.93 + 24.02; // 26.06 - 35
            avgDay15 += v;
            avgDay30 += v;
            avgDay60 += v;
            avgMonth += v;
            avgYear += v;
            if (minDay15 == null || minDay15 > v) {
                minDay15 = v;
            }
            if (minDay30 == null || minDay30 > v) {
                minDay30 = v;
            }
            if (minDay60 == null || minDay60 > v) {
                minDay60 = v;
            }
            if (maxDay15 == null || maxDay15 < v) {
                maxDay15 = v;
            }
            if (maxDay30 == null || maxDay30 < v) {
                maxDay30 = v;
            }
            if (maxDay60 == null || maxDay60 < v) {
                maxDay60 = v;
            }
            if (maxMonth == null || maxMonth < v) {
                maxMonth = v;
            }
            if (maxYear == null || maxYear < v) {
                maxYear = v;
            }
            if (minYear == null || minYear > v) {
                minYear = v;
            }
            if (minMonth == null || minMonth > v) {
                minMonth = v;
            }

            po.setMinValue(v);
            po.setMaxValue(v);
            po.setAverageValue(v);
            po.setTotalValue(po.getAverageValue());
            po.setYear(Integer.parseInt(split1[0]));
            po.setMonth(Integer.parseInt(split1[1]));
            po.setDay(Integer.parseInt(split1[2]));
            String[] split2 = split[1].split(":");
            po.setHour(Integer.parseInt(split2[0]));
            po.setLocationGid(selgid);
            po.setCreateTs(date);
            po.setCreateUser("hefq");
            po.setTimeStr(split[1]);
            po.setDeviceName(deviceName);
            maxCountList.add(po);

//             一天后
            if (startTime.isEqual(endTimeMonth)) {
                EmsUsedDataDaySummaryPO emsUsedDataDaySummaryPO = new EmsUsedDataDaySummaryPO();
                emsUsedDataDaySummaryPO.setMinValue(minMonth);
                minDay60 = null;
                emsUsedDataDaySummaryPO.setMaxValue(maxMonth);
                maxDay60 = null;
                emsUsedDataDaySummaryPO.setAverageValue(avgMonth / monthCount);
                emsUsedDataDaySummaryPO.setTotalValue(avgMonth / monthCount * 24);
                avgMonth = 0.0;
                monthCount = 0;
                emsUsedDataDaySummaryPO.setUsedDate(date);
                emsUsedDataDaySummaryPO.setLocationTreeGid(selgid);
                emsUsedDataDaySummaryPO.setCreateTs(date);
                emsUsedDataDaySummaryPO.setCreateUser("hefq");
                emsUsedDataDaySummaryPO.setTimeStr(split[1]);
                emsUsedDataDaySummaryPO.setYear(Integer.parseInt(split1[0]));
                emsUsedDataDaySummaryPO.setMonth(Integer.parseInt(split1[1]));
                emsUsedDataDaySummaryPO.setDay(Integer.parseInt(split1[2]));
                emsUsedDataDaySummaryPO.setParamName(pointName);
                emsUsedDataDaySummaryPO.setParamType("electricityConsumption");
                //插入数据 弄个my Insert
                emsUsedDataDaySummaryMapper.dayInsert(emsUsedDataDaySummaryPO);
                halfMonth.add(emsUsedDataDaySummaryPO);
                endTimeMonth = endTimeMonth.plusDays(1);
            }

            // 一个月后
            if (startTime.isEqual(endTimeYear) || startTime.isAfter(endTimeYear)) {
                EmsUsedDataMonthSummaryPO emsUsedDataMonthSummaryPO = new EmsUsedDataMonthSummaryPO();
                emsUsedDataMonthSummaryPO.setMinValue(minYear);
                minYear = null;
                emsUsedDataMonthSummaryPO.setMaxValue(maxYear);
                maxYear = null;
                emsUsedDataMonthSummaryPO.setAverageValue(avgYear / yearCount);
                emsUsedDataMonthSummaryPO.setTotalValue(avgYear / yearCount * 720);
                avgYear = 0.0;
                yearCount = 0;
                emsUsedDataMonthSummaryPO.setUsedDate(date);
                emsUsedDataMonthSummaryPO.setParamName(pointName);
                emsUsedDataMonthSummaryPO.setLocationTreeGid(selgid);
                emsUsedDataMonthSummaryPO.setCreateTs(date);
                emsUsedDataMonthSummaryPO.setCreateUser("hefq");
                emsUsedDataMonthSummaryPO.setTimeStr(split[1]);
                emsUsedDataMonthSummaryPO.setYear(Integer.parseInt(split1[0]));
                emsUsedDataMonthSummaryPO.setMonth(Integer.parseInt(split1[1]));
                emsUsedDataMonthSummaryPO.setParamName(pointName);
                emsUsedDataMonthSummaryPO.setParamType("electricityConsumption");
                //插入数据 弄个my Insert
//                emsUsedDataDaySummaryMapper.insert(emsUsedDataDaySummaryPO);
                emsUsedDataMonthSummaryMapper.myInsert(emsUsedDataMonthSummaryPO);
                endTimeYear = endTimeYear.plusMonths(1);
            }

            //添加
            if (maxCountList.size() >= 10) {
                emsUsedDataMapper.newInsert(maxCountList);
                maxCountList.clear();
            }
            startTime = startTime.plusMinutes(60);
        }
        if (!maxCountList.isEmpty()) {
            emsUsedDataMapper.newInsert(maxCountList);
            maxCountList.clear();
        }

        // 月没跑完把记录补上
        YearMonth from = YearMonth.from(startTime.toLocalDate());
        LocalDate localDate = from.atEndOfMonth();
        // 当月的最后一天日期
        int dayOfMonth = localDate.getDayOfMonth();
        // 现在的日期小于这个月的最大日期
        if (endTime.getDayOfMonth() < dayOfMonth && !halfMonth.isEmpty()) {
            // 先处理年表 ，把这个月走完的天求一下最大最小和平均值
            List<Double> sortedList = halfMonth.stream().map(EmsUsedDataDaySummaryPO::getMinValue).sorted().collect(Collectors.toList());
            List<Double> sortedList2 = halfMonth.stream().map(EmsUsedDataDaySummaryPO::getMaxValue).sorted().collect(Collectors.toList());
            List<Double> avgList = halfMonth.stream().map(EmsUsedDataDaySummaryPO::getAverageValue).sorted().collect(Collectors.toList());
            Double avg = 0.0;
            for (int i = 0; i < avgList.size(); i++) {
                avg += avgList.get(i);
            }
            avg = avg / avgList.size();
            EmsUsedDataMonthSummaryPO emsUsedDataMonthSummaryPO = new EmsUsedDataMonthSummaryPO();
            emsUsedDataMonthSummaryPO.setMinValue(sortedList.get(0));
            emsUsedDataMonthSummaryPO.setMaxValue(sortedList.get(sortedList2.size() - 1));
            emsUsedDataMonthSummaryPO.setAverageValue(avg);
            String[] split = startTime.format(formatter).split(" ");
            String[] split1 = split[0].split("-");
            Date date = Date.from(startTime.minusMinutes(1).atZone(ZoneId.systemDefault()).toInstant());
            emsUsedDataMonthSummaryPO.setUsedDate(date);
            emsUsedDataMonthSummaryPO.setParamName(pointName);
            emsUsedDataMonthSummaryPO.setLocationTreeGid(selgid);
            emsUsedDataMonthSummaryPO.setCreateTs(date);
            emsUsedDataMonthSummaryPO.setCreateUser("hefq");
            emsUsedDataMonthSummaryPO.setTimeStr(split[1]);
            emsUsedDataMonthSummaryPO.setYear(Integer.parseInt(split1[0]));
            emsUsedDataMonthSummaryPO.setMonth(Integer.parseInt(split1[1]));
            emsUsedDataMonthSummaryPO.setTotalValue(avg * startTime.minusMinutes(1).getDayOfMonth() * 1440 * 0.00009489);
            emsUsedDataMonthSummaryPO.setParamType("electricityConsumption");
            emsUsedDataMonthSummaryPO.setTimeStr("23:59:00");
            emsUsedDataMonthSummaryMapper.myInsert(emsUsedDataMonthSummaryPO);
        }

    }

    public void insert_60() {

    }

    public List<GetEmsUseDataDTO> getPrice(List<MyBO> list) {
        return emsUsedDataMapper.getPrice(list);
    }
}
