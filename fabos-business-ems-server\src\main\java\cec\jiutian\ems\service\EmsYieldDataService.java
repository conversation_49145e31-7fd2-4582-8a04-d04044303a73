
package cec.jiutian.ems.service;

import cec.jiutian.core.base.BaseDomainService;
import cec.jiutian.ems.domain.EmsCapacityData;
import cec.jiutian.ems.domain.EmsYieldData;
import cec.jiutian.ems.po.EmsYieldDataPO;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
@Transactional
@AllArgsConstructor
public class EmsYieldDataService extends BaseDomainService<EmsYieldDataPO, EmsYieldData, String> {

    /**
     * 批量添加产能数据
     * @param list
     */
    void batchInsertData(List<EmsYieldDataPO> list) {
        List<EmsYieldData> collect = list.stream().map(EmsYieldData::new).collect(Collectors.toList());
        saveBatch(collect);
    }

}
