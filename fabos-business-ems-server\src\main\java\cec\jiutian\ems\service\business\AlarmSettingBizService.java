package cec.jiutian.ems.service.business;
import cec.jiutian.base.dto.BaseQueryDTO;
import cec.jiutian.ems.domain.AlarmSetting;
import cec.jiutian.ems.dto.*;
import cec.jiutian.ems.service.AlarmSettingParameterRelationService;
import cec.jiutian.ems.service.AlarmSettingService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
/**
 * Alarm Setting;报警设置
 * <AUTHOR> CodeGenerator
 * @date : 2023-6-7
 */
@Slf4j
@Service
@Transactional
public class AlarmSettingBizService {
    private final AlarmSettingService alarmSettingService;
    private final AlarmSettingParameterRelationService alarmSettingParameterRelationService;

    public AlarmSettingBizService(AlarmSettingService alarmSettingService, AlarmSettingParameterRelationService alarmSettingParameterRelationService) {
        this.alarmSettingService = alarmSettingService;
        this.alarmSettingParameterRelationService = alarmSettingParameterRelationService;
    }

    public Object getAlarmSettingList(GetAlarmSettingDTO getAlarmSettingDTO) {
        return alarmSettingService.getAlarmSettingList(getAlarmSettingDTO);
    }

    public Object getAlarmSettingParameterRelationList(GetAlarmSettingParameterRelationDTO getAlarmSettingParameterRelationDTO) {
        return alarmSettingParameterRelationService.getAlarmSettingParameterRelationList(getAlarmSettingParameterRelationDTO);
    }

    public Boolean createAlarmSetting(AlarmSettingCreateDTO alarmSettingCreateDTO) {
        AlarmSetting alarmSetting = alarmSettingService.createAlarmSetting(alarmSettingCreateDTO);
        alarmSettingParameterRelationService.createAlarmSettingParameterRelation(alarmSettingCreateDTO.getAlarmSettingParameterRelationCreateList(),alarmSetting.getGid());
        return true;
    }

    public Boolean updateAlarmSetting(AlarmSettingUpdateDTO alarmSettingUpdateDTO) {
        alarmSettingService.updateAlarmSetting(alarmSettingUpdateDTO);
        alarmSettingParameterRelationService.deleteAlarmSettingParameterRelation(alarmSettingUpdateDTO.getGid());
        alarmSettingParameterRelationService.createAlarmSettingParameterRelation(alarmSettingUpdateDTO.getAlarmSettingParameterRelationCreateList(), alarmSettingUpdateDTO.getGid());
        return true;
    }

    public Boolean deleteAlarmSetting(AlarmSettingDeleteDTO alarmSettingDeleteDTO) {
        alarmSettingService.deleteAlarmSetting(alarmSettingDeleteDTO);
        alarmSettingParameterRelationService.deleteAlarmSettingParameterRelation(alarmSettingDeleteDTO.getGid());
        return true;
    }

}