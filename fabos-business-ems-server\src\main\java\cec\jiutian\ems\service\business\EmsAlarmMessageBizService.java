package cec.jiutian.ems.service.business;

import cec.jiutian.core.comn.util.BeanUtils;
import cec.jiutian.core.comn.util.StringUtils;
import cec.jiutian.core.service.TokenAnalysisService;
import cec.jiutian.ems.domain.EmsAlarmMessage;
import cec.jiutian.ems.domain.EmsAlarmMessageHistory;
import cec.jiutian.ems.domain.EmsAlarmMessageLog;
import cec.jiutian.ems.dto.*;
import cec.jiutian.ems.facade.dto.MessageCreateDTO;
import cec.jiutian.ems.facade.feign.MessageRequestFeign;
import cec.jiutian.ems.mapper.EmsAlarmMessageHistoryMapper;
import cec.jiutian.ems.mapper.EmsAlarmMessageLogMapper;
import cec.jiutian.ems.mapper.EmsAlarmMessageMapper;
import cec.jiutian.ems.po.EmsAlarmMessageHistoryPO;
import cec.jiutian.ems.po.EmsAlarmMessageLogPO;
import cec.jiutian.ems.po.EmsAlarmMessagePO;
import cec.jiutian.ems.service.*;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.weekend.WeekendSqls;

import java.text.ParseException;
import java.util.List;
import java.util.stream.Collectors;

/**
 * EMS Alarm Message;报警信息
 *
 * <AUTHOR> CodeGenerator
 * @date : 2023-6-12
 */
@Slf4j
@Service
@Transactional
public class EmsAlarmMessageBizService {
    private final EmsAlarmMessageService emsAlarmMessageService;
    private final EmsAlarmMessageHistoryService emsAlarmMessageHistoryService;
    private final EmsAlarmMessageLogService emsAlarmMessageLogService;
    private final EmsParamInfoService emsParamInfoService;
    private final AlarmSettingService alarmSettingService;
    private final EmsAlarmMessageMapper emsAlarmMessageMapper;
    private final EmsAlarmMessageHistoryMapper emsAlarmMessageHistoryMapper;
    private final EmsAlarmMessageLogMapper emsAlarmMessageLogMapper;
    private final MessageRequestFeign messageRequestFeign;
    private final TokenAnalysisService tokenAnalysisService;

    public EmsAlarmMessageBizService(EmsAlarmMessageService emsAlarmMessageService, EmsAlarmMessageHistoryService emsAlarmMessageHistoryService, EmsAlarmMessageLogService emsAlarmMessageLogService, EmsParamInfoService emsParamInfoService, AlarmSettingService alarmSettingService, EmsAlarmMessageMapper emsAlarmMessageMapper, EmsAlarmMessageHistoryMapper emsAlarmMessageHistoryMapper, EmsAlarmMessageLogMapper emsAlarmMessageLogMapper, MessageRequestFeign messageRequestFeign, TokenAnalysisService tokenAnalysisService) {
        this.emsAlarmMessageService = emsAlarmMessageService;
        this.emsAlarmMessageHistoryService = emsAlarmMessageHistoryService;
        this.emsAlarmMessageLogService = emsAlarmMessageLogService;
        this.emsParamInfoService = emsParamInfoService;
        this.alarmSettingService = alarmSettingService;
        this.emsAlarmMessageMapper = emsAlarmMessageMapper;
        this.emsAlarmMessageHistoryMapper = emsAlarmMessageHistoryMapper;
        this.emsAlarmMessageLogMapper = emsAlarmMessageLogMapper;
        this.messageRequestFeign = messageRequestFeign;
        this.tokenAnalysisService = tokenAnalysisService;
    }

    public void autoAlarmTriggerConfirmMultiplex(List<EmsAlarmMessage> emsAlarmMessageList, List<EmsAlarmMessageHistory> emsAlarmMessageHistoryList, GetAlarmSettingByParamResultDTO getAlarmSettingByParamResultDTO, GetUsedParamResultDTO getUsedParamResultDTO, String alarmCondition, String alarmContent) {
        if (CollectionUtils.isNotEmpty(emsAlarmMessageList)) {
            EmsAlarmMessage emsAlarmMessage = emsAlarmMessageList.get(0);
            EmsAlarmMessageCreateDTO emsAlarmMessageCreateDTO = new EmsAlarmMessageCreateDTO();
            BeanUtils.copyProperties(getAlarmSettingByParamResultDTO, emsAlarmMessageCreateDTO);
            emsAlarmMessage.init(emsAlarmMessageCreateDTO);
            emsAlarmMessage.setAlarmCondition(alarmCondition);
            emsAlarmMessage.setAlarmContent(alarmContent);
            emsAlarmMessage.setCurrentValue(getUsedParamResultDTO.getTotalValue());
            emsAlarmMessage.setAlarmStatus("待处理");
            emsAlarmMessageService.update(emsAlarmMessage);
        } else {
            EmsAlarmMessage emsAlarmMessage = new EmsAlarmMessage(new EmsAlarmMessagePO());
            EmsAlarmMessageCreateDTO emsAlarmMessageCreateDTO = new EmsAlarmMessageCreateDTO();
            BeanUtils.copyProperties(getAlarmSettingByParamResultDTO, emsAlarmMessageCreateDTO);
            emsAlarmMessage.init(emsAlarmMessageCreateDTO);
            emsAlarmMessage.setAlarmCondition(alarmCondition);
            emsAlarmMessage.setAlarmContent(alarmContent);
            emsAlarmMessage.setCurrentValue(getUsedParamResultDTO.getTotalValue());
            emsAlarmMessage.setAlarmStatus("待处理");
            emsAlarmMessage.save();
        }
        if (CollectionUtils.isEmpty(emsAlarmMessageHistoryList)) {
            EmsAlarmMessageHistory emsAlarmMessageHistory = new EmsAlarmMessageHistory(new EmsAlarmMessageHistoryPO());
            EmsAlarmMessageCreateDTO emsAlarmMessageCreateDTO = new EmsAlarmMessageCreateDTO();
            BeanUtils.copyProperties(getAlarmSettingByParamResultDTO, emsAlarmMessageCreateDTO);
            emsAlarmMessageHistory.init(emsAlarmMessageCreateDTO);
            emsAlarmMessageHistory.setAlarmCondition(alarmCondition);
            emsAlarmMessageHistory.setAlarmContent(alarmContent);
            emsAlarmMessageHistory.setCurrentValue(getUsedParamResultDTO.getTotalValue());
            emsAlarmMessageHistory.setAlarmStatus("待处理");
            emsAlarmMessageHistory.save();
            MessageCreateDTO messageCreateDTO = new MessageCreateDTO();
            messageCreateDTO.setSysName("EMS");
            messageCreateDTO.setAuthKey("88BBF3F2BA7D779D1CE6D8488267DD36C037069CB17EB3FC3E7D439454AE134C31E8F1F5AFFAB764450679D13BB7E098");
            JSONObject pushUserJSON = new JSONObject();
            pushUserJSON.put("first", getAlarmSettingByParamResultDTO.getPushUser().replace(";", ","));
            messageCreateDTO.setDispatchUser(pushUserJSON.toString());
            String[] pushMethodList = getAlarmSettingByParamResultDTO.getPushMethod().split(";");
            StringBuilder stringBuilder = new StringBuilder();
            for (int i = 0; i < pushMethodList.length; i++) {
                if (StringUtils.equals(pushMethodList[i], "邮件")) {
                    stringBuilder.append("Email");
                } else if (StringUtils.equals(pushMethodList[i], "短信")) {
                    stringBuilder.append("SMS");
                }
                if (i != pushMethodList.length - 1) {
                    stringBuilder.append(",");
                }
            }
            JSONObject pushMethodJSON = new JSONObject();
            pushMethodJSON.put("first", stringBuilder.toString());
            messageCreateDTO.setDispatchWay(pushMethodJSON.toString());
            messageCreateDTO.setUpNumber(1L);
            messageCreateDTO.setUpIntervalMinute(0L);
            messageCreateDTO.setIsProcess("0");
            if (!StringUtils.equals(alarmContent, "null")) {
                messageCreateDTO.setContent("点位" + emsAlarmMessageCreateDTO.getParamName() + "发生报警," + "触发条件为" + alarmCondition +
                        "," + "当前值为" + getUsedParamResultDTO.getTotalValue() + "," + "报警内容为" + alarmContent);
            } else {
                messageCreateDTO.setContent("点位" + emsAlarmMessageCreateDTO.getParamName() + "发生报警," + "触发条件为" + alarmCondition +
                        "," + "当前值为" + getUsedParamResultDTO.getTotalValue() + "," + "报警内容为");
            }
            messageRequestFeign.receiveMsg(messageCreateDTO);
        }
        EmsAlarmMessageLog emsAlarmMessageLog = new EmsAlarmMessageLog(new EmsAlarmMessageLogPO());
        EmsAlarmMessageCreateDTO emsAlarmMessageCreateDTO = new EmsAlarmMessageCreateDTO();
        BeanUtils.copyProperties(getAlarmSettingByParamResultDTO, emsAlarmMessageCreateDTO);
        emsAlarmMessageLog.init(emsAlarmMessageCreateDTO);
        emsAlarmMessageLog.setAlarmCondition(alarmCondition);
        emsAlarmMessageLog.setAlarmContent(alarmContent);
        emsAlarmMessageLog.setCurrentValue(getUsedParamResultDTO.getTotalValue());
        emsAlarmMessageLog.setAlarmStatus("待处理");
        emsAlarmMessageLog.save();
    }

    public void autoAlarmTriggerNotConfirmMultiplex(List<EmsAlarmMessage> emsAlarmMessageList, List<EmsAlarmMessageHistory> emsAlarmMessageHistoryList, List<EmsAlarmMessageHistory> emsAlarmMessageHistoryProcessedList, List<EmsAlarmMessageLog> emsAlarmMessageLogList, GetAlarmSettingByParamResultDTO getAlarmSettingByParamResultDTO, GetUsedParamResultDTO getUsedParamResultDTO, String alarmCondition, String alarmContent) {
        if (CollectionUtils.isNotEmpty(emsAlarmMessageList)) {
            EmsAlarmMessage emsAlarmMessage = emsAlarmMessageList.get(0);
            EmsAlarmMessageCreateDTO emsAlarmMessageCreateDTO = new EmsAlarmMessageCreateDTO();
            BeanUtils.copyProperties(getAlarmSettingByParamResultDTO, emsAlarmMessageCreateDTO);
            emsAlarmMessage.init(emsAlarmMessageCreateDTO);
            emsAlarmMessage.setAlarmCondition(alarmCondition);
            emsAlarmMessage.setAlarmContent(alarmContent);
            emsAlarmMessage.setCurrentValue(getUsedParamResultDTO.getTotalValue());
            emsAlarmMessage.setAlarmStatus("已处理");
            emsAlarmMessageService.update(emsAlarmMessage);
        }
        if (CollectionUtils.isNotEmpty(emsAlarmMessageHistoryList) && CollectionUtils.isEmpty(emsAlarmMessageHistoryProcessedList)) {
            EmsAlarmMessageHistory emsAlarmMessageHistory = new EmsAlarmMessageHistory(new EmsAlarmMessageHistoryPO());
            EmsAlarmMessageCreateDTO emsAlarmMessageCreateDTO = new EmsAlarmMessageCreateDTO();
            BeanUtils.copyProperties(getAlarmSettingByParamResultDTO, emsAlarmMessageCreateDTO);
            emsAlarmMessageHistory.init(emsAlarmMessageCreateDTO);
            emsAlarmMessageHistory.setAlarmCondition(alarmCondition);
            emsAlarmMessageHistory.setAlarmContent(alarmContent);
            emsAlarmMessageHistory.setCurrentValue(getUsedParamResultDTO.getTotalValue());
            emsAlarmMessageHistory.setAlarmStatus("已处理");
            emsAlarmMessageHistory.save();
            MessageCreateDTO messageCreateDTO = new MessageCreateDTO();
            messageCreateDTO.setSysName("EMS");
            messageCreateDTO.setAuthKey("88BBF3F2BA7D779D1CE6D8488267DD36C037069CB17EB3FC3E7D439454AE134C31E8F1F5AFFAB764450679D13BB7E098");
            JSONObject pushUserJSON = new JSONObject();
            pushUserJSON.put("first", getAlarmSettingByParamResultDTO.getPushUser().replace(";", ","));
            messageCreateDTO.setDispatchUser(pushUserJSON.toString());
            String[] pushMethodList = getAlarmSettingByParamResultDTO.getPushMethod().split(";");
            StringBuilder stringBuilder = new StringBuilder();
            for (int i = 0; i < pushMethodList.length; i++) {
                if (StringUtils.equals(pushMethodList[i], "邮件")) {
                    stringBuilder.append("Email");
                } else if (StringUtils.equals(pushMethodList[i], "短信")) {
                    stringBuilder.append("SMS");
                }
                if (i != pushMethodList.length - 1) {
                    stringBuilder.append(",");
                }
            }
            JSONObject pushMethodJSON = new JSONObject();
            pushMethodJSON.put("first", stringBuilder.toString());
            messageCreateDTO.setDispatchWay(pushMethodJSON.toString());
            messageCreateDTO.setUpNumber(1L);
            messageCreateDTO.setUpIntervalMinute(0L);
            messageCreateDTO.setIsProcess("0");
            if (!StringUtils.equals(alarmContent, "null")) {
                messageCreateDTO.setContent("点位" + emsAlarmMessageCreateDTO.getParamName() + "已处理报警," + "触发条件为" + alarmCondition +
                        "," + "当前值为" + getUsedParamResultDTO.getTotalValue() + "," + "报警内容为" + alarmContent);
            } else {
                messageCreateDTO.setContent("点位" + emsAlarmMessageCreateDTO.getParamName() + "已处理报警," + "触发条件为" + alarmCondition +
                        "," + "当前值为" + getUsedParamResultDTO.getTotalValue() + "," + "报警内容为");
            }
            messageRequestFeign.receiveMsg(messageCreateDTO);
        }
        if (CollectionUtils.isNotEmpty(emsAlarmMessageLogList)) {
            EmsAlarmMessageLog emsAlarmMessageLog = new EmsAlarmMessageLog(new EmsAlarmMessageLogPO());
            EmsAlarmMessageCreateDTO emsAlarmMessageCreateDTO = new EmsAlarmMessageCreateDTO();
            BeanUtils.copyProperties(getAlarmSettingByParamResultDTO, emsAlarmMessageCreateDTO);
            emsAlarmMessageLog.init(emsAlarmMessageCreateDTO);
            emsAlarmMessageLog.setAlarmCondition(alarmCondition);
            emsAlarmMessageLog.setAlarmContent(alarmContent);
            emsAlarmMessageLog.setCurrentValue(getUsedParamResultDTO.getTotalValue());
            emsAlarmMessageLog.setAlarmStatus("已处理");
            emsAlarmMessageLog.save();
        }
    }

    public Boolean autoAlarmTrigger() {
        tokenAnalysisService.genToken("60f7363e-ec33-4cf1-a596-54ced9ecfdeb");
        List<GetUsedParamResultDTO> getUsedParamResultList = emsParamInfoService.getUsedParamList();
        for (GetUsedParamResultDTO getUsedParamResultDTO : getUsedParamResultList) {
            GetAlarmSettingByParamDTO getAlarmSettingByParamDTO = new GetAlarmSettingByParamDTO();
            getAlarmSettingByParamDTO.setParamCode(getUsedParamResultDTO.getParamKey());
            getAlarmSettingByParamDTO.setDeviceCode(getUsedParamResultDTO.getDeviceCode());
            List<GetAlarmSettingByParamResultDTO> getAlarmSettingByParamResultList = alarmSettingService.getAlarmSettingByParamList(getAlarmSettingByParamDTO);
            for (GetAlarmSettingByParamResultDTO getAlarmSettingByParamResultDTO : getAlarmSettingByParamResultList) {
                String[] alarmConditionList = getAlarmSettingByParamResultDTO.getAlarmCondition().split(";");
                String[] normalFlagList = getAlarmSettingByParamResultDTO.getNormalFlag().split(";");
                String[] alarmContentList = getAlarmSettingByParamResultDTO.getAlarmContent().split(";");
                for (int i = 0; i < alarmConditionList.length; i++) {
                    if (StringUtils.equals(normalFlagList[i], "N")) {
                        String[] conditonList = alarmConditionList[i].split(" ");
                        List<EmsAlarmMessage> emsAlarmMessageList = emsAlarmMessageMapper.selectByExample(new Example.Builder(EmsAlarmMessagePO.class)
                                .where(WeekendSqls.<EmsAlarmMessagePO>custom()
                                        .andEqualTo(EmsAlarmMessagePO::getAlarmCondition, alarmConditionList[i]).andEqualTo(EmsAlarmMessagePO::getAlarmTypeCode, getAlarmSettingByParamResultDTO.getAlarmTypeCode())
                                        .andEqualTo(EmsAlarmMessagePO::getAlarmLevel, getAlarmSettingByParamResultDTO.getAlarmLevel()).andEqualTo(EmsAlarmMessagePO::getDeviceCode, getAlarmSettingByParamResultDTO.getDeviceCode())
                                        .andEqualTo(EmsAlarmMessagePO::getParamCode, getAlarmSettingByParamResultDTO.getParamCode())).forUpdate().build()).stream().map(EmsAlarmMessage::new).collect(Collectors.toList());
                        List<EmsAlarmMessageHistory> emsAlarmMessageHistoryList = emsAlarmMessageHistoryMapper.selectByExample(new Example.Builder(EmsAlarmMessageHistoryPO.class)
                                .where(WeekendSqls.<EmsAlarmMessageHistoryPO>custom()
                                        .andEqualTo(EmsAlarmMessageHistoryPO::getAlarmCondition, alarmConditionList[i]).andEqualTo(EmsAlarmMessageHistoryPO::getAlarmTypeCode, getAlarmSettingByParamResultDTO.getAlarmTypeCode())
                                        .andEqualTo(EmsAlarmMessageHistoryPO::getAlarmLevel, getAlarmSettingByParamResultDTO.getAlarmLevel()).andEqualTo(EmsAlarmMessageHistoryPO::getDeviceCode, getAlarmSettingByParamResultDTO.getDeviceCode())
                                        .andEqualTo(EmsAlarmMessageHistoryPO::getParamCode, getAlarmSettingByParamResultDTO.getParamCode())).forUpdate().build()).stream().map(EmsAlarmMessageHistory::new).collect(Collectors.toList());
                        List<EmsAlarmMessageHistory> emsAlarmMessageHistoryProcessedList = emsAlarmMessageHistoryMapper.selectByExample(new Example.Builder(EmsAlarmMessageHistoryPO.class)
                                .where(WeekendSqls.<EmsAlarmMessageHistoryPO>custom()
                                        .andEqualTo(EmsAlarmMessageHistoryPO::getAlarmCondition, alarmConditionList[i]).andEqualTo(EmsAlarmMessageHistoryPO::getAlarmTypeCode, getAlarmSettingByParamResultDTO.getAlarmTypeCode())
                                        .andEqualTo(EmsAlarmMessageHistoryPO::getAlarmLevel, getAlarmSettingByParamResultDTO.getAlarmLevel()).andEqualTo(EmsAlarmMessageHistoryPO::getDeviceCode, getAlarmSettingByParamResultDTO.getDeviceCode())
                                        .andEqualTo(EmsAlarmMessageHistoryPO::getParamCode, getAlarmSettingByParamResultDTO.getParamCode())
                                        .andEqualTo(EmsAlarmMessageHistoryPO::getAlarmStatus, "已处理")).forUpdate().build()).stream().map(EmsAlarmMessageHistory::new).collect(Collectors.toList());
                        List<EmsAlarmMessageLog> emsAlarmMessageLogList = emsAlarmMessageLogMapper.selectByExample(new Example.Builder(EmsAlarmMessageLogPO.class)
                                .where(WeekendSqls.<EmsAlarmMessageLogPO>custom()
                                        .andEqualTo(EmsAlarmMessageLogPO::getAlarmCondition, alarmConditionList[i]).andEqualTo(EmsAlarmMessageLogPO::getAlarmTypeCode, getAlarmSettingByParamResultDTO.getAlarmTypeCode())
                                        .andEqualTo(EmsAlarmMessageLogPO::getAlarmLevel, getAlarmSettingByParamResultDTO.getAlarmLevel()).andEqualTo(EmsAlarmMessageLogPO::getDeviceCode, getAlarmSettingByParamResultDTO.getDeviceCode())
                                        .andEqualTo(EmsAlarmMessageLogPO::getParamCode, getAlarmSettingByParamResultDTO.getParamCode())).forUpdate().build()).stream().map(EmsAlarmMessageLog::new).collect(Collectors.toList());
                        if (StringUtils.equals(conditonList[0], ">")) {
                            Double conditionValue = Double.parseDouble(conditonList[1]);
                            if (getUsedParamResultDTO.getTotalValue().compareTo(conditionValue) > 0) {
                                autoAlarmTriggerConfirmMultiplex(emsAlarmMessageList, emsAlarmMessageHistoryList, getAlarmSettingByParamResultDTO, getUsedParamResultDTO, alarmConditionList[i], alarmContentList[i]);
                            } else {
                                autoAlarmTriggerNotConfirmMultiplex(emsAlarmMessageList, emsAlarmMessageHistoryList, emsAlarmMessageHistoryProcessedList, emsAlarmMessageLogList, getAlarmSettingByParamResultDTO, getUsedParamResultDTO, alarmConditionList[i], alarmContentList[i]);
                            }
                        } else if (StringUtils.equals(conditonList[0], "<")) {
                            Double conditionValue = Double.parseDouble(conditonList[1]);
                            if (getUsedParamResultDTO.getTotalValue().compareTo(conditionValue) < 0) {
                                autoAlarmTriggerConfirmMultiplex(emsAlarmMessageList, emsAlarmMessageHistoryList, getAlarmSettingByParamResultDTO, getUsedParamResultDTO, alarmConditionList[i], alarmContentList[i]);
                            } else {
                                autoAlarmTriggerNotConfirmMultiplex(emsAlarmMessageList, emsAlarmMessageHistoryList, emsAlarmMessageHistoryProcessedList, emsAlarmMessageLogList, getAlarmSettingByParamResultDTO, getUsedParamResultDTO, alarmConditionList[i], alarmContentList[i]);
                            }
                        } else if (StringUtils.equals(conditonList[0], "=")) {
                            Double conditionValue = Double.parseDouble(conditonList[1]);
                            if (getUsedParamResultDTO.getTotalValue().compareTo(conditionValue) == 0) {
                                autoAlarmTriggerConfirmMultiplex(emsAlarmMessageList, emsAlarmMessageHistoryList, getAlarmSettingByParamResultDTO, getUsedParamResultDTO, alarmConditionList[i], alarmContentList[i]);
                            } else {
                                autoAlarmTriggerNotConfirmMultiplex(emsAlarmMessageList, emsAlarmMessageHistoryList, emsAlarmMessageHistoryProcessedList, emsAlarmMessageLogList, getAlarmSettingByParamResultDTO, getUsedParamResultDTO, alarmConditionList[i], alarmContentList[i]);
                            }
                        } else if (StringUtils.equals(conditonList[0], "≠")) {
                            Double conditionValue = Double.parseDouble(conditonList[1]);
                            if (getUsedParamResultDTO.getTotalValue().compareTo(conditionValue) != 0) {
                                autoAlarmTriggerConfirmMultiplex(emsAlarmMessageList, emsAlarmMessageHistoryList, getAlarmSettingByParamResultDTO, getUsedParamResultDTO, alarmConditionList[i], alarmContentList[i]);
                            } else {
                                autoAlarmTriggerNotConfirmMultiplex(emsAlarmMessageList, emsAlarmMessageHistoryList, emsAlarmMessageHistoryProcessedList, emsAlarmMessageLogList, getAlarmSettingByParamResultDTO, getUsedParamResultDTO, alarmConditionList[i], alarmContentList[i]);
                            }
                        } else if (StringUtils.equals(conditonList[0], "≥")) {
                            Double conditionValue = Double.parseDouble(conditonList[1]);
                            if (getUsedParamResultDTO.getTotalValue().compareTo(conditionValue) >= 0) {
                                autoAlarmTriggerConfirmMultiplex(emsAlarmMessageList, emsAlarmMessageHistoryList, getAlarmSettingByParamResultDTO, getUsedParamResultDTO, alarmConditionList[i], alarmContentList[i]);
                            } else {
                                autoAlarmTriggerNotConfirmMultiplex(emsAlarmMessageList, emsAlarmMessageHistoryList, emsAlarmMessageHistoryProcessedList, emsAlarmMessageLogList, getAlarmSettingByParamResultDTO, getUsedParamResultDTO, alarmConditionList[i], alarmContentList[i]);
                            }
                        } else if (StringUtils.equals(conditonList[0], "≤")) {
                            Double conditionValue = Double.parseDouble(conditonList[1]);
                            if (getUsedParamResultDTO.getTotalValue().compareTo(conditionValue) <= 0) {
                                autoAlarmTriggerConfirmMultiplex(emsAlarmMessageList, emsAlarmMessageHistoryList, getAlarmSettingByParamResultDTO, getUsedParamResultDTO, alarmConditionList[i], alarmContentList[i]);
                            } else {
                                autoAlarmTriggerNotConfirmMultiplex(emsAlarmMessageList, emsAlarmMessageHistoryList, emsAlarmMessageHistoryProcessedList, emsAlarmMessageLogList, getAlarmSettingByParamResultDTO, getUsedParamResultDTO, alarmConditionList[i], alarmContentList[i]);
                            }
                        }

                    }
                }
            }
        }
        return true;
    }

    public Object getEmsAlarmMessageHistoryList(GetEmsAlarmMessageHistoryDTO getEmsAlarmMessageHistoryDTO) throws ParseException {
        return emsAlarmMessageHistoryService.getEmsAlarmMessageHistoryList(getEmsAlarmMessageHistoryDTO);
    }

    public Boolean updateAlarmMessage(String gid) {
        emsAlarmMessageService.updateAlarmMessage(gid);
        return true;
    }

    public Object getEmsAlarmMessageHistoryByEventAggregationList(GetEmsAlarmMessageHistoryDTO getEmsAlarmMessageHistoryDTO) throws ParseException {
        return emsAlarmMessageHistoryService.getEmsAlarmMessageHistoryByEventAggregationList(getEmsAlarmMessageHistoryDTO);
    }

    public Object getEmsAlarmMessageList(GetEmsAlarmMessageHistoryDTO getEmsAlarmMessageHistoryDTO) throws ParseException {
        return emsAlarmMessageService.getEmsAlarmMessageList(getEmsAlarmMessageHistoryDTO);

    }
}