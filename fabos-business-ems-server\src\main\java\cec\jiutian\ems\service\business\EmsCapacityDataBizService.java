package cec.jiutian.ems.service.business;

import cec.jiutian.core.exception.MesErrorCodeException;
import cec.jiutian.ems.dto.CapacityDataCreateDTO;
import cec.jiutian.ems.dto.CapacityDataGetDTO;
import cec.jiutian.ems.dto.CapacityDataOutDTO;
import cec.jiutian.ems.po.EmsCapacityDataPO;
import cec.jiutian.ems.service.EmsCapacityDataService;
import io.swagger.models.auth.In;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Year;
import java.util.HashMap;
import java.util.List;

/**

 *
 * <AUTHOR>
 * @since 2023/6/19 creation
 */
@Slf4j
@Service
@Transactional
@AllArgsConstructor

public class EmsCapacityDataBizService {
    private final EmsCapacityDataService emsCapacityDataService;
    public Object insertCapacityData(CapacityDataCreateDTO capacityDataCreateDTO) {
       Object re= emsCapacityDataService.insertCapacityData(capacityDataCreateDTO);
       return  re;
    }

    public CapacityDataOutDTO getRegionCapacityByRegion(CapacityDataGetDTO capacityDataGetDTO) {

        if (capacityDataGetDTO.getTimeType().equals("year"))
                  capacityDataGetDTO.setMonth(null);
        else if(capacityDataGetDTO.getTimeType().equals("month"))
        {
            if (capacityDataGetDTO.getMonth()==null||capacityDataGetDTO.getMonth()<=0||capacityDataGetDTO.getMonth()>12)
            {
                throw new MesErrorCodeException("请选择正确月份后重试!");
            }
        }
        // 获取表格列数据table
        List<HashMap> tables= emsCapacityDataService.getTableInfos(capacityDataGetDTO);
        // 获取当月数据
        List<EmsCapacityDataPO> updateDatas = emsCapacityDataService.getUpdateDatas(capacityDataGetDTO);
        // 将当月数据进行组合成行数据
        List<HashMap<String, Object>> regionResult= emsCapacityDataService.getRegionCapacityByRegion(updateDatas);
        // 将当月数据进行组合全厂数据
        List<HashMap<String, Object>> wholePlantResults = emsCapacityDataService.setWholePlantData(regionResult);
        // 设置产能数据
        CapacityDataOutDTO capacityDataOutDTO = new CapacityDataOutDTO().setTables(tables).setRegionResults(regionResult).setWholePlantResults(wholePlantResults).setUpdateData(updateDatas);
        return  capacityDataOutDTO;
    }


    public List<EmsCapacityDataPO> getUpdateData(CapacityDataGetDTO capacityDataGetDTO) {
        if (capacityDataGetDTO.getTimeType().equals("year"))
            capacityDataGetDTO.setMonth(null);
        List<EmsCapacityDataPO> updateDatas = emsCapacityDataService.getUpdateDatas(capacityDataGetDTO);
        return updateDatas;
    }

    public Integer updateEmsCapacityDataPOs(List<EmsCapacityDataPO> updateDatas) {

       Integer updateAffects= emsCapacityDataService.updateEmsCapacityDataPOs(updateDatas);

       return updateAffects;
    }

    public Integer deleteEmsCapacityDataPOs(List<EmsCapacityDataPO> deleteDatas) {
        Integer deleteAffectsRows= emsCapacityDataService.deleteEmsCapacityDataPOs(deleteDatas);

        return deleteAffectsRows;

    }
}
