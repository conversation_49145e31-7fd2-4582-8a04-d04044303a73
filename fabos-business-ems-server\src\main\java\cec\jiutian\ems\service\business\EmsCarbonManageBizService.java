package cec.jiutian.ems.service.business;

import cec.jiutian.core.comn.util.BigDecimalUtils;
import cec.jiutian.core.exception.MesErrorCodeException;
import cec.jiutian.ems.domain.EmsConfig;
import cec.jiutian.ems.dto.GetCarbonEmissionResultDTO;
import cec.jiutian.ems.mapper.OverViewMapper;
import cec.jiutian.ems.po.EmsConfigPO;
import cec.jiutian.ems.po.EmsUsedDataDaySummaryPO;
import cec.jiutian.ems.po.EmsUsedDataMonthSummaryPO;
import cec.jiutian.ems.query.dto.*;
import cec.jiutian.ems.service.business.definition.BusinessDefinition;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestBody;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.GregorianCalendar;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 碳管理
 *
 * <AUTHOR> CodeGenerator
 * @date : 2023-6-15
 */
@Slf4j
@Service
@Transactional
public class EmsCarbonManageBizService {
    private final EmsUsedDataMonthSummaryBizService emsUsedDataMonthSummaryBizService;
    private final EmsUsedDataDaySummaryBizService emsUsedDataDaySummaryBizService;
    private final EmsConfigBizService emsConfigBizService;

    public EmsCarbonManageBizService(EmsUsedDataMonthSummaryBizService emsUsedDataMonthSummaryBizService,
                                     EmsUsedDataDaySummaryBizService emsUsedDataDaySummaryBizService,
                                     EmsConfigBizService emsConfigBizService) {
        this.emsUsedDataMonthSummaryBizService = emsUsedDataMonthSummaryBizService;
        this.emsUsedDataDaySummaryBizService = emsUsedDataDaySummaryBizService;
        this.emsConfigBizService = emsConfigBizService;
    }

    @Resource
    private OverViewMapper overViewMapper;

    public GetCarbonEmissionResultDTO getCarbonEmission(CarbonEmissionQueryDTO queryDTO) {
        // 从配置表中查询折算系数和碳配额,碳强度系数
        EmsConfigQueryDTO configQueryDTO = new EmsConfigQueryDTO();
        configQueryDTO.setType(EmsConfig.CONVERSION_COEFFICIENT);
        List<EmsConfigPO> configPOS = emsConfigBizService.getByConditions(configQueryDTO);
        List<String> energyTypeList = configPOS.stream().map(EmsConfigPO::getConfigCode).collect(Collectors.toList());

        configQueryDTO.setType(EmsConfig.CARBON_QUOTA);
        configQueryDTO.setCode(queryDTO.getYear().toString());
        List<EmsConfigPO> emsConfigPOS = emsConfigBizService.getByConditions(configQueryDTO);

        configQueryDTO.setType(EmsConfig.CII_COEFFICIENT);
        List<EmsConfigPO> emsConfigCiiPOS = emsConfigBizService.getByConditions(configQueryDTO);

        // 查询月度能源用量，并计算年度总用量
        List<EmsUsedDataMonthSummaryPO> usedDataMonthSummaryPOS = emsUsedDataMonthSummaryBizService.getMonthSummaryByYearAndTypes(queryDTO.getLocationName(), queryDTO.getYear(), energyTypeList);

        GetCarbonEmissionResultDTO resultDTO = new GetCarbonEmissionResultDTO();
        List<GetCarbonEmissionResultDTO.MonthEmission> monthEmissionList = new ArrayList<>();
        List<GetCarbonEmissionResultDTO.MonthEmission> monthEmissionTempList = new ArrayList<>();
        Double total = 0.00;

        if (CollectionUtils.isNotEmpty(usedDataMonthSummaryPOS)) {
            Map<String, List<EmsUsedDataMonthSummaryPO>> map = usedDataMonthSummaryPOS.stream().collect(Collectors.groupingBy(EmsUsedDataMonthSummaryPO::getParamType));
            map.forEach((k, v) -> {
                // 配置表中折算系数的code与param type比对, 注意：电的折算系数对应的单位是万kwh
                Double coe = configPOS.stream().filter(x -> x.getConfigCode().equals(k)).map(EmsConfigPO::getValue).findFirst().orElse(0d);
                for (EmsUsedDataMonthSummaryPO monthSummaryPO : v) {
                    GetCarbonEmissionResultDTO.MonthEmission monthEmission = new GetCarbonEmissionResultDTO.MonthEmission();
                    monthEmission.setDate(monthSummaryPO.getMonth());
                    monthEmission.setEmission(BigDecimalUtils.mul((k.equals("electricityConsumption") ? monthSummaryPO.getTotalValue() / 10000 : monthSummaryPO.getTotalValue()), coe));
                    monthEmissionTempList.add(monthEmission);
                }
            });
        }
        for (int i = 1; i <= 12; i++) {
            GetCarbonEmissionResultDTO.MonthEmission monthEmission = new GetCarbonEmissionResultDTO.MonthEmission();
            int finalI = i;
            Double emission = monthEmissionTempList.stream().filter(x -> x.getDate().equals(finalI)).mapToDouble(GetCarbonEmissionResultDTO.MonthEmission::getEmission).sum();
            monthEmission.setDate(i);
            monthEmission.setEmission(emission);
            monthEmissionList.add(monthEmission);
            total = BigDecimalUtils.add(total, emission);
        }
        //获取当前日期，如果queryDTO中的year是当前年，则需根据年开始到当前日期的天数来计算日均碳排量，否则以整年的天数计算日均碳排量
        Calendar cal = Calendar.getInstance();
        int dayNum;
        if (cal.get(Calendar.YEAR) == queryDTO.getYear()) {
            dayNum = cal.get(Calendar.DAY_OF_YEAR);
        } else {
            dayNum = new GregorianCalendar().isLeapYear(queryDTO.getYear()) ? 366 : 365;
        }

        resultDTO.setMonthEmissionList(monthEmissionList);
        // 重新结算
        OverViewQueryDTO overViewQueryDTO = new OverViewQueryDTO();
        overViewQueryDTO.setYear(queryDTO.getYear());
        overViewQueryDTO.setTimeType("year");
        overViewQueryDTO.setIsPage(false);
        if (queryDTO.getLocationName() != null) {
            overViewQueryDTO.setLocationName(queryDTO.getLocationName());
        }
        List<EnergyUsageDTO> factoryEnergy = overViewMapper.getFactoryEnergy(overViewQueryDTO);
        if(!factoryEnergy.isEmpty()) {
            double ele =  factoryEnergy.get(0).getElectricityConsumption();
            double gas =  factoryEnergy.get(0).getGasConsumption();
            total = ele*0.00009489 + gas * 0.0022;
        }

        resultDTO.setTotalEmission(total);
        resultDTO.setDayEmission(total/dayNum);
        // 碳配额计算和碳配额使用率
        if (CollectionUtils.isNotEmpty(emsConfigPOS)) {
            resultDTO.setTotalQuota(emsConfigPOS.get(0).getValue());
            resultDTO.setUseRate(resultDTO.getTotalQuota() == 0d ? null : BigDecimalUtils.div(resultDTO.getTotalEmission(), resultDTO.getTotalQuota()));
        }

        //折算系数和碳强度系数
        List<GetCarbonEmissionResultDTO.ConversionCoefficient> conversionCoefficients = new ArrayList<>();
        configPOS.forEach(c -> {
            GetCarbonEmissionResultDTO.ConversionCoefficient conversionCoefficient = new GetCarbonEmissionResultDTO.ConversionCoefficient();
            switch (c.getConfigCode()) {
                case "electricityConsumption":
                    conversionCoefficient.setParamType(BusinessDefinition.EnergyTypes.ENERGY_TYPE_ELECTRICITY);
                    conversionCoefficient.setUnit("Kwh");
                    break;
                case "waterConsumption":
                    conversionCoefficient.setParamType(BusinessDefinition.EnergyTypes.ENERGY_TYPE_WATER);
                    conversionCoefficient.setUnit("立方米");
                    break;
                case "gasConsumption":
                    conversionCoefficient.setParamType(BusinessDefinition.EnergyTypes.ENERGY_TYPE_GAS);
                    conversionCoefficient.setUnit("立方米");
                    break;
            }
            conversionCoefficient.setCoefficient(c.getValue());
            conversionCoefficient.setConversion(c.getConfigName());
            conversionCoefficients.add(conversionCoefficient);
        });
        resultDTO.setConversionCoefficientList(conversionCoefficients);
        resultDTO.setCiiCoefficient(CollectionUtils.isNotEmpty(emsConfigCiiPOS) ? emsConfigCiiPOS.get(0).getValue() : null);

        return resultDTO;
    }

    public List<GetCarbonEmissionResultDTO.MonthEmission> getCarbonEmissionByMonth(CarbonEmissionQueryDTO queryDTO) {
        if (null == queryDTO.getMonth()) {
            throw new MesErrorCodeException("月份必选");
        }
        // 从配置表中查询折算系数
        EmsConfigQueryDTO configQueryDTO = new EmsConfigQueryDTO();
        configQueryDTO.setType(EmsConfig.CONVERSION_COEFFICIENT);
        List<EmsConfigPO> configPOS = emsConfigBizService.getByConditions(configQueryDTO);
        List<String> energyTypeList = configPOS.stream().map(EmsConfigPO::getConfigCode).collect(Collectors.toList());

        //根据年月获取每日能源用量
        List<EmsUsedDataDaySummaryPO> daySummaryPOS = emsUsedDataDaySummaryBizService.getByMonthAndTypes(queryDTO.getLocationName(), queryDTO.getYear(), queryDTO.getMonth(), energyTypeList);

        List<GetCarbonEmissionResultDTO.MonthEmission> dayEmissionList = new ArrayList<>();
        List<GetCarbonEmissionResultDTO.MonthEmission> dayEmissionTempList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(daySummaryPOS)) {
            Map<String, List<EmsUsedDataDaySummaryPO>> map = daySummaryPOS.stream().collect(Collectors.groupingBy(EmsUsedDataDaySummaryPO::getParamType));
            map.forEach((k, v) -> {
                Double coe = configPOS.stream().filter(x -> x.getConfigCode().equals(k)).map(EmsConfigPO::getValue).findFirst().orElse(0d);
                for (EmsUsedDataDaySummaryPO daySummaryPO : v) {
                    GetCarbonEmissionResultDTO.MonthEmission dayEmission = new GetCarbonEmissionResultDTO.MonthEmission();
                    dayEmission.setDate(daySummaryPO.getDay());
                    dayEmission.setEmission(BigDecimalUtils.mul((k.equals("electricityConsumption") ? daySummaryPO.getTotalValue() / 10000 : daySummaryPO.getTotalValue()), coe));
                    dayEmissionTempList.add(dayEmission);
                }
            });
        }
        // 获取月份的天数
        Calendar cal = Calendar.getInstance();
        cal.set(Calendar.YEAR, queryDTO.getYear());
        cal.set(Calendar.MONTH, queryDTO.getMonth() - 1);
        cal.set(Calendar.DATE, 1);
        cal.roll(Calendar.DATE, -1);
        int dayNum = cal.getActualMaximum(Calendar.DATE);

        for (int i = 1; i <= dayNum; i++) {
            GetCarbonEmissionResultDTO.MonthEmission dayEmission = new GetCarbonEmissionResultDTO.MonthEmission();
            int finalI = i;
            Double emission = dayEmissionTempList.stream().filter(x -> x.getDate().equals(finalI)).mapToDouble(GetCarbonEmissionResultDTO.MonthEmission::getEmission).sum();
            dayEmission.setDate(i);
            dayEmission.setEmission(emission);
            dayEmissionList.add(dayEmission);
        }
        return dayEmissionList;
    }

}