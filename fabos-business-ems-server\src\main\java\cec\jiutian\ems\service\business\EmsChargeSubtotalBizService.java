package cec.jiutian.ems.service.business;

import cec.jiutian.ems.dto.*;
import cec.jiutian.ems.service.EmsChargeSubtotalService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Slf4j
@Service
@Transactional
@AllArgsConstructor
public class EmsChargeSubtotalBizService {
    private final EmsChargeSubtotalService emsChargeSubtotalService;

    public GetTotalChargeResultDTO getTotalChargeList(GetTotalChargeDTO getTotalChargeDTO) {
        return emsChargeSubtotalService.getTotalChargeList(getTotalChargeDTO);
    }

    public List<GetEmsChargeSubtotalForBarResultDTO> getEmsChargeSubtotalForBarList(GetTotalChargeDTO getTotalChargeDTO) {
        return emsChargeSubtotalService.getEmsChargeSubtotalForBarList(getTotalChargeDTO);
    }

    public List<GetEmsChargeSubtotalForPieResultDTO> getEmsChargeSubtotalForPieList(GetTotalChargeDTO getTotalChargeDTO) {
        return emsChargeSubtotalService.getEmsChargeSubtotalForPieList(getTotalChargeDTO);
    }

    public List<GetEmsChargeSubtotalForTableResultDTO> getEmsChargeSubtotalForTableList(GetTotalChargeDTO getTotalChargeDTO) {
        return emsChargeSubtotalService.getEmsChargeSubtotalForTableList(getTotalChargeDTO);
    }
}
