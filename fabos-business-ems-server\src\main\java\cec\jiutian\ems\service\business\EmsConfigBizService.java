package cec.jiutian.ems.service.business;

import cec.jiutian.ems.po.EmsConfigPO;
import cec.jiutian.ems.query.dto.EmsConfigQueryDTO;
import cec.jiutian.ems.service.EmsConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * Ems Config
 *
 * <AUTHOR> CodeGenerator
 * @date : 2023-6-15
 */
@Slf4j
@Service
@Transactional
public class EmsConfigBizService {
    private final EmsConfigService emsConfigService;

    public EmsConfigBizService(EmsConfigService emsConfigService) {
        this.emsConfigService = emsConfigService;
    }

    public List<EmsConfigPO> getByConditions(EmsConfigQueryDTO queryDTO) {
        return emsConfigService.getByConditions(queryDTO);
    }

}