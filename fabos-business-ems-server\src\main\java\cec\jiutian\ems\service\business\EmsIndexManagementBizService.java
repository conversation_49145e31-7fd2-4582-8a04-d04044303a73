package cec.jiutian.ems.service.business;

import cec.jiutian.core.comn.util.BeanUtils;
import cec.jiutian.core.comn.util.StringUtils;
import cec.jiutian.ems.dto.EnergyConsumptionUsageResultDTO;
import cec.jiutian.ems.dto.GetEnergyConsumptionIndexResultDTO;
import cec.jiutian.ems.dto.IndexSettingCreateDTO;
import cec.jiutian.ems.po.EmsConfigPO;
import cec.jiutian.ems.po.EmsIndexSettingPO;
import cec.jiutian.ems.po.EmsLocationTreePO;
import cec.jiutian.ems.query.dto.EmsConfigQueryDTO;
import cec.jiutian.ems.query.dto.EnergyConsumptionIndexQueryDTO;
import cec.jiutian.ems.query.dto.EnergyConsumptionQueryDTO;
import cec.jiutian.ems.service.EmsIndexSettingService;
import cec.jiutian.ems.service.business.definition.BusinessDefinition;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * 指标管理
 *
 * <AUTHOR> CodeGenerator
 * @date : 2023-6-19
 */
@Slf4j
@Service
@Transactional
public class EmsIndexManagementBizService {
    private final EmsUsedDataMonthSummaryBizService emsUsedDataMonthSummaryBizService;
    private final EmsIndexSettingService emsIndexSettingService;
    private final EmsLocationTreeBizService emsLocationTreeBizService;
    private final EmsConfigBizService emsConfigBizService;

    public EmsIndexManagementBizService(EmsUsedDataMonthSummaryBizService emsUsedDataMonthSummaryBizService,
                                        EmsIndexSettingService emsIndexSettingService,
                                        EmsLocationTreeBizService emsLocationTreeBizService,
                                        EmsConfigBizService emsConfigBizService) {
        this.emsUsedDataMonthSummaryBizService = emsUsedDataMonthSummaryBizService;
        this.emsIndexSettingService = emsIndexSettingService;
        this.emsLocationTreeBizService = emsLocationTreeBizService;
        this.emsConfigBizService = emsConfigBizService;
    }

    public void createIndex(IndexSettingCreateDTO opDTO) {
        emsIndexSettingService.create(opDTO);
    }

    public void updateIndex(IndexSettingCreateDTO opDTO) {
        emsIndexSettingService.update(opDTO);
    }

    public EmsIndexSettingPO getSetting(EnergyConsumptionIndexQueryDTO queryDTO) {
        return emsIndexSettingService.getSetting(queryDTO);
    }

    public Object getEnergyConsumptionIndex(EnergyConsumptionIndexQueryDTO queryDTO) {
        List<GetEnergyConsumptionIndexResultDTO> list = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(queryDTO.getLocationGidList())) {
            // 根据年、位置和能源类型从指标设定表获取基准值，
            List<EmsIndexSettingPO> settingPOS = emsIndexSettingService.getSettingList(queryDTO);

            // 根据年、位置和能源类型从monthSummary表中获取各位置每个月的总能源用量
            EnergyConsumptionQueryDTO consumptionQueryDTO = new EnergyConsumptionQueryDTO();
            BeanUtils.copyProperties(queryDTO, consumptionQueryDTO);
            List<EnergyConsumptionUsageResultDTO> monthUsageList = emsUsedDataMonthSummaryBizService.getSummarizedByLocationGidListAndYear(consumptionQueryDTO);

            // 根据位置id获取位置名称
            List<EmsLocationTreePO> locationTreePOS = emsLocationTreeBizService.getByGids(queryDTO.getLocationGidList());

            // 根据能源类型获取配置表中的单位
            EmsConfigQueryDTO configQueryDTO = new EmsConfigQueryDTO();
            configQueryDTO.setCode(queryDTO.getEnergyType());
            configQueryDTO.setType("unit");
            List<EmsConfigPO> configPOS = emsConfigBizService.getByConditions(configQueryDTO);

            queryDTO.getLocationGidList().forEach(l -> {
                GetEnergyConsumptionIndexResultDTO resultDTO = new GetEnergyConsumptionIndexResultDTO();
                settingPOS.stream().filter(x -> x.getLocationTreeGid().equals(l)).findFirst().ifPresent(p -> BeanUtils.copyProperties(p, resultDTO));
                if (CollectionUtils.isNotEmpty(monthUsageList)) {
                    monthUsageList.forEach(u -> {
                        if (u.getLocationGid().equals(l)) {
                            Double actual = u.getUsage().doubleValue();
                            switch (u.getTime()) {
                                case "1":
                                    resultDTO.setActualJan(actual);
                                    break;
                                case "2":
                                    resultDTO.setActualFeb(actual);
                                    break;
                                case "3":
                                    resultDTO.setActualMar(actual);
                                    break;
                                case "4":
                                    resultDTO.setActualApr(actual);
                                    break;
                                case "5":
                                    resultDTO.setActualMay(actual);
                                    break;
                                case "6":
                                    resultDTO.setActualJun(actual);
                                    break;
                                case "7":
                                    resultDTO.setActualJul(actual);
                                    break;
                                case "8":
                                    resultDTO.setActualAug(actual);
                                    break;
                                case "9":
                                    resultDTO.setActualSep(actual);
                                    break;
                                case "10":
                                    resultDTO.setActualOct(actual);
                                    break;
                                case "11":
                                    resultDTO.setActualNov(actual);
                                    break;
                                case "12":
                                    resultDTO.setActualDec(actual);
                                    break;
                            }
                        }
                    });
                }
                locationTreePOS.stream().filter(x -> x.getGid().equals(l)).findFirst().ifPresent(p -> resultDTO.setLocationName(p.getLocationName()));
                if (StringUtils.isBlank(resultDTO.getEnergyType())) {
                    resultDTO.setEnergyType(queryDTO.getEnergyType());
                }
                switch (resultDTO.getEnergyType()) {
                    case BusinessDefinition.ParamTypes.PARAM_TYPE_ELECTRICITY:
                        resultDTO.setEnergyName(BusinessDefinition.EnergyTypes.ENERGY_TYPE_ELECTRICITY);
                        break;
                    case BusinessDefinition.ParamTypes.PARAM_TYPE_WATER:
                        resultDTO.setEnergyName(BusinessDefinition.EnergyTypes.ENERGY_TYPE_WATER);
                        break;
                    case BusinessDefinition.ParamTypes.PARAM_TYPE_GAS:
                        resultDTO.setEnergyName(BusinessDefinition.EnergyTypes.ENERGY_TYPE_GAS);
                        break;
                }
                if (StringUtils.isBlank(resultDTO.getUnit())) {
                    resultDTO.setUnit(CollectionUtils.isEmpty(configPOS) ? null : configPOS.get(0).getValueStr());
                }

                list.add(resultDTO);
            });
        }

        return list;
    }

}