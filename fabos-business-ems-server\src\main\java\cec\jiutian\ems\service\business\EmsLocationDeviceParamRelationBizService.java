
package cec.jiutian.ems.service.business;

import cec.jiutian.ems.domain.EmsLocationTree;
import cec.jiutian.ems.po.EmsParamInfoPO;
import cec.jiutian.ems.query.dto.LocationDeviceParamRelationQueryDTO;
import cec.jiutian.ems.service.EmsLocationDeviceParamRelationService;
import cec.jiutian.ems.service.EmsLocationTreeService;
import cec.jiutian.ems.service.EmsParamInfoService;
import cec.jiutian.ems.utils.Assert;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * EMS Location Device Param Relation;位置树与测点关系
 *
 * <AUTHOR> CodeGenerator
 * @date : 2023-5-29
 */
@Slf4j
@Service
@Transactional
public class EmsLocationDeviceParamRelationBizService {
    private final EmsLocationDeviceParamRelationService emsLocationDeviceParamRelationService;
    private final EmsLocationTreeService emsLocationTreeService;
    private final EmsParamInfoService emsParamInfoService;

    public EmsLocationDeviceParamRelationBizService(EmsLocationDeviceParamRelationService emsLocationDeviceParamRelationService,
                                                    EmsLocationTreeService emsLocationTreeService,
                                                    EmsParamInfoService emsParamInfoService) {
        this.emsLocationDeviceParamRelationService = emsLocationDeviceParamRelationService;
        this.emsLocationTreeService = emsLocationTreeService;
        this.emsParamInfoService = emsParamInfoService;
    }

    public Object getRelationshipList(LocationDeviceParamRelationQueryDTO param) {
        return emsLocationDeviceParamRelationService.getRelationshipListCreateTime(param);
    }

    public void bindToLocation(LocationDeviceParamRelationQueryDTO param) {
        Assert.notBlank("绑定类型不能为空", param.getBindType());
        Assert.notBlank("LocationGid不能为空", param.getLocationGid());
        Assert.notEmpty("ParamInfoGid不能为空", param.getParamInfoGidList());
        EmsLocationTree locationTree = emsLocationTreeService.checkExistById(param.getLocationGid());
        List<EmsParamInfoPO> paramInfoPOS = emsParamInfoService.getParamInfoListByIdList(param.getParamInfoGidList());
        Assert.equalTo("未查询到测点", paramInfoPOS.size(), param.getParamInfoGidList().size());
        emsLocationDeviceParamRelationService.bindToLocation(locationTree, paramInfoPOS, param.getBindType());
    }

    public void unbindFromLocation(LocationDeviceParamRelationQueryDTO param) {
        Assert.notBlank("关系Gid不能为空", param.getGid());
        emsLocationDeviceParamRelationService.unbindFromLocation(param.getGid());
    }

    public void unbindAllFromLocation(LocationDeviceParamRelationQueryDTO param) {
        Assert.notBlank("LocationGid不能为空", param.getLocationGid());
        emsLocationTreeService.checkExistById(param.getLocationGid());
        emsLocationDeviceParamRelationService.unbindAllFromLocation(param.getLocationGid());
    }
}