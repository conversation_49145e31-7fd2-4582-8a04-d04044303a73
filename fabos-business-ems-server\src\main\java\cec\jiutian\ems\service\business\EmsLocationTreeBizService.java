package cec.jiutian.ems.service.business;

import cec.jiutian.core.comn.util.StringUtils;
import cec.jiutian.ems.dto.*;
import cec.jiutian.ems.po.EmsLocationTreePO;
import cec.jiutian.ems.query.dto.LocationTreeQueryDTO;
import cec.jiutian.ems.service.EmsLocationDeviceParamRelationService;
import cec.jiutian.ems.service.EmsLocationTreeService;
import cec.jiutian.ems.utils.Assert;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * EMS location tree;位置树表
 *
 * <AUTHOR> CodeGenerator
 * @date : 2023-5-29
 */
@Slf4j
@Service
@Transactional
public class EmsLocationTreeBizService {
    private final EmsLocationTreeService emsLocationTreeService;
    private final EmsLocationDeviceParamRelationService emsLocationDeviceParamRelationService;

    public EmsLocationTreeBizService(EmsLocationTreeService emsLocationTreeService,
                                     EmsLocationDeviceParamRelationService emsLocationDeviceParamRelationService) {
        this.emsLocationTreeService = emsLocationTreeService;
        this.emsLocationDeviceParamRelationService = emsLocationDeviceParamRelationService;
    }

    public List<EmsLocationTreeResultDTO> getAllTopLocationsByTypeAndCode(LocationTreeQueryDTO dto) {
//        Assert.notBlank("", dto.getLocationType());
//        Assert.notBlank("", dto.getEnergyCode());
        LocationTreeQueryDTO param = new LocationTreeQueryDTO();
        param.setLocationLevel(StringUtils.isBlank(dto.getLocationLevel()) ? "1" : dto.getLocationLevel());
        param.setLocationType(dto.getLocationType());
        param.setEnergyCode(dto.getEnergyCode());
        return emsLocationTreeService.getLocationTree(param);
    }

    public List<EmsLocationTreeResultDTO> getLocationsByParentGid(LocationTreeQueryDTO dto) {
        Assert.notBlank("父节点Gid不能为空", dto.getParentGid());
        LocationTreeQueryDTO param = new LocationTreeQueryDTO();
        param.setParentGid(dto.getParentGid());
        return emsLocationTreeService.getLocationTree(param);
    }

    public List<EmsLocationTreeResultDTO> getLocationsByTopParentGid(LocationTreeQueryDTO dto) {
        Assert.notBlank("父节点Gid不能为空", dto.getParentGid());
        LocationTreeQueryDTO param = new LocationTreeQueryDTO();
        param.setTopParentGid(dto.getTopParentGid());
        return emsLocationTreeService.getLocationTree(param);
    }

    public List<EmsLocationTreeResultDTO> getLocationsByName(LocationTreeQueryDTO dto) {
        Assert.notBlank("LocationGid不能为空", dto.getLocationName());
        LocationTreeQueryDTO param = new LocationTreeQueryDTO();
        param.setLocationName(dto.getLocationName());
        return emsLocationTreeService.getLocationTree(param);
    }

    public void saveLocationTree(EmsLocationTreePO dto) {
        Assert.notBlank("节点类型不能为空", dto.getLocationType());
//        Assert.notBlank("能源类型不能为空", dto.getEnergyCode());
        Assert.notBlank("节点名称不能为空", dto.getLocationName());
        emsLocationTreeService.saveLocationTree(dto);
    }

    public void updateLocationTreeName(LocationTreeModDTO dto) {
        Assert.notBlank("LocationGid不能为空", dto.getGid());
        Assert.notBlank("节点名称不能为空", dto.getLocationName());
        emsLocationTreeService.updateLocationTreeName(dto);
    }

    public void removeLocationTree(LocationTreeModDTO dto) {
        Assert.notBlank("LocationGid不能为空", dto.getGid());
        emsLocationTreeService.checkExistById(dto.getGid());
        emsLocationTreeService.checkChildNodeExists(dto.getGid());
        if (emsLocationDeviceParamRelationService.checkBindingExistsByLocationGid(dto.getGid())) {
            emsLocationDeviceParamRelationService.unbindAllFromLocation(dto.getGid());
        }
        emsLocationTreeService.removeLocationTree(dto);
    }

    public void updateSortSeq(LocationTreeModDTO dto) {
        Assert.notBlank("LocationGid不能为空", dto.getParentGid());
        Assert.notEmpty("排序参数不能为空", dto.getSortSeqMap());
        emsLocationTreeService.updateSortSeq(dto);
    }

    public Object getAllTrees(LocationTreeQueryDTO param) {

        return emsLocationTreeService.getAllTrees(param);
    }


    public List<EmsLocationTreePO> getByGids(List<String> locationGidList) {
        return emsLocationTreeService.getByGids(locationGidList);
    }

    public Object getTreeChild(LocationTreeQueryDTO param) {
        return emsLocationTreeService.getTreeChild(param);
    }


    public Object getLocationType() { return emsLocationTreeService.getLocationType();
    }

    public Object getEnergyType() { return emsLocationTreeService.getEnergyType();
    }

    public List<GetElectricTopologyDTO> getElectricTopology(GetElectricTopologyDTO getElectricTopologyDTO) {
        return emsLocationTreeService.getElectricTopology(getElectricTopologyDTO);
    }

    public List<WaterTopologyDTO> getMapByLocationType(GetWaterTopologyDTO getWaterTopologyDTO) {
        return emsLocationTreeService.getMapByLocationType(getWaterTopologyDTO);
    }
}
