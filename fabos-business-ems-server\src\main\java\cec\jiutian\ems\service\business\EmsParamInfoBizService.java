
package cec.jiutian.ems.service.business;

import cec.jiutian.ems.dto.GetDeviceParamDTO;
import cec.jiutian.ems.po.EmsParamInfoPO;
import cec.jiutian.ems.query.dto.ParamInfoQueryDTO;
import cec.jiutian.ems.service.EmsLocationDeviceParamRelationService;
import cec.jiutian.ems.service.EmsParamInfoService;
import cec.jiutian.ems.utils.Assert;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * Ems Param Info;模型参数信息表
 *
 * <AUTHOR> CodeGenerator
 * @date : 2023-5-29
 */
@Slf4j
@Service
@Transactional
public class EmsParamInfoBizService {
    private final EmsParamInfoService emsParamInfoService;
    private final EmsLocationDeviceParamRelationService emsLocationDeviceParamRelationService;

    public EmsParamInfoBizService(EmsParamInfoService emsParamInfoService,
                                  EmsLocationDeviceParamRelationService emsLocationDeviceParamRelationService) {
        this.emsParamInfoService = emsParamInfoService;
        this.emsLocationDeviceParamRelationService = emsLocationDeviceParamRelationService;
    }

    public Object getParamInfoList(ParamInfoQueryDTO dto) {
        return emsParamInfoService.getParamInfoListCreateTime(dto);
    }

    public void saveParamInfo(EmsParamInfoPO po) {
        emsParamInfoService.saveParamInfo(po);
    }

    public void updateParamInfo(EmsParamInfoPO po) {
        emsParamInfoService.updateParamInfo(po);
    }

    public void removeParamInfo(EmsParamInfoPO po) {
        emsParamInfoService.checkExistById(po.getGid());
        Assert.isFalse("此测点已被节点绑定，无法删除", emsLocationDeviceParamRelationService.checkBindingExistsByParamGid(po.getGid()));
        emsParamInfoService.removeParamInfo(po);
    }

    public Object getDeviceParamList(GetDeviceParamDTO getDeviceParamDTO) {
        return emsParamInfoService.getDeviceParamList(getDeviceParamDTO);
    }

}