package cec.jiutian.ems.service.business;


import cec.jiutian.ems.service.EmsRealTimeAlarmMessageService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.ParseException;

/**
 * EMS Real Time Alarm Message;实时报警大屏信息
 *
 * <AUTHOR> wangjj
 * @date : 2023-11-13
 */
@Slf4j
@Service
@Transactional
public class EmsRealTimeAlarmMessageBizService {
    private final EmsRealTimeAlarmMessageService emsRealTimeAlarmMessageService;

    public EmsRealTimeAlarmMessageBizService(EmsRealTimeAlarmMessageService emsRealTimeAlarmMessageService) {
        this.emsRealTimeAlarmMessageService = emsRealTimeAlarmMessageService;
    }

    public Object getEmsAlarmLevelSummary() throws ParseException {
        return emsRealTimeAlarmMessageService.getEmsAlarmLevelSummary();
    }

    public Object getEmsAlarmClassSummary() throws ParseException {
        return emsRealTimeAlarmMessageService.getEmsAlarmClassSummary();
    }

    public Object getEmsAlarmFactorySummary() throws ParseException {
        return emsRealTimeAlarmMessageService.getEmsAlarmFactorySummary();
    }
}
