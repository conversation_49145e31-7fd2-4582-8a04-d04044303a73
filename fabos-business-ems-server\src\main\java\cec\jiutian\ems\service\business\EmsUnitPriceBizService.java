package cec.jiutian.ems.service.business;

import cec.jiutian.ems.dto.EmsUnitPriceCreateDTO;
import cec.jiutian.ems.dto.EmsUnitPriceDeleteDTO;
import cec.jiutian.ems.dto.EmsUnitPriceUpdateDTO;
import cec.jiutian.ems.dto.GetEmsUnitPriceDTO;
import cec.jiutian.ems.service.EmsUnitPriceService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
/**
 * 能源单价;
 * <AUTHOR> CodeGenerator
 * @date : 2023-6-19
 */
@Slf4j
@Service
@Transactional
public class EmsUnitPriceBizService {
    private final EmsUnitPriceService emsUnitPriceService;

    public EmsUnitPriceBizService(EmsUnitPriceService emsUnitPriceService) {
        this.emsUnitPriceService = emsUnitPriceService;
    }

    public Object getEmsUnitPriceList(GetEmsUnitPriceDTO getEmsUnitPriceDTO) {
        return emsUnitPriceService.getEmsUnitPriceList(getEmsUnitPriceDTO);
    }

    public Boolean create(EmsUnitPriceCreateDTO emsUnitPriceCreateDTO) {
        return emsUnitPriceService.create(emsUnitPriceCreateDTO);
    }

    public Boolean update(EmsUnitPriceUpdateDTO emsUnitPriceUpdateDTO) {
        return emsUnitPriceService.update(emsUnitPriceUpdateDTO);
    }

    public Boolean delete(EmsUnitPriceDeleteDTO emsUnitPriceDeleteDTO) {
        return emsUnitPriceService.delete(emsUnitPriceDeleteDTO);
    }

    public Object getEmsUnitPriceLineList(GetEmsUnitPriceDTO getEmsUnitPriceDTO) {
        return emsUnitPriceService.getEmsUnitPriceLineList(getEmsUnitPriceDTO);
    }

}
