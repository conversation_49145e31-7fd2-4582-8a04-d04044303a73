
package cec.jiutian.ems.service.business;
import cec.jiutian.ems.bo.MyBO;
import cec.jiutian.ems.constant.EmsConstant;
import cec.jiutian.ems.dto.GetEmsUseDataDTO;
import cec.jiutian.ems.dto.UsedDataOutDTO;
import cec.jiutian.ems.po.EmsLocationDeviceParamRelationPO;
import cec.jiutian.ems.po.EmsParamInfoPO;
import cec.jiutian.ems.po.EmsUsedDataPO;
import cec.jiutian.ems.query.dto.LocationDeviceParamRelationQueryDTO;
import cec.jiutian.ems.query.dto.ParamInfoQueryDTO;
import cec.jiutian.ems.query.dto.UsedDataQueryDTO;
import cec.jiutian.ems.service.*;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Ems Used Data;使用数据
 * <AUTHOR> CodeGenerator
 * @date : 2023-5-29
 */
@Slf4j
@Service
@Transactional
@AllArgsConstructor
public class EmsUsedDataBizService {
    private final EmsUsedDataService emsUsedDataService;
    private final EmsUsedDataDaySummaryService emsUsedDataDaySummaryService;
    private final EmsUsedDataMonthSummaryService emsUsedDataMonthSummaryService;
    private final EmsParamInfoService emsParamInfoService;

    private final EmsLocationDeviceParamRelationService emsLocationDeviceParamRelationService;


    public List<UsedDataOutDTO> getUsedDataByLocId(UsedDataQueryDTO usedDataQueryDTO, List<String> gid) {
        List<UsedDataOutDTO> result=new ArrayList<>();
        for (int i = 0; i < gid.size(); i++) {
            List<UsedDataOutDTO> usedDataOutDTOS = getUsedDataOutDTOS(usedDataQueryDTO.setGid(gid.get(i)));
         if (usedDataOutDTOS!=null&& !usedDataOutDTOS.isEmpty())
            result.addAll(usedDataOutDTOS);
        }
        // 查到该设备有几个点位
        return result;
    }


    private List<UsedDataOutDTO> getUsedDataOutDTOS(UsedDataQueryDTO usedDataQueryDTO) {
        List<EmsLocationDeviceParamRelationPO>  relationshipList = (List<EmsLocationDeviceParamRelationPO>)emsLocationDeviceParamRelationService.getRelationshipList(new LocationDeviceParamRelationQueryDTO().setLocationGid(usedDataQueryDTO.getGid()));
        if (relationshipList==null||relationshipList.size()==0)
            return null;
        List<String> collect = relationshipList.stream().map(x -> x.getParamName()).collect(Collectors.toList());
        List<UsedDataOutDTO> result =  emsUsedDataService.getUsedDataByParamList(usedDataQueryDTO.setParamList(collect).setGid(relationshipList.get(0).getLocationGid()));
        return result;
    }

    public Object getUseDataByIDAndPoint(UsedDataQueryDTO usedDataQueryDTO) {
        return  getMonitDataByDay(usedDataQueryDTO);
    }

    private  List<EmsUsedDataPO> getMonitDataByDay(UsedDataQueryDTO usedDataQueryDTO) {
        List<EmsParamInfoPO> paramInfoList = emsParamInfoService.getParamInfoListNotByPage(new ParamInfoQueryDTO().setParamType(usedDataQueryDTO.getPointGroup()));
        List<String> collect = paramInfoList.stream().map(x -> x.getParamName()).collect(Collectors.toList());
        List<String> pn = new ArrayList<>();
        pn.add(usedDataQueryDTO.getParamName());

        if (pn != null && pn.size()!=0) {
            List<String> keys=  emsLocationDeviceParamRelationService.getRelationshipByParamAndLocGid(usedDataQueryDTO.getGid(),pn);
            usedDataQueryDTO.setKeys(keys);
            List<EmsUsedDataPO> result =  emsUsedDataService.getUseDataByIDAndPoint(usedDataQueryDTO);
            return result;
        }
        return null;
    }
    private  Object getMonitDataByMonth(UsedDataQueryDTO usedDataQueryDTO) {
        List<EmsParamInfoPO> paramInfoList =
                emsParamInfoService.getParamInfoListNotByPage(new ParamInfoQueryDTO().setParamType(usedDataQueryDTO.getPointGroup()));
        List<String> collect = paramInfoList.stream().map(x -> x.getParamName()).collect(Collectors.toList());
        if (collect != null&&collect.size()!=0) {
            List<String> keys=  emsLocationDeviceParamRelationService.getRelationshipByParamAndLocGid(usedDataQueryDTO.getGid(),collect);
            usedDataQueryDTO.setKeys(keys);
            Object result =  emsUsedDataMonthSummaryService.getUseDataByYear(usedDataQueryDTO);
            return result;
        }
        return null;
    }
    private  Object getMonitDataByWeek(UsedDataQueryDTO usedDataQueryDTO) {
        List<EmsParamInfoPO> paramInfoList =
                emsParamInfoService.getParamInfoListNotByPage(new ParamInfoQueryDTO().setParamType(usedDataQueryDTO.getPointGroup()));
        List<String> collect = paramInfoList.stream().map(x -> x.getParamName()).collect(Collectors.toList());
        if (collect != null&&collect.size()!=0) {
            List<String> keys=  emsLocationDeviceParamRelationService.getRelationshipByParamAndLocGid(usedDataQueryDTO.getGid(),collect);
            usedDataQueryDTO.setKeys(keys);
            Object result =  emsUsedDataDaySummaryService.getUseDataByMonth(usedDataQueryDTO);
            return result;
        }
        return null;
    }

    public List<String> getChildrenInfo(UsedDataQueryDTO usedDataQueryDTO) {
        List<String> re=   emsUsedDataService.getChildrenInfo(usedDataQueryDTO);
        return re;
    }

    public void myInsert() {
        emsUsedDataService.myInsert();
    }

    public List<GetEmsUseDataDTO> getPrice(List<MyBO> list) {
         return emsUsedDataService.getPrice(list);
    }

    public void myInsert2() {
        emsUsedDataService.myInsert2();
    }
}
