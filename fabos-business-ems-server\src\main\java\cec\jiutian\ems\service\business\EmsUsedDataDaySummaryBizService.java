package cec.jiutian.ems.service.business;

import cec.jiutian.core.service.TokenAnalysisService;
import cec.jiutian.ems.domain.EmsUsedDataDaySummary;
import cec.jiutian.ems.mapper.EmsUsedDataDaySummaryMapper;
import cec.jiutian.ems.mapper.EmsUsedDataMapper;
import cec.jiutian.ems.po.EmsUsedDataDaySummaryPO;
import cec.jiutian.ems.po.EmsUsedDataPO;
import cec.jiutian.ems.service.EmsUsedDataDaySummaryService;
import cec.jiutian.ems.service.EmsUsedDataService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.joda.time.LocalDate;
import org.joda.time.LocalDateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Ems Used Data Summary;使用信息汇总表
 *
 * <AUTHOR> CodeGenerator
 * @date : 2023-5-29
 */
@Slf4j
@Service
@Transactional
public class EmsUsedDataDaySummaryBizService {
    private final EmsUsedDataDaySummaryService emsUsedDataSummaryService;
    private final EmsUsedDataService emsUsedDataService;

    private final TokenAnalysisService tokenAnalysisService;

    @Autowired
    private EmsUsedDataDaySummaryMapper emsUsedDataDaySummaryMapper;
    @Autowired
    private EmsUsedDataMapper emsUsedDataMapper;

    public EmsUsedDataDaySummaryBizService(EmsUsedDataDaySummaryService emsUsedDataSummaryService, EmsUsedDataService emsUsedDataService, TokenAnalysisService tokenAnalysisService) {
        this.emsUsedDataSummaryService = emsUsedDataSummaryService;
        this.emsUsedDataService = emsUsedDataService;
        this.tokenAnalysisService = tokenAnalysisService;
    }


    /**
     * 每小时将数据同步到 day 表中去
     * 业务步骤
     * 1. 查询一个小时前的所有测点数据
     * 2. 幂等性校验，判断是否添加过了，day 表中没有数据就做添加，有数据就做修改
     * 从 00：00开始，每隔一小时执行一次
     */
    public void summarizeUsedData() {
        // 系统令牌
        tokenAnalysisService.genToken("60f7363e-ec33-4cf1-a596-54ced9ecfdeb");

        // 当前日期格式：2023-12-19 00:00:00
        LocalDateTime localDateTime = LocalDateTime.now();

        // 校验当前时间，如果是 0 - 1 点的话
        if (localDateTime.getHourOfDay() >= 0 && localDateTime.getHourOfDay() < 1) {

            // 减少一个小时，调整时间为昨天
            localDateTime = localDateTime.minusHours(1);

        }

        // 查询每天或者昨天的所有测点数据
        List<EmsUsedDataPO> todayAllUsedData = emsUsedDataService.getTodayAllUsedData(localDateTime.getYear(), localDateTime.getMonthOfYear(), localDateTime.getDayOfMonth());

        // 没有数据直接返回
        if (CollectionUtils.isEmpty(todayAllUsedData)) {
            return;
        }

        // 对测点数据进行分组
        // key : gid_测点名称
        // value : 测点对象
        Map<String, List<EmsUsedDataPO>> keyToUsedDataList = todayAllUsedData.stream().collect(Collectors.groupingBy(EmsUsedDataPO::getParamName));

        // 拆出测点名称集合
        List<String> keyList = new ArrayList<>(keyToUsedDataList.keySet());

        // 遍历 keyList 的 key，通过 测点名称 和 当日日期，对 day 表做幂等性校验，查询 day 表中是否已经包含了当日的测点数据，对没有的进行添加，有的进行修改
        List<EmsUsedDataDaySummaryPO> list = emsUsedDataDaySummaryMapper.getPointDataByNameAndDate(keyList, localDateTime.getYear(), localDateTime.getMonthOfYear(), localDateTime.getDayOfMonth());

        // 校验集合是否为空
        if (!list.isEmpty()) {


            // 集合不为空，对集合中存在的 param_name 做修改最新数值
            for (EmsUsedDataDaySummaryPO emsUsedDataDaySummaryPO : list) {
                // 根据测点名称获取当日的数据，进行计算
                List<EmsUsedDataPO> pointDataForDayList = keyToUsedDataList.get(emsUsedDataDaySummaryPO.getParamName());

                // 计算最小值
                double minValue = pointDataForDayList.stream().mapToDouble(EmsUsedDataPO::getMinValue).min().orElse(0); // 如果集合为空，返回默认值0

                // 计算最大值
                double maxValue = pointDataForDayList.stream().mapToDouble(EmsUsedDataPO::getMaxValue).max().orElse(0); // 如果集合为空，返回默认值0

                // 计算平均值
//                OptionalDouble averageValue = pointDataForDayList.stream().mapToDouble(EmsUsedDataPO::getAverageValue).average();
                double sum = 0;
                for (EmsUsedDataPO emsUsedDataPO : pointDataForDayList) {
                    sum += emsUsedDataPO.getAverageValue();
                }
                double avg = sum/pointDataForDayList.size();

                // 计算总值
                double totalValue = pointDataForDayList.stream().mapToDouble(EmsUsedDataPO::getTotalValue).sum();
                // 本身存在数据，修改数值即可
                emsUsedDataDaySummaryPO.setMinValue(minValue);
                emsUsedDataDaySummaryPO.setMaxValue(maxValue);
                emsUsedDataDaySummaryPO.setAverageValue(avg);
                emsUsedDataDaySummaryPO.setTotalValue(totalValue);
                // 修改时间
                emsUsedDataDaySummaryPO.setLastTrxnTs(Date.from(localDateTime.toDate().toInstant()));

                // 移除在 map 中的数据
                keyToUsedDataList.remove(emsUsedDataDaySummaryPO.getParamName());

                emsUsedDataDaySummaryMapper.update(emsUsedDataDaySummaryPO);
            }


        }

        // map中没数据直接跳过
        if(keyToUsedDataList.isEmpty()) {
            return;
        }

        // 日期
        Calendar calendar = Calendar.getInstance();

        // 月表集合，后续批量添加使用
        List<EmsUsedDataDaySummaryPO> summarizedDomainList = new ArrayList<>();

        keyToUsedDataList.forEach((k, todayList) -> {
            EmsUsedDataDaySummaryPO summary = new EmsUsedDataDaySummaryPO();
            int totalCount = 0;
            BigDecimal totalValue = BigDecimal.ZERO;
            Double maxValue = null;
            Double minValue = null;
            for (EmsUsedDataPO data : todayList) {
                totalCount += (Objects.isNull(data.getCountNumber()) ? 0 : data.getCountNumber());
                if (null != data.getTotalValue()) {
                    totalValue = totalValue.add(BigDecimal.valueOf(data.getTotalValue()));
                }
                if (Objects.isNull(maxValue)) {
                    maxValue = data.getMaxValue();
                } else {
                    maxValue = data.getMaxValue() > maxValue ? data.getMaxValue() : maxValue;
                }
                if (Objects.isNull(minValue)) {
                    minValue = data.getMinValue();
                } else {
                    minValue = data.getMinValue() < minValue ? data.getMinValue() : minValue;
                }
            }
            summary.setUsedDate(calendar.getTime());
            if (0 == BigDecimal.ZERO.compareTo(totalValue) || 0 == totalCount) {
                summary.setAverageValue(0d);
            } else {
                summary.setAverageValue((totalValue.divide(BigDecimal.valueOf(totalCount), 4, RoundingMode.HALF_UP)).doubleValue());
            }
            summary.setMaxValue(maxValue);
            summary.setMinValue(minValue);
            summary.setTotalValue(totalValue.doubleValue());
            summary.setLocationTreeGid(todayList.get(0).getLocationGid());
            summary.setParamName(todayList.get(0).getParamName());
            summary.setYear(calendar.get(Calendar.YEAR));
            summary.setMonth(calendar.get(Calendar.MONTH) + 1);
            summary.setDay(calendar.get(Calendar.DAY_OF_MONTH));
            // 这里使用DeviceName做ParamType的桥梁
            summary.setParamType(todayList.get(0).getDeviceName());
            summary.setCreateTs(calendar.getTime());
            summary.setCreateUser("AutoTaskExecutor");
            summarizedDomainList.add(summary);
        });
//        emsUsedDataSummaryService.saveBatch(summarizedDomainList);
        for (EmsUsedDataDaySummaryPO emsUsedDataDaySummaryPO : summarizedDomainList) {
            emsUsedDataDaySummaryMapper.dayInsert(emsUsedDataDaySummaryPO);
        }


//        tokenAnalysisService.genToken("60f7363e-ec33-4cf1-a596-54ced9ecfdeb");
//
//        // 获取当天所有的能源使用数据，所有测点的
//        List<EmsUsedDataPO> todayAllUsedData = emsUsedDataService.getTodayAllUsedData();
//
//        // 没有直接返回
//        if (CollectionUtils.isEmpty(todayAllUsedData)) {
//            return;
//        }
//
//        // 当前的日期时间
//        Calendar calendar = Calendar.getInstance();
//
//        // 月表集合，后续批量添加使用
//        List<EmsUsedDataDaySummaryPO> summarizedDomainList = new ArrayList<>();
//
//        // 对测点进行分组
//        // key : gid_测点名称
//        // value : 测点对象
//        Map<String, List<EmsUsedDataPO>> keyToUsedDataList = todayAllUsedData.stream().collect(Collectors.groupingBy(ud -> ud.getLocationGid() + "_" + ud.getParamName()));
//
//        // 幂等性校验，查询是否添加过了，如果添加过了，就执行修改操作
//
//        keyToUsedDataList.forEach((k, todayList) -> {
//            EmsUsedDataDaySummaryPO summary = new EmsUsedDataDaySummaryPO();
//            int totalCount = 0;
//            BigDecimal totalValue = BigDecimal.ZERO;
//            Double maxValue = null;
//            Double minValue = null;
//            for (EmsUsedDataPO data : todayList) {
//                totalCount += (Objects.isNull(data.getCountNumber()) ? 0 : data.getCountNumber());
//                if (null != data.getTotalValue()) {
//                    totalValue = totalValue.add(BigDecimal.valueOf(data.getTotalValue()));
//                }
//                if (Objects.isNull(maxValue)) {
//                    maxValue = data.getMaxValue();
//                } else {
//                    maxValue = data.getMaxValue() > maxValue ? data.getMaxValue() : maxValue;
//                }
//                if (Objects.isNull(minValue)) {
//                    minValue = data.getMinValue();
//                } else {
//                    minValue = data.getMinValue() < minValue ? data.getMinValue() : minValue;
//                }
//            }
//            summary.setUsedDate(calendar.getTime());
//            if (0 == BigDecimal.ZERO.compareTo(totalValue) || 0 == totalCount) {
//                summary.setAverageValue(0d);
//            } else {
//                summary.setAverageValue((totalValue.divide(BigDecimal.valueOf(totalCount), 4, RoundingMode.HALF_UP)).doubleValue());
//            }
//            summary.setMaxValue(maxValue);
//            summary.setMinValue(minValue);
//            summary.setTotalValue(totalValue.doubleValue());
//            summary.setLocationTreeGid(todayList.get(0).getLocationGid());
//            summary.setParamName(todayList.get(0).getParamName());
//            summary.setYear(calendar.get(Calendar.YEAR));
//            summary.setMonth(calendar.get(Calendar.MONTH) + 1);
//            summary.setDay(calendar.get(Calendar.DAY_OF_MONTH));
//            // 这里使用DeviceName做ParamType的桥梁
//            summary.setParamType(todayList.get(0).getDeviceName());
//            summary.setCreateTs(calendar.getTime());
//            summary.setCreateUser("AutoTaskExecutor");
//            summarizedDomainList.add(summary);
//        });
////        emsUsedDataSummaryService.saveBatch(summarizedDomainList);
//        for (EmsUsedDataDaySummaryPO emsUsedDataDaySummaryPO : summarizedDomainList) {
//            mapper.dayInsert(emsUsedDataDaySummaryPO);
//        }
    }





    public void summarizeUsedData2(LocalDateTime localDateTime) {
        // 系统令牌
        tokenAnalysisService.genToken("60f7363e-ec33-4cf1-a596-54ced9ecfdeb");

        // 当前日期格式：2023-12-19 00:00:00
//        LocalDateTime localDateTime = LocalDateTime.now();

        // 校验当前时间，如果是 0 - 1 点的话
        if (localDateTime.getHourOfDay() >= 0 && localDateTime.getHourOfDay() < 1) {

            // 减少一个小时，调整时间为昨天
            localDateTime = localDateTime.minusHours(1);

        }
        Date newDate = localDateTime.toDate();
        int newYear = localDateTime.getYear();
        int newDay = localDateTime.getDayOfMonth();
        int newMonth = localDateTime.getMonthOfYear();

        // 查询每天或者昨天的所有测点数据
        List<EmsUsedDataPO> todayAllUsedData = emsUsedDataService.getTodayAllUsedData(localDateTime.getYear(), localDateTime.getMonthOfYear(), localDateTime.getDayOfMonth());

        // 没有数据直接返回
        if (CollectionUtils.isEmpty(todayAllUsedData)) {
            return;
        }

        // 对测点数据进行分组
        // key : gid_测点名称
        // value : 测点对象
        Map<String, List<EmsUsedDataPO>> keyToUsedDataList = todayAllUsedData.stream().collect(Collectors.groupingBy(EmsUsedDataPO::getParamName));

        // 拆出测点名称集合
        List<String> keyList = new ArrayList<>(keyToUsedDataList.keySet());

        // 遍历 keyList 的 key，通过 测点名称 和 当日日期，对 day 表做幂等性校验，查询 day 表中是否已经包含了当日的测点数据，对没有的进行添加，有的进行修改
        List<EmsUsedDataDaySummaryPO> list = emsUsedDataDaySummaryMapper.getPointDataByNameAndDate(keyList, localDateTime.getYear(), localDateTime.getMonthOfYear(), localDateTime.getDayOfMonth());

        // 校验集合是否为空
        if (!list.isEmpty()) {


            // 集合不为空，对集合中存在的 param_name 做修改最新数值
            for (EmsUsedDataDaySummaryPO emsUsedDataDaySummaryPO : list) {
                // 根据测点名称获取当日的数据，进行计算
                List<EmsUsedDataPO> pointDataForDayList = keyToUsedDataList.get(emsUsedDataDaySummaryPO.getParamName());

                // 计算最小值
                double minValue = pointDataForDayList.stream().mapToDouble(EmsUsedDataPO::getMinValue).min().orElse(0); // 如果集合为空，返回默认值0

                // 计算最大值
                double maxValue = pointDataForDayList.stream().mapToDouble(EmsUsedDataPO::getMaxValue).max().orElse(0); // 如果集合为空，返回默认值0

                // 计算平均值
//                OptionalDouble averageValue = pointDataForDayList.stream().mapToDouble(EmsUsedDataPO::getAverageValue).average();
                double sum = 0;
                for (EmsUsedDataPO emsUsedDataPO : pointDataForDayList) {
                    sum += emsUsedDataPO.getAverageValue();
                }
                double avg = sum/pointDataForDayList.size();

                // 计算总值
                double totalValue = pointDataForDayList.stream().mapToDouble(EmsUsedDataPO::getTotalValue).sum();
                // 本身存在数据，修改数值即可
                emsUsedDataDaySummaryPO.setMinValue(minValue);
                emsUsedDataDaySummaryPO.setMaxValue(maxValue);
                emsUsedDataDaySummaryPO.setAverageValue(avg);
                emsUsedDataDaySummaryPO.setTotalValue(totalValue);
                // 修改时间
                emsUsedDataDaySummaryPO.setLastTrxnTs(Date.from(localDateTime.toDate().toInstant()));

                // 移除在 map 中的数据
                keyToUsedDataList.remove(emsUsedDataDaySummaryPO.getParamName());

                emsUsedDataDaySummaryMapper.update(emsUsedDataDaySummaryPO);
            }


        }

        // map中没数据直接跳过
        if(keyToUsedDataList.isEmpty()) {
            return;
        }

        // 日期
        Calendar calendar = Calendar.getInstance();

        // 月表集合，后续批量添加使用
        List<EmsUsedDataDaySummaryPO> summarizedDomainList = new ArrayList<>();

        keyToUsedDataList.forEach((k, todayList) -> {
            EmsUsedDataDaySummaryPO summary = new EmsUsedDataDaySummaryPO();
            int totalCount = 0;
            BigDecimal totalValue = BigDecimal.ZERO;
            Double maxValue = null;
            Double minValue = null;
            for (EmsUsedDataPO data : todayList) {
                totalCount += (Objects.isNull(data.getCountNumber()) ? 0 : data.getCountNumber());
                if (null != data.getTotalValue()) {
                    totalValue = totalValue.add(BigDecimal.valueOf(data.getTotalValue()));
                }
                if (Objects.isNull(maxValue)) {
                    maxValue = data.getMaxValue();
                } else {
                    maxValue = data.getMaxValue() > maxValue ? data.getMaxValue() : maxValue;
                }
                if (Objects.isNull(minValue)) {
                    minValue = data.getMinValue();
                } else {
                    minValue = data.getMinValue() < minValue ? data.getMinValue() : minValue;
                }
            }
            summary.setUsedDate(calendar.getTime());
            if (0 == BigDecimal.ZERO.compareTo(totalValue) || 0 == totalCount) {
                summary.setAverageValue(0d);
            } else {
                summary.setAverageValue((totalValue.divide(BigDecimal.valueOf(totalCount), 4, RoundingMode.HALF_UP)).doubleValue());
            }
            summary.setMaxValue(maxValue);
            summary.setMinValue(minValue);
            summary.setTotalValue(totalValue.doubleValue());
            summary.setLocationTreeGid(todayList.get(0).getLocationGid());
            summary.setParamName(todayList.get(0).getParamName());
            summary.setYear(newYear);
            summary.setMonth(newMonth);
            summary.setDay(newDay);
            // 这里使用DeviceName做ParamType的桥梁
            summary.setParamType(todayList.get(0).getDeviceName());
            summary.setCreateTs(newDate);
            summary.setCreateUser("AutoTaskExecutor");
            summary.setUsedDate(newDate);
            summarizedDomainList.add(summary);
        });
//        emsUsedDataSummaryService.saveBatch(summarizedDomainList);
        for (EmsUsedDataDaySummaryPO emsUsedDataDaySummaryPO : summarizedDomainList) {
            emsUsedDataDaySummaryMapper.dayInsert(emsUsedDataDaySummaryPO);
        }


//        tokenAnalysisService.genToken("60f7363e-ec33-4cf1-a596-54ced9ecfdeb");
//
//        // 获取当天所有的能源使用数据，所有测点的
//        List<EmsUsedDataPO> todayAllUsedData = emsUsedDataService.getTodayAllUsedData();
//
//        // 没有直接返回
//        if (CollectionUtils.isEmpty(todayAllUsedData)) {
//            return;
//        }
//
//        // 当前的日期时间
//        Calendar calendar = Calendar.getInstance();
//
//        // 月表集合，后续批量添加使用
//        List<EmsUsedDataDaySummaryPO> summarizedDomainList = new ArrayList<>();
//
//        // 对测点进行分组
//        // key : gid_测点名称
//        // value : 测点对象
//        Map<String, List<EmsUsedDataPO>> keyToUsedDataList = todayAllUsedData.stream().collect(Collectors.groupingBy(ud -> ud.getLocationGid() + "_" + ud.getParamName()));
//
//        // 幂等性校验，查询是否添加过了，如果添加过了，就执行修改操作
//
//        keyToUsedDataList.forEach((k, todayList) -> {
//            EmsUsedDataDaySummaryPO summary = new EmsUsedDataDaySummaryPO();
//            int totalCount = 0;
//            BigDecimal totalValue = BigDecimal.ZERO;
//            Double maxValue = null;
//            Double minValue = null;
//            for (EmsUsedDataPO data : todayList) {
//                totalCount += (Objects.isNull(data.getCountNumber()) ? 0 : data.getCountNumber());
//                if (null != data.getTotalValue()) {
//                    totalValue = totalValue.add(BigDecimal.valueOf(data.getTotalValue()));
//                }
//                if (Objects.isNull(maxValue)) {
//                    maxValue = data.getMaxValue();
//                } else {
//                    maxValue = data.getMaxValue() > maxValue ? data.getMaxValue() : maxValue;
//                }
//                if (Objects.isNull(minValue)) {
//                    minValue = data.getMinValue();
//                } else {
//                    minValue = data.getMinValue() < minValue ? data.getMinValue() : minValue;
//                }
//            }
//            summary.setUsedDate(calendar.getTime());
//            if (0 == BigDecimal.ZERO.compareTo(totalValue) || 0 == totalCount) {
//                summary.setAverageValue(0d);
//            } else {
//                summary.setAverageValue((totalValue.divide(BigDecimal.valueOf(totalCount), 4, RoundingMode.HALF_UP)).doubleValue());
//            }
//            summary.setMaxValue(maxValue);
//            summary.setMinValue(minValue);
//            summary.setTotalValue(totalValue.doubleValue());
//            summary.setLocationTreeGid(todayList.get(0).getLocationGid());
//            summary.setParamName(todayList.get(0).getParamName());
//            summary.setYear(calendar.get(Calendar.YEAR));
//            summary.setMonth(calendar.get(Calendar.MONTH) + 1);
//            summary.setDay(calendar.get(Calendar.DAY_OF_MONTH));
//            // 这里使用DeviceName做ParamType的桥梁
//            summary.setParamType(todayList.get(0).getDeviceName());
//            summary.setCreateTs(calendar.getTime());
//            summary.setCreateUser("AutoTaskExecutor");
//            summarizedDomainList.add(summary);
//        });
////        emsUsedDataSummaryService.saveBatch(summarizedDomainList);
//        for (EmsUsedDataDaySummaryPO emsUsedDataDaySummaryPO : summarizedDomainList) {
//            mapper.dayInsert(emsUsedDataDaySummaryPO);
//        }
    }


    public List<EmsUsedDataDaySummaryPO> getByMonthAndTypes(String locationName, Integer year, Integer month, List<String> energyTypeList) {
        return emsUsedDataSummaryService.getByMonthAndTypes(locationName, year, month, energyTypeList);
    }


}