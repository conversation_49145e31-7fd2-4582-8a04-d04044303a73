package cec.jiutian.ems.service.business;

import cec.jiutian.core.service.TokenAnalysisService;
import cec.jiutian.ems.domain.EmsUsedDataMonthSummary;
import cec.jiutian.ems.dto.EnergyConsumptionUsageResultDTO;
import cec.jiutian.ems.po.EmsUsedDataDaySummaryPO;
import cec.jiutian.ems.po.EmsUsedDataMonthSummaryPO;
import cec.jiutian.ems.query.dto.EnergyConsumptionQueryDTO;
import cec.jiutian.ems.service.EmsUsedDataDaySummaryService;
import cec.jiutian.ems.service.EmsUsedDataMonthSummaryService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Ems Used Data Summary;使用信息汇总表
 *
 * <AUTHOR> CodeGenerator
 * @date : 2023-5-29
 */
@Slf4j
@Service
@Transactional
public class EmsUsedDataMonthSummaryBizService {
    private final EmsUsedDataMonthSummaryService emsUsedDataMonthSummaryService;
    private final EmsUsedDataDaySummaryService emsUsedDataDaySummaryService;
    private final TokenAnalysisService tokenAnalysisService;

    public EmsUsedDataMonthSummaryBizService(EmsUsedDataMonthSummaryService emsUsedDataMonthSummaryService,
                                             EmsUsedDataDaySummaryService emsUsedDataDaySummaryService,
                                             TokenAnalysisService tokenAnalysisService) {
        this.emsUsedDataMonthSummaryService = emsUsedDataMonthSummaryService;
        this.emsUsedDataDaySummaryService = emsUsedDataDaySummaryService;
        this.tokenAnalysisService = tokenAnalysisService;
    }

    public void summarizeUsedData() {
        tokenAnalysisService.genToken("60f7363e-ec33-4cf1-a596-54ced9ecfdeb");
        List<EmsUsedDataDaySummaryPO> thisMonthAllData = emsUsedDataDaySummaryService.getThisMonthAllData();
        if (CollectionUtils.isEmpty(thisMonthAllData)) {
            return;
        }
        Calendar calendar = Calendar.getInstance();
        List<EmsUsedDataMonthSummary> summarizedDomainList = new ArrayList<>();
        Map<String, List<EmsUsedDataDaySummaryPO>> keyToUsedDataList = thisMonthAllData.stream().collect(Collectors.groupingBy(s -> s.getLocationTreeGid() + "_" + s.getParamName()));
        keyToUsedDataList.forEach((k, todayList) -> {
            EmsUsedDataMonthSummaryPO summary = new EmsUsedDataMonthSummaryPO();
            int totalCount = 0;
            BigDecimal totalValue = BigDecimal.ZERO;
            BigDecimal avgValue = BigDecimal.ZERO;
            Double maxValue = null;
            Double minValue = null;
            for (EmsUsedDataDaySummaryPO data : todayList) {
                totalCount += 1;
                if (null != data.getTotalValue()) {
                    totalValue = totalValue.add(BigDecimal.valueOf(data.getTotalValue()));
                }
                if (null != data.getAverageValue()) {
                    avgValue = avgValue.add(BigDecimal.valueOf(data.getAverageValue()));
                }
                if (Objects.isNull(maxValue)) {
                    maxValue = data.getMaxValue();
                } else {
                    maxValue = data.getMaxValue() > maxValue ? data.getMaxValue() : maxValue;
                }
                if (Objects.isNull(minValue)) {
                    minValue = data.getMinValue();
                } else {
                    minValue = data.getMinValue() < minValue ? data.getMinValue() : minValue;
                }
            }
            calendar.set(Calendar.DAY_OF_MONTH, 1);
            summary.setUsedDate(calendar.getTime());
            if (0 == BigDecimal.ZERO.compareTo(totalValue) || 0 == totalCount) {
                summary.setAverageValue(0d);
            } else {
                summary.setAverageValue((avgValue.divide(BigDecimal.valueOf(totalCount), 4, RoundingMode.HALF_UP)).doubleValue());
            }
            summary.setMaxValue(maxValue);
            summary.setMinValue(minValue);
            summary.setTotalValue(totalValue.doubleValue());
            summary.setLocationTreeGid(todayList.get(0).getLocationTreeGid());
            summary.setParamName(todayList.get(0).getParamName());
            summary.setParamType(todayList.get(0).getParamType());
            summary.setYear(calendar.get(Calendar.YEAR));
            summary.setMonth(calendar.get(Calendar.MONTH) + 1);
            summary.setCreateTs(calendar.getTime());
            summary.setCreateUser("AutoTaskExecutor");
            summarizedDomainList.add(new EmsUsedDataMonthSummary(summary));
        });
        emsUsedDataMonthSummaryService.saveBatch(summarizedDomainList);
    }


    public void summarizeUsedData3(int m) {

        tokenAnalysisService.genToken("60f7363e-ec33-4cf1-a596-54ced9ecfdeb");
        List<EmsUsedDataDaySummaryPO> thisMonthAllData = emsUsedDataDaySummaryService.getThisMonthAllData();
        if (CollectionUtils.isEmpty(thisMonthAllData)) {
            return;
        }
        Calendar calendar = Calendar.getInstance();
        List<EmsUsedDataMonthSummary> summarizedDomainList = new ArrayList<>();
        Map<String, List<EmsUsedDataDaySummaryPO>> keyToUsedDataList = thisMonthAllData.stream().collect(Collectors.groupingBy(s -> s.getLocationTreeGid() + "_" + s.getParamName()));
        keyToUsedDataList.forEach((k, todayList) -> {
            EmsUsedDataMonthSummaryPO summary = new EmsUsedDataMonthSummaryPO();
            int totalCount = 0;
            BigDecimal totalValue = BigDecimal.ZERO;
            BigDecimal avgValue = BigDecimal.ZERO;
            Double maxValue = null;
            Double minValue = null;
            for (EmsUsedDataDaySummaryPO data : todayList) {
                totalCount += 1;
                if (null != data.getTotalValue()) {
                    totalValue = totalValue.add(BigDecimal.valueOf(data.getTotalValue()));
                }
                if (null != data.getAverageValue()) {
                    avgValue = avgValue.add(BigDecimal.valueOf(data.getAverageValue()));
                }
                if (Objects.isNull(maxValue)) {
                    maxValue = data.getMaxValue();
                } else {
                    maxValue = data.getMaxValue() > maxValue ? data.getMaxValue() : maxValue;
                }
                if (Objects.isNull(minValue)) {
                    minValue = data.getMinValue();
                } else {
                    minValue = data.getMinValue() < minValue ? data.getMinValue() : minValue;
                }
            }
            calendar.set(Calendar.DAY_OF_MONTH, 1);
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
            Date ddd = null;
            try {
                ddd = dateFormat.parse("2023-09-30");
            } catch (ParseException e) {
                throw new RuntimeException(e);
            }
            summary.setUsedDate(ddd);
            if (0 == BigDecimal.ZERO.compareTo(totalValue) || 0 == totalCount) {
                summary.setAverageValue(0d);
            } else {
                summary.setAverageValue((avgValue.divide(BigDecimal.valueOf(totalCount), 4, RoundingMode.HALF_UP)).doubleValue());
            }
            summary.setMaxValue(maxValue);
            summary.setMinValue(minValue);
            summary.setTotalValue(totalValue.doubleValue());
            summary.setLocationTreeGid(todayList.get(0).getLocationTreeGid());
            summary.setParamName(todayList.get(0).getParamName());
            summary.setParamType(todayList.get(0).getParamType());
            summary.setYear(ddd.getYear());
            summary.setMonth(m);
            summary.setCreateTs(ddd);
            summary.setCreateUser("AutoTaskExecutor");

            summarizedDomainList.add(new EmsUsedDataMonthSummary(summary));
        });
        emsUsedDataMonthSummaryService.saveBatch(summarizedDomainList);
    }

    public List<EmsUsedDataMonthSummaryPO> getMonthSummaryByYearAndTypes(String locationName,Integer year, List<String> energyTypeList) {
        return emsUsedDataMonthSummaryService.getByYearAndTypes(locationName, year, energyTypeList);
    }

    public List<EnergyConsumptionUsageResultDTO> getSummarizedByLocationGidListAndYear(EnergyConsumptionQueryDTO dto) {
        return emsUsedDataMonthSummaryService.getSummarizedByLocationGidListAndYear(dto);
    }

    public List<EmsUsedDataMonthSummaryPO> getByYearAndTypeAndLocations(String energyType, Integer year, List<String> locationGidList) {
        return emsUsedDataMonthSummaryService.getByYearAndTypeAndLocations(energyType, year, locationGidList);
    }
}