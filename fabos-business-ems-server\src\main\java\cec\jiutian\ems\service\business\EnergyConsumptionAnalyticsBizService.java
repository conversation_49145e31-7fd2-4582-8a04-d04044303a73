
package cec.jiutian.ems.service.business;

import cec.jiutian.core.exception.MesErrorCodeException;
import cec.jiutian.ems.dto.EnergyConsumptionAsStructureResultDTO;
import cec.jiutian.ems.dto.EnergyConsumptionSimpleResultDTO;
import cec.jiutian.ems.po.EmsUsedDataDaySummaryPO;
import cec.jiutian.ems.query.dto.ComprehensiveEnergyConsumptionQueryDTO;
import cec.jiutian.ems.service.EmsUsedDataDaySummaryService;
import cec.jiutian.ems.service.EmsUsedDataMonthSummaryService;
import cec.jiutian.ems.service.EmsUsedDataService;
import cec.jiutian.ems.service.business.definition.BusinessDefinition;
import cec.jiutian.ems.utils.Assert;
import cec.jiutian.ems.utils.DateUtils;
import com.xxl.job.core.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import static cec.jiutian.ems.service.business.definition.BusinessDefinition.ParamTypes;

/**
 * 能耗分析biz
 */
@Slf4j
@Service
@Transactional
public class EnergyConsumptionAnalyticsBizService {
    private final EmsUsedDataMonthSummaryService emsUsedDataMonthSummaryService;
    private final EmsUsedDataDaySummaryService emsUsedDataDaySummaryService;
    private final EmsUsedDataService emsUsedDataService;

    public EnergyConsumptionAnalyticsBizService(EmsUsedDataMonthSummaryService emsUsedDataMonthSummaryService,
                                                EmsUsedDataDaySummaryService emsUsedDataDaySummaryService,
                                                EmsUsedDataService emsUsedDataService) {
        this.emsUsedDataMonthSummaryService = emsUsedDataMonthSummaryService;
        this.emsUsedDataDaySummaryService = emsUsedDataDaySummaryService;
        this.emsUsedDataService = emsUsedDataService;
    }


    /**
     * 获取综合能耗日历看板，通过type指定按年和月
     */
    public List<EnergyConsumptionSimpleResultDTO> getComprehensiveEnergyConsumptionAsCalendar(ComprehensiveEnergyConsumptionQueryDTO usedDataQueryDTO) {
        Assert.notBlank("查询时间类型不能为空", usedDataQueryDTO.getQueryType());
        Assert.isTrue("查询时间类型必须是01或者02",
                "01".equals(usedDataQueryDTO.getQueryType()) || "02".equals(usedDataQueryDTO.getQueryType()));
        Assert.notNull("年份不能为空", usedDataQueryDTO.getYear());
        Calendar cal = Calendar.getInstance();
        if (Objects.equals(usedDataQueryDTO.getQueryType(), "02")) {
            // 按月查
            Assert.notNull("月份不能为空", usedDataQueryDTO.getMonth());
            cal.set(Calendar.YEAR, usedDataQueryDTO.getYear());
            cal.set(Calendar.MONTH, usedDataQueryDTO.getMonth() - 1);
            cal.set(Calendar.DAY_OF_MONTH, 1);
            int dayOfWeek = cal.get(Calendar.DAY_OF_WEEK);// 1: Sunday，2: Monday
            cal.add(Calendar.DAY_OF_MONTH, -1 * (dayOfWeek + 5) % 7); // get the first Monday of the month (or LAST month
            usedDataQueryDTO.setStartDate(cal.getTime());
            cal.add(Calendar.DAY_OF_MONTH, 6 * 7 - 1); // get the last Sunday of the month (or NEXT month
            usedDataQueryDTO.setEndDate(cal.getTime());

            List<Date> everyDay = DateUtils.getEveryDayInRange(usedDataQueryDTO.getStartDate(), usedDataQueryDTO.getEndDate());
            List<EmsUsedDataDaySummaryPO> summarized = emsUsedDataDaySummaryService.getSummarizedByDateRange(usedDataQueryDTO);
            if (CollectionUtils.isEmpty(summarized)) {
                return everyDay.stream()
                        .map(date -> new EnergyConsumptionSimpleResultDTO(DateUtil.format(date, "yyyyMMdd")))
                        .collect(Collectors.toList());
            } else {
                Map<String, List<EmsUsedDataDaySummaryPO>> dateToSummaryList = summarized.stream().collect(Collectors.groupingBy(summaryPO -> DateUtil.format(summaryPO.getUsedDate(), "yyyyMMdd")));
                return everyDay.stream().map(date -> {
                    String formattedDate = DateUtil.format(date, "yyyyMMdd");
                    List<EmsUsedDataDaySummaryPO> unprocessedPOList = dateToSummaryList.get(formattedDate);
                    if (CollectionUtils.isEmpty(unprocessedPOList)) {
                        return new EnergyConsumptionSimpleResultDTO(formattedDate);
                    } else {
                        EnergyConsumptionSimpleResultDTO sum = new EnergyConsumptionSimpleResultDTO(formattedDate);
                        unprocessedPOList.forEach(summary -> {
                            switch (summary.getParamType()) {
                                case ParamTypes.PARAM_TYPE_WATER:
                                    sum.setUsage(sum.getUsage().add(
                                            // 使用量*折算系数
                                            BigDecimal.valueOf(summary.getTotalValue()).multiply(BusinessDefinition.EnergyConsumptionConstants.STANDARD_COAL_CONVERSION_FACTOR_WATER)));
                                    break;
                                case ParamTypes.PARAM_TYPE_ELECTRICITY:
                                    sum.setUsage(sum.getUsage().add(
                                            BigDecimal.valueOf(summary.getTotalValue()).multiply(BusinessDefinition.EnergyConsumptionConstants.STANDARD_COAL_CONVERSION_FACTOR_ELECTRICITY)));
                                    break;
                                case ParamTypes.PARAM_TYPE_GAS:
                                    sum.setUsage(sum.getUsage().add(
                                            BigDecimal.valueOf(summary.getTotalValue()).multiply(BusinessDefinition.EnergyConsumptionConstants.STANDARD_COAL_CONVERSION_FACTOR_GAS)));
                                    break;
                            }
                        });
                        return sum.setUsage(sum.getUsage().setScale(2, RoundingMode.HALF_UP));
                    }
                }).collect(Collectors.toList());
            }
        } else {
            // 01 按年
            Map<Integer, EnergyConsumptionSimpleResultDTO> resultByYear = new HashMap<>();
            for (int i = 1; i <= 12; i++) {
                resultByYear.put(i, (new EnergyConsumptionSimpleResultDTO(usedDataQueryDTO.getYear() + Integer.toString(i))));
            }
            List<EnergyConsumptionSimpleResultDTO> summarized = emsUsedDataMonthSummaryService.getSummarizedByDateAndType(
                    null, usedDataQueryDTO.getYear(), null);
            if (CollectionUtils.isNotEmpty(summarized)) {
                Map<String, List<EnergyConsumptionSimpleResultDTO>> timeToSimpleList = summarized.stream().collect(Collectors.groupingBy(EnergyConsumptionSimpleResultDTO::getTime));
                resultByYear.forEach((time, result) -> {
                    List<EnergyConsumptionSimpleResultDTO> simpleResultDTOS = timeToSimpleList.get(time.toString());
                    if (CollectionUtils.isNotEmpty(simpleResultDTOS)) {
                        simpleResultDTOS.forEach(summary -> {
                            switch (summary.getEnergyType()) {
                                case ParamTypes.PARAM_TYPE_WATER:
                                    result.setUsage(result.getUsage().add(
                                            // 使用量*折算系数
                                            summary.getUsage().multiply(BusinessDefinition.EnergyConsumptionConstants.STANDARD_COAL_CONVERSION_FACTOR_WATER)));
                                    break;
                                case ParamTypes.PARAM_TYPE_ELECTRICITY:
                                    result.setUsage(result.getUsage().add(
                                            summary.getUsage().multiply(BusinessDefinition.EnergyConsumptionConstants.STANDARD_COAL_CONVERSION_FACTOR_ELECTRICITY)));
                                    break;
                                case ParamTypes.PARAM_TYPE_GAS:
                                    result.setUsage(result.getUsage().add(
                                            summary.getUsage().multiply(BusinessDefinition.EnergyConsumptionConstants.STANDARD_COAL_CONVERSION_FACTOR_GAS)));
                                    break;
                            }
                            result.setUsage(result.getUsage().setScale(2, RoundingMode.HALF_UP));
                        });
                    }
                });
            }
            return new ArrayList<>(resultByYear.values());
        }
    }

    /**
     * 获取综合能耗看板，折线图
     */
    public List<EnergyConsumptionSimpleResultDTO> getComprehensiveEnergyConsumptionAsLineChart(ComprehensiveEnergyConsumptionQueryDTO usedDataQueryDTO) {
        Assert.notBlank("查询时间类型不能为空", usedDataQueryDTO.getQueryType());
        String energyType = usedDataQueryDTO.getEnergyType();
        Assert.notBlank("查询能源类型不能为空", energyType);
        boolean isQueryingStandardCoal = ParamTypes.PARAM_TYPE_STANDARD_COAL.equals(energyType);
        Calendar cal = Calendar.getInstance();
        switch (usedDataQueryDTO.getQueryType()) {
            case "03": // 按日
                Map<String, EnergyConsumptionSimpleResultDTO> result = new HashMap<>();
                usedDataQueryDTO.setDay(cal.get(Calendar.DAY_OF_MONTH));
                usedDataQueryDTO.setMonth(cal.get(Calendar.MONTH) + 1);
                usedDataQueryDTO.setYear(cal.get(Calendar.YEAR));
                for (int i = 0; i <= cal.get(Calendar.HOUR_OF_DAY); i++) {
                    result.put(Integer.toString(i), (new EnergyConsumptionSimpleResultDTO(Integer.toString(i))));
                }
                if (isQueryingStandardCoal) {
                    usedDataQueryDTO.setEnergyType(null);
                }
                List<EnergyConsumptionSimpleResultDTO> usedDataByHours = emsUsedDataService.getGroupedUsedDataByHours(usedDataQueryDTO);
                if (CollectionUtils.isNotEmpty(usedDataByHours)) {
                    if (isQueryingStandardCoal) {
                        performCalculation(usedDataByHours, result);
                    } else {
                        usedDataByHours.forEach(used -> {
                            result.get(used.getTime()).setUsage(used.getUsage().setScale(2, RoundingMode.HALF_UP));
                        });
                    }
                }
                return result.values().stream()
                        .sorted(Comparator.comparing(energyConsumptionSimpleResultDTO -> Integer.parseInt(energyConsumptionSimpleResultDTO.getTime())))
                        .peek(r -> {
                            String time = r.getTime();
                            r.setTime((time.length() == 1 ? "0" + time : time) + ":00");
                        })
                        .collect(Collectors.toList());
            case "01": // 按年
                Map<String, EnergyConsumptionSimpleResultDTO> resultByYear = new HashMap<>();
                for (int i = 1; i <= cal.get(Calendar.MONTH) + 1; i++) {
                    resultByYear.put(Integer.toString(i), (new EnergyConsumptionSimpleResultDTO(Integer.toString(i))));
                }
                List<EnergyConsumptionSimpleResultDTO> usedDataByMonth = emsUsedDataMonthSummaryService.getSummarizedByDateAndType(isQueryingStandardCoal ? null : energyType, cal.get(Calendar.YEAR), null);
                if (CollectionUtils.isNotEmpty(usedDataByMonth)) {
                    if (isQueryingStandardCoal) {
                        performCalculation(usedDataByMonth, resultByYear);
                    } else {
                        usedDataByMonth.forEach(sum -> {
                            resultByYear.get(sum.getTime()).setUsage(sum.getUsage().setScale(2, RoundingMode.HALF_UP));
                        });
                    }
                }
                return resultByYear.values().stream()
                        .sorted(Comparator.comparing(energyConsumptionSimpleResultDTO -> Integer.parseInt(energyConsumptionSimpleResultDTO.getTime()))).collect(Collectors.toList());
            case "02": // 按月
                cal.set(Calendar.DAY_OF_MONTH, 1);
                Map<String, EnergyConsumptionSimpleResultDTO> resultDTOMap = DateUtils.getEveryDayInRange(cal.getTime(), new Date()).stream()
                        .map(date -> new EnergyConsumptionSimpleResultDTO(DateUtil.format(date, "d")))
                        .collect(Collectors.toMap(EnergyConsumptionSimpleResultDTO::getTime, Function.identity(), (o1, o2) -> o2));
                List<EmsUsedDataDaySummaryPO> summarized = emsUsedDataDaySummaryService.getSummarizedByDateAndType(isQueryingStandardCoal ? null : energyType, cal.get(Calendar.YEAR), cal.get(Calendar.MONTH) + 1);
                if (CollectionUtils.isNotEmpty(summarized)) {
                    List<EnergyConsumptionSimpleResultDTO> simpleResultDTOS = summarized.stream().map(e -> {
                        EnergyConsumptionSimpleResultDTO resultDTO = new EnergyConsumptionSimpleResultDTO();
                        resultDTO.setUsage(BigDecimal.valueOf(e.getTotalValue()));
                        resultDTO.setEnergyType(e.getParamType());
                        resultDTO.setTime(DateUtil.format(e.getUsedDate(), "d"));
                        return resultDTO;
                    }).collect(Collectors.toList());
                    if (isQueryingStandardCoal) {
                        performCalculation(simpleResultDTOS, resultDTOMap);
                    } else {
                        summarized.forEach(used -> {
                            resultDTOMap.get(DateUtil.format(used.getUsedDate(), "d")).setUsage(BigDecimal.valueOf(used.getTotalValue()).setScale(2, RoundingMode.HALF_UP));
                        });
                    }

                }
                return resultDTOMap.values().stream()
                        .sorted(Comparator.comparing(energyConsumptionSimpleResultDTO -> Integer.parseInt(energyConsumptionSimpleResultDTO.getTime()))).collect(Collectors.toList());
            default:
                throw new MesErrorCodeException("查询时间类型必须是01、02或者03");
        }
    }

    private List<EnergyConsumptionSimpleResultDTO> performCalculation(List<EnergyConsumptionSimpleResultDTO> dataList, Map<String, EnergyConsumptionSimpleResultDTO> result) {
        // 按时间进行分组之后，统计折标煤
        dataList.stream().collect(Collectors.groupingBy(EnergyConsumptionSimpleResultDTO::getTime))
                .forEach((time, resultList) -> { // 一个时间维度一个result，计算折标煤
                    // 查询折标煤，需要分组乘以系数之后相加
                    BigDecimal electricityPartialUsage = resultList.stream().filter(r -> ParamTypes.PARAM_TYPE_ELECTRICITY.equals(r.getEnergyType()))
                            .map(EnergyConsumptionSimpleResultDTO::getUsage).reduce(BigDecimal.ZERO, BigDecimal::add)
                            .multiply(BusinessDefinition.EnergyConsumptionConstants.STANDARD_COAL_CONVERSION_FACTOR_ELECTRICITY);
                    BigDecimal waterPartialUsage = resultList.stream().filter(r -> ParamTypes.PARAM_TYPE_WATER.equals(r.getEnergyType()))
                            .map(EnergyConsumptionSimpleResultDTO::getUsage).reduce(BigDecimal.ZERO, BigDecimal::add)
                            .multiply(BusinessDefinition.EnergyConsumptionConstants.STANDARD_COAL_CONVERSION_FACTOR_WATER);
                    BigDecimal gasPartialUsage = resultList.stream().filter(r -> ParamTypes.PARAM_TYPE_GAS.equals(r.getEnergyType()))
                            .map(EnergyConsumptionSimpleResultDTO::getUsage).reduce(BigDecimal.ZERO, BigDecimal::add)
                            .multiply(BusinessDefinition.EnergyConsumptionConstants.STANDARD_COAL_CONVERSION_FACTOR_GAS);
                    BigDecimal reallyTotalUsage = electricityPartialUsage.add(waterPartialUsage).add(gasPartialUsage).setScale(2, RoundingMode.HALF_UP);
                    if (Objects.nonNull(result.get(time))) {
                        result.get(time).setUsage(reallyTotalUsage);
                    }
                });
        return dataList;
    }

    public EnergyConsumptionAsStructureResultDTO getComprehensiveEnergyConsumptionAsStructure(ComprehensiveEnergyConsumptionQueryDTO usedDataQueryDTO) {
        Assert.notBlank("查询时间类型不能为空", usedDataQueryDTO.getQueryType());

        EnergyConsumptionAsStructureResultDTO result = new EnergyConsumptionAsStructureResultDTO();
        List<EnergyConsumptionSimpleResultDTO> summarizedThisPeriod;
        List<EnergyConsumptionSimpleResultDTO> summarizedLastPeriod;
        Calendar cal = Calendar.getInstance();
        switch (usedDataQueryDTO.getQueryType()) {
            case "03": // 按日
                usedDataQueryDTO.setDay(cal.get(Calendar.DAY_OF_MONTH));
                usedDataQueryDTO.setMonth(cal.get(Calendar.MONTH) + 1);
                usedDataQueryDTO.setYear(cal.get(Calendar.YEAR));
                usedDataQueryDTO.setHour(cal.get(Calendar.HOUR_OF_DAY));
                summarizedThisPeriod = emsUsedDataService.getGroupedUsedDataByHoursAndType(usedDataQueryDTO);
                usedDataQueryDTO.setDay(usedDataQueryDTO.getDay() - 1);
                summarizedLastPeriod = emsUsedDataDaySummaryService.getSummarizedByDate(usedDataQueryDTO.getYear(), usedDataQueryDTO.getMonth(), usedDataQueryDTO.getDay());
                break;
            case "02": // 按月
                summarizedThisPeriod = emsUsedDataDaySummaryService.getSummarizedByDate(cal.get(Calendar.YEAR), cal.get(Calendar.MONTH) + 1);
                summarizedLastPeriod = emsUsedDataMonthSummaryService.getSummarizedByDateAndType(null, cal.get(Calendar.YEAR), cal.get(Calendar.MONTH));
                break;
            case "01": // 按年
                summarizedThisPeriod = emsUsedDataMonthSummaryService.getSummarizedByDate(cal.get(Calendar.YEAR));
                summarizedLastPeriod = emsUsedDataMonthSummaryService.getSummarizedByDate(cal.get(Calendar.YEAR) - 1);
                break;
            default:
                throw new MesErrorCodeException("查询时间类型必须是01、02或者03");
        }
        if (CollectionUtils.isNotEmpty(summarizedThisPeriod)) {
            summarizedThisPeriod.forEach(summarized -> {
                switch (summarized.getEnergyType()) {
                    case ParamTypes.PARAM_TYPE_WATER:
                        result.setWaterConsumption(result.getWaterConsumption().add(summarized.getUsage()));
                        break;
                    case ParamTypes.PARAM_TYPE_ELECTRICITY:
                        result.setElectricityConsumption(result.getElectricityConsumption().add(summarized.getUsage()));
                        break;
                    case ParamTypes.PARAM_TYPE_GAS:
                        result.setGasConsumption(result.getGasConsumption().add(summarized.getUsage()));
                        break;
                }
            });
            result.calculateThisPeriodCoal();
        }
        if (CollectionUtils.isNotEmpty(summarizedLastPeriod)) {
            summarizedLastPeriod.forEach(summarized -> {
                switch (summarized.getEnergyType()) {
                    case ParamTypes.PARAM_TYPE_WATER:
                        result.setWaterYearOnYear(result.getWaterConsumption().add(summarized.getUsage()));
                        break;
                    case ParamTypes.PARAM_TYPE_ELECTRICITY:
                        result.setElectricityYearOnYear(result.getElectricityConsumption().add(summarized.getUsage()));
                        break;
                    case ParamTypes.PARAM_TYPE_GAS:
                        result.setGasYearOnYear(result.getGasConsumption().add(summarized.getUsage()));
                        break;
                }
            });
            result.calculateLastPeriodCoal();
        }
        result.calculateRateAndSetScale();
        return result;
    }
}