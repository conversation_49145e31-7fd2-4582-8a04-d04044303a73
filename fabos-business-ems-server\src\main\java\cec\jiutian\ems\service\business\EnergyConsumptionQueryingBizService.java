
package cec.jiutian.ems.service.business;

import cec.jiutian.core.comn.util.BeanUtils;
import cec.jiutian.core.exception.MesErrorCodeException;
import cec.jiutian.ems.constant.EmsConstant;
import cec.jiutian.ems.domain.EmsLocationTree;
import cec.jiutian.ems.dto.CapacityDataGetDTO;
import cec.jiutian.ems.dto.EmsLocationTreeResultDTO;
import cec.jiutian.ems.dto.EnergyConsumptionSimpleResultDTO;
import cec.jiutian.ems.dto.EnergyConsumptionUsageResultDTO;
import cec.jiutian.ems.mapper.EmsLocationTreeMapper;
import cec.jiutian.ems.query.dto.EnergyConsumptionByDateRangeQueryDTO;
import cec.jiutian.ems.query.dto.EnergyConsumptionQueryDTO;
import cec.jiutian.ems.query.dto.EnergyConsumptionSelectDTO;
import cec.jiutian.ems.service.*;
import cec.jiutian.ems.service.business.definition.BusinessDefinition;
import cec.jiutian.ems.utils.Assert;
import cec.jiutian.ems.utils.DateUtils;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.BeanWrapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import static cec.jiutian.ems.service.business.definition.BusinessDefinition.ParamTypes;

/**
 * 用能查询biz
 */
@Slf4j
@Service
@Transactional
@AllArgsConstructor
public class EnergyConsumptionQueryingBizService {
    private final EmsUsedDataMonthSummaryService emsUsedDataMonthSummaryService;
    private final EmsUsedDataDaySummaryService emsUsedDataDaySummaryService;
    private final EmsUsedDataService emsUsedDataService;
    private final EmsLocationTreeService emsLocationTreeService;

    private final EmsCapacityDataService emsCapacityDataService;

    public List<EnergyConsumptionUsageResultDTO> getEnergyConsumptionUsageByLocGidAndType(EnergyConsumptionQueryDTO queryDto) {
        Assert.notBlank("日期类型不能为空", queryDto.getTimeType());
        String energyType = queryDto.getEnergyType();
        Assert.notBlank("能源类型不能为空", energyType);
        Assert.notEmpty("请选择结构", queryDto.getLocationGidList());
        Calendar cal = Calendar.getInstance();
        List<EnergyConsumptionUsageResultDTO> assembledResult;
        switch (queryDto.getTimeType()) {
            case EmsConstant.timeType.YEAR: // 年
                Assert.notNull("年份不能为空", queryDto.getYear());
                List<EnergyConsumptionUsageResultDTO> summarizedMonthData = emsUsedDataMonthSummaryService.getSummarizedByLocationGidListAndYear(queryDto);
                List<EnergyConsumptionUsageResultDTO> locationsAndMonths = new ArrayList<>();
                new HashSet<>(queryDto.getLocationGidList()).forEach(gid -> {
                    EmsLocationTree tree = emsLocationTreeService.checkExistById(gid);
                    EnergyConsumptionUsageResultDTO dto = new EnergyConsumptionUsageResultDTO(tree.getEntity()).setEnergyType(EmsConstant.energyType.get(energyType));
                    for (int i = 1; i <= 12; i++) {
                        dto.getValueList().add(new EnergyConsumptionSimpleResultDTO(Integer.toString(i)));
                    }
                    summarizedMonthData.forEach(item -> {
                        dto.setUnit(item.getUnit());
                        dto.setParentGid(item.getParentGid());
                        dto.setLocationLevel(item.getLocationLevel());
                    });
                    locationsAndMonths.add(dto);
                });
                if (CollectionUtils.isNotEmpty(summarizedMonthData)) {
                    Map<String, List<EnergyConsumptionUsageResultDTO>> locationGroupedMonthData = summarizedMonthData.stream().collect(Collectors.groupingBy(EnergyConsumptionUsageResultDTO::getLocationGid));
                    performCalculation(energyType, locationsAndMonths, locationGroupedMonthData);
                }
                assembledResult = locationsAndMonths;
                break;
            case EmsConstant.timeType.MONTH: // 月
                Assert.notNull("年份不能为空", queryDto.getYear());
                Assert.notNull("月份不能为空", queryDto.getMonth());
                List<EnergyConsumptionUsageResultDTO> summarizedDayData = emsUsedDataDaySummaryService.getSummarizedByLocationGidListAndYearMonth(queryDto);
                EnergyCostBoardBizService.processStartTimeAndEndTime(queryDto);
                List<EnergyConsumptionUsageResultDTO> locationsAndDays = new ArrayList<>();
                SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
                SimpleDateFormat sdfDay = new SimpleDateFormat("d");
                new HashSet<>(queryDto.getLocationGidList()).forEach(gid -> {
                    EmsLocationTree tree = emsLocationTreeService.checkExistById(gid);
                    EnergyConsumptionUsageResultDTO dto = new EnergyConsumptionUsageResultDTO(tree.getEntity()).setEnergyType(EmsConstant.energyType.get(energyType));
                    dto.getValueList().addAll(DateUtils
                            .getEveryDayInRange(queryDto.getStartTime(), queryDto.getEndTime())
                            .stream().map(date -> new EnergyConsumptionSimpleResultDTO(sdf.format(date), sdfDay.format(date)))
                            .collect(Collectors.toList()));
                    summarizedDayData.forEach(item -> {
                        dto.setUnit(item.getUnit());
                        dto.setParentGid(item.getParentGid());
                        dto.setLocationLevel(item.getLocationLevel());
                    });
                    locationsAndDays.add(dto);
                });
                if (CollectionUtils.isNotEmpty(summarizedDayData)) {
                    Map<String, List<EnergyConsumptionUsageResultDTO>> locationGroupedDayData = summarizedDayData.stream().collect(Collectors.groupingBy(EnergyConsumptionUsageResultDTO::getLocationGid));
                    performCalculation(energyType, locationsAndDays, locationGroupedDayData);
                }
                assembledResult = locationsAndDays;
                break;
            case EmsConstant.timeType.DAY: // 日
                Assert.notNull("年份不能为空", queryDto.getYear());
                Assert.notNull("月份不能为空", queryDto.getMonth());
                Assert.notNull("日期不能为空", queryDto.getDay());
                List<EnergyConsumptionUsageResultDTO> groupedHourData = emsUsedDataService.getGroupedByLocationGidListAndYearMonthDay(queryDto);
                List<EnergyConsumptionUsageResultDTO> locationsAndHours = new ArrayList<>();
                new HashSet<>(queryDto.getLocationGidList()).forEach(gid -> {
                    EmsLocationTree tree = emsLocationTreeService.checkExistById(gid);
                    EnergyConsumptionUsageResultDTO dto = new EnergyConsumptionUsageResultDTO(tree.getEntity()).setEnergyType(EmsConstant.energyType.get(energyType));
                    for (int i = 0; i <= 23; i++) {
                        dto.getValueList().add((new EnergyConsumptionSimpleResultDTO((i <= 9 ? "0" : "") + i + ":00", String.valueOf(i))));
                    }
                    groupedHourData.forEach(item -> {
                        dto.setUnit(item.getUnit());
                        dto.setParentGid(item.getParentGid());
                        dto.setLocationLevel(item.getLocationLevel());
                    });
                    locationsAndHours.add(dto);
                });
                if (CollectionUtils.isNotEmpty(groupedHourData)) {
                    Map<String, List<EnergyConsumptionUsageResultDTO>> locationGroupedHourData = groupedHourData.stream().collect(Collectors.groupingBy(EnergyConsumptionUsageResultDTO::getLocationGid));
                    performCalculation(energyType, locationsAndHours, locationGroupedHourData);
                }
                assembledResult = locationsAndHours;
                break;
            default:
                throw new MesErrorCodeException("查询时间类型必须是year、month或者day");
        }
        return assembledResult;
    }

    public Object getEnergyConsumptionSelect(EnergyConsumptionQueryDTO queryDto) {
        CapacityDataGetDTO capacityDataGetDTO = new CapacityDataGetDTO();
        BeanUtils.copyNotEmptyProperties(queryDto, capacityDataGetDTO);

        List<HashMap> tables = emsCapacityDataService.getTableInfos(capacityDataGetDTO);
        // 能耗数据
        List<EnergyConsumptionUsageResultDTO> resultDTOS = this.getEnergyConsumptionUsageByLocGidAndType(queryDto);

        List<HashMap<String, Object>> maps = this.convertAssembledResult(resultDTOS, queryDto.getTimeType());
        EnergyConsumptionSelectDTO energyConsumptionSelectDTO = new EnergyConsumptionSelectDTO().setTables(tables).setConsumptionData(maps);

        //有序处理
        List<HashMap<String, Object>> consumptionData = energyConsumptionSelectDTO.getConsumptionData();
        // 有序的 gid
        List<String> locationGidList = queryDto.getLocationGidList();
        List<HashMap<String, Object>> sortData = new LinkedList<>();
        for (String gid : locationGidList) {
            for (int i = 0; i < consumptionData.size(); i++) {
                if(consumptionData.get(i).get("id").equals(gid)) {
                    sortData.add(consumptionData.get(i));
                }
            }
        }
        energyConsumptionSelectDTO.setConsumptionData(sortData);


//        List<EmsLocationTreeResultDTO> locationList = queryDto.getLocationList();
//        List<HashMap<String, Object>> newConsumptionData = energyConsumptionSelectDTO.getConsumptionData();
//        if(!locationList.isEmpty()) {
//            EmsLocationTreeResultDTO emsLocationTreeResultDTO = locationList.get(0);
//            newConsumptionData.forEach(item -> {
//                String id = item.get("id").toString();
//                BigDecimal totalValue = new BigDecimal(String.valueOf(item.get("totalValue")));;
//                updateSumByGid(emsLocationTreeResultDTO,id,totalValue);
//            });
//
//            // 累加
//            accumulateSum(emsLocationTreeResultDTO);
//
//            // 将emsLocationTreeResultDTO累加的值给回
////            HashMap<String,>
//
//            newConsumptionData.forEach(item -> {
//                int i = 1;
//                List<HashMap<String, Object>> hashMaps = selectGidAndValue(emsLocationTreeResultDTO, newConsumptionData,i);
//                energyConsumptionSelectDTO.setConsumptionData(hashMaps);
//            });
//        }
        return energyConsumptionSelectDTO;
    }

    /**
     * 向上汇总
     */
    private static void accumulateSum(EmsLocationTreeResultDTO node) {
        // 递归终止条件：当节点没有子节点时，直接返回
        if (node.getChildren().isEmpty()) {
            return;
        }
        // 遍历子节点，累加每个子节点的sum值
        for (EmsLocationTreeResultDTO child : node.getChildren()) {
            accumulateSum(child);

            BigDecimal nodeSum = (node.getSum() != null) ? node.getSum() : BigDecimal.ZERO;
            BigDecimal childSum = (child.getSum() != null) ? child.getSum() : BigDecimal.ZERO;
            node.setSum(nodeSum.add(childSum));

        }
    }


    /**
     * 遍历树
     */
    private static List<HashMap<String, Object>> selectGidAndValue(EmsLocationTreeResultDTO node,List<HashMap<String, Object>> result,int i) {
        int k = i;
        // 打印当前节点的 gid
        System.out.println("Gid: " + node.getGid());
        result.forEach(item -> {
            if(item.get("id").equals(node.getGid())){
                item.put("month" + 1,node.getSum());
            }
        });
        // 递归终止条件：当节点没有子节点时，直接返回
        if (node.getChildren().isEmpty()) {
            return result;
        }

        // 遍历子节点，累加每个子节点的 sum 值并打印 gid
        for (EmsLocationTreeResultDTO child : node.getChildren()) {

            selectGidAndValue(child,result,k++);
        }
        return result;
    }


    /**
     * 查询修改
     * @param node
     * @param gid
     * @param sum
     */
    private static void updateSumByGid(EmsLocationTreeResultDTO node, String gid, BigDecimal sum) {
        // 递归终止条件：当节点没有子节点时，直接返回
        if (node.getChildren().isEmpty()) {
            return;
        }

        // 遍历子节点，累加每个子节点的sum值
        for (EmsLocationTreeResultDTO child : node.getChildren()) {
            // 递归调用时传递 gid 和 sum
            updateSumByGid(child, gid, sum);

            // 判断当前节点的 gid 是否等于传递的 gid，如果是，则更新 sum 值
            if (child.getGid().equals(gid)) {
                child.setSum(sum);
            }
        }
    }


    public List<HashMap<String, Object>> convertAssembledResult(List<EnergyConsumptionUsageResultDTO> assembledResult, String timeType) {

        // 将EmsCapacityDataPO装换想要的HashMap
        List<HashMap<String, Object>> collect = assembledResult.stream().map(x -> {
            HashMap<String, Object> temp = new HashMap<>();
            temp.put("area", x.getLocationName());
            temp.put("energyType", x.getEnergyType());
            temp.put("unit", x.getUnit());
            temp.put("id", x.getLocationGid());
            temp.put("pid", x.getParentGid());
            temp.put("locationLevel", x.getLocationLevel());
            List<EnergyConsumptionSimpleResultDTO> valueList = x.getValueList();
            if (CollectionUtils.isNotEmpty(valueList)) {
                BigDecimal totalValue = BigDecimal.ZERO;
                for (EnergyConsumptionSimpleResultDTO val : valueList) {
                    BigDecimal value = val.getUsage();
                    totalValue = totalValue.add(value);
                    switch (timeType) {
                        case EmsConstant.timeType.YEAR:
                            temp.put("month" + val.getRawTime(), val.getUsage());
                            break;
                        case EmsConstant.timeType.MONTH:
                            temp.put("day" + val.getRawTime(), val.getUsage());
                            break;
                        case EmsConstant.timeType.DAY:
                            temp.put("hour" + val.getRawTime(), val.getUsage());
                            break;
                        default:
                            break;
                    }
                }
                temp.put("totalValue", totalValue);
            }
            return temp;
        }).collect(Collectors.toList());

        return collect;
    }


    private void performCalculation(String energyType, List<EnergyConsumptionUsageResultDTO> locationsAndMonths, Map<String, List<EnergyConsumptionUsageResultDTO>> locationGrouped) {
        locationGrouped.forEach((locId, grouped) -> {
            grouped.stream() // 按时间进行分组之后，统计折标煤
                    .collect(Collectors.groupingBy(EnergyConsumptionUsageResultDTO::getTime)) // Map<String, List<EnergyConsumptionUsageResultDTO>>
                    .forEach((time, resultList) -> { // 一个时间维度一个result，计算折标煤
                        EnergyConsumptionSimpleResultDTO simpleResult = new EnergyConsumptionSimpleResultDTO(time);
                        BigDecimal reallyTotalUsage = BigDecimal.ZERO;
                        // 如果查询折标煤，需要分组乘以系数之后相加
                        if (ParamTypes.PARAM_TYPE_STANDARD_COAL.equals(energyType)) {
                            BigDecimal electricityPartialUsage = resultList.stream().filter(r -> ParamTypes.PARAM_TYPE_ELECTRICITY.equals(r.getEnergyType()))
                                    .map(EnergyConsumptionUsageResultDTO::getUsage).reduce(BigDecimal.ZERO, BigDecimal::add)
                                    .multiply(BusinessDefinition.EnergyConsumptionConstants.STANDARD_COAL_CONVERSION_FACTOR_ELECTRICITY);
                            BigDecimal waterPartialUsage = resultList.stream().filter(r -> ParamTypes.PARAM_TYPE_WATER.equals(r.getEnergyType()))
                                    .map(EnergyConsumptionUsageResultDTO::getUsage).reduce(BigDecimal.ZERO, BigDecimal::add)
                                    .multiply(BusinessDefinition.EnergyConsumptionConstants.STANDARD_COAL_CONVERSION_FACTOR_WATER);
                            BigDecimal gasPartialUsage = resultList.stream().filter(r -> ParamTypes.PARAM_TYPE_GAS.equals(r.getEnergyType()))
                                    .map(EnergyConsumptionUsageResultDTO::getUsage).reduce(BigDecimal.ZERO, BigDecimal::add)
                                    .multiply(BusinessDefinition.EnergyConsumptionConstants.STANDARD_COAL_CONVERSION_FACTOR_GAS);
                            reallyTotalUsage = electricityPartialUsage.add(waterPartialUsage).add(gasPartialUsage);
                        } else {
                            reallyTotalUsage = resultList.stream().filter(r -> energyType.equals(r.getEnergyType()))
                                    .map(EnergyConsumptionUsageResultDTO::getUsage).reduce(BigDecimal.ZERO, BigDecimal::add);
                        }
                        EnergyConsumptionUsageResultDTO usageResultDTO = locationsAndMonths.stream()
                                .filter(r -> Objects.equals(r.getLocationGid(), locId)).findAny().orElseThrow(() -> new MesErrorCodeException(""));
                        usageResultDTO.setLocationName(resultList.get(0).getLocationName());
                        usageResultDTO.getValueList().stream()
                                .filter(v -> Objects.equals(v.getRawTime(), time)).findAny().orElseThrow(() -> new MesErrorCodeException(""))
                                .setUsage(reallyTotalUsage);
                    });
        });
    }


    public EnergyConsumptionSimpleResultDTO getEnergyConsumptionUsageByLocGidAndTypeAndDateRange(EnergyConsumptionByDateRangeQueryDTO queryDto) {
        String energyType = queryDto.getEnergyType();
        Assert.notBlank("能源类型不能为空", energyType);
        Assert.notBlank("请选择结构", queryDto.getLocationGid());
        if (queryDto.getStartTime() != null && queryDto.getEndTime() != null) {
            List<EnergyConsumptionSimpleResultDTO> resultDTOS = emsUsedDataDaySummaryService.getByLocGidAndTypeAndDateRange(queryDto);
            if (CollectionUtils.isNotEmpty(resultDTOS)) {
                return resultDTOS.get(0).setTime(DateUtils.parseDateToString(queryDto.getStartTime(), "yyyy-MM-dd") + "至" + DateUtils.parseDateToString(queryDto.getEndTime(), "yyyy-MM-dd"));
            } else {
                return new EnergyConsumptionSimpleResultDTO();
            }
        }
        if (queryDto.getYear() != null && queryDto.getMonth() != null && queryDto.getDay() != null) {
            List<EnergyConsumptionSimpleResultDTO> resultDTOS = emsUsedDataService.getByLocGidAndTypeAndDateRange(queryDto);
            if (CollectionUtils.isNotEmpty(resultDTOS)) {
                return resultDTOS.get(0);
            } else {
                return new EnergyConsumptionSimpleResultDTO();
            }
        }
        throw new MesErrorCodeException("年月日或开始结束时间组合不能均为空");
    }
}