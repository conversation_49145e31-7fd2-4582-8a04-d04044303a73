
package cec.jiutian.ems.service.business;

import cec.jiutian.core.exception.MesErrorCodeException;
import cec.jiutian.ems.dto.EnergyConsumptionRankingResultDTO;
import cec.jiutian.ems.query.dto.EnergyConsumptionQueryDTO;
import cec.jiutian.ems.service.EmsUsedDataDaySummaryService;
import cec.jiutian.ems.service.EmsUsedDataMonthSummaryService;
import cec.jiutian.ems.service.EmsUsedDataService;
import cec.jiutian.ems.utils.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 能耗排行biz
 */
@Slf4j
@Service
@Transactional
public class EnergyConsumptionRankingBizService {
    private final EmsUsedDataMonthSummaryService emsUsedDataMonthSummaryService;
    private final EmsUsedDataDaySummaryService emsUsedDataDaySummaryService;
    private final EmsUsedDataService emsUsedDataService;

    public EnergyConsumptionRankingBizService(EmsUsedDataMonthSummaryService emsUsedDataMonthSummaryService,
                                              EmsUsedDataDaySummaryService emsUsedDataDaySummaryService,
                                              EmsUsedDataService emsUsedDataService) {
        this.emsUsedDataMonthSummaryService = emsUsedDataMonthSummaryService;
        this.emsUsedDataDaySummaryService = emsUsedDataDaySummaryService;
        this.emsUsedDataService = emsUsedDataService;
    }

    public List<EnergyConsumptionRankingResultDTO> getEnergyConsumptionRankingLvl4(EnergyConsumptionQueryDTO queryDTO) {
        List<EnergyConsumptionRankingResultDTO> resultDTOS;
        switch (queryDTO.getDateType()) {
            case "01": // 年
                resultDTOS = emsUsedDataMonthSummaryService.getEnergyConsumptionRankingLvl4(queryDTO);
                break;
            case "02": // 月
                resultDTOS = emsUsedDataDaySummaryService.getEnergyConsumptionRankingLvl4(queryDTO);
                break;
            case "03": // 日
                if (DateUtils.isQueryingCurrentDay(queryDTO.getYear(), queryDTO.getMonth(), queryDTO.getDay())) {
                    resultDTOS = emsUsedDataService.getEnergyConsumptionRankingLvl4(queryDTO);
                } else {
                    resultDTOS = emsUsedDataDaySummaryService.getEnergyConsumptionRankingLvl4(queryDTO);
                }
                break;
            default:
                throw new MesErrorCodeException("查询时间类型必须是01、02或者03");
        }
        return resultDTOS;
    }


    public List<EnergyConsumptionRankingResultDTO> getEnergyConsumptionRankingLvl3(EnergyConsumptionQueryDTO queryDTO) {
        List<EnergyConsumptionRankingResultDTO> resultDTOS;
        switch (queryDTO.getDateType()) {
            case "01": // 年
                resultDTOS = emsUsedDataMonthSummaryService.getEnergyConsumptionRankingLvl3(queryDTO);
                break;
            case "02": // 月
                resultDTOS = emsUsedDataDaySummaryService.getEnergyConsumptionRankingLvl3(queryDTO);
                break;
            case "03": // 日
                if (DateUtils.isQueryingCurrentDay(queryDTO.getYear(), queryDTO.getMonth(), queryDTO.getDay())) {
                    resultDTOS = emsUsedDataService.getEnergyConsumptionRankingLvl3(queryDTO);
                } else {
                    resultDTOS = emsUsedDataDaySummaryService.getEnergyConsumptionRankingLvl3(queryDTO);
                }
                break;
            default:
                throw new MesErrorCodeException("查询时间类型必须是01、02或者03");
        }
        return resultDTOS;
    }

    public List<EnergyConsumptionRankingResultDTO> getEnergyConsumptionRankingLvl2(EnergyConsumptionQueryDTO queryDTO) {
        List<EnergyConsumptionRankingResultDTO> resultDTOS;
        switch (queryDTO.getDateType()) {
            case "01": // 年
                resultDTOS = emsUsedDataMonthSummaryService.getEnergyConsumptionRankingLvl2(queryDTO);
                break;
            case "02": // 月
                resultDTOS = emsUsedDataDaySummaryService.getEnergyConsumptionRankingLvl2(queryDTO);
                break;
            case "03": // 日
                if (DateUtils.isQueryingCurrentDay(queryDTO.getYear(), queryDTO.getMonth(), queryDTO.getDay())) {
                    resultDTOS = emsUsedDataService.getEnergyConsumptionRankingLvl2(queryDTO);
                } else {
                    resultDTOS = emsUsedDataDaySummaryService.getEnergyConsumptionRankingLvl2(queryDTO);
                }
                break;
            default:
                throw new MesErrorCodeException("查询时间类型必须是01、02或者03");
        }
        return resultDTOS;
    }

    public List<EnergyConsumptionRankingResultDTO> getEnergyConsumptionRankingLvl1(EnergyConsumptionQueryDTO queryDTO) {
        List<EnergyConsumptionRankingResultDTO> resultDTOS;
        switch (queryDTO.getDateType()) {
            case "01": // 年
                resultDTOS = emsUsedDataMonthSummaryService.getEnergyConsumptionRankingLvl1(queryDTO);
                break;
            case "02": // 月
                resultDTOS = emsUsedDataDaySummaryService.getEnergyConsumptionRankingLvl1(queryDTO);
                break;
            case "03": // 日
                if (DateUtils.isQueryingCurrentDay(queryDTO.getYear(), queryDTO.getMonth(), queryDTO.getDay())) {
                    resultDTOS = emsUsedDataService.getEnergyConsumptionRankingLvl1(queryDTO);
                } else {
                    resultDTOS = emsUsedDataDaySummaryService.getEnergyConsumptionRankingLvl1(queryDTO);
                }
                break;
            default:
                throw new MesErrorCodeException("查询时间类型必须是01、02或者03");
        }
        return resultDTOS;
    }

}