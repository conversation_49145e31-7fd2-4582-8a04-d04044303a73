
package cec.jiutian.ems.service.business;

import cec.jiutian.ems.dto.EnergyCostBoardTotalChargeResultDTO;
import cec.jiutian.ems.dto.EnergyCostBoardUsageResultDTO;
import cec.jiutian.ems.mapper.EmsUnitPriceMapper;
import cec.jiutian.ems.po.EmsChargeSubtotalPO;
import cec.jiutian.ems.po.EmsUnitPricePO;
import cec.jiutian.ems.query.dto.EnergyConsumptionQueryDTO;
import cec.jiutian.ems.service.EmsChargeSubtotalService;
import cec.jiutian.ems.service.EmsUsedDataDaySummaryService;
import cec.jiutian.ems.service.EmsUsedDataMonthSummaryService;
import cec.jiutian.ems.utils.DateUtils;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.PostMapping;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 能源费用看板业务
 */
@Slf4j
@Service
@Transactional
@AllArgsConstructor
public class EnergyCostBoardBizService {

    private final EmsUsedDataDaySummaryService emsUsedDataDaySummaryService;
    private final EmsUsedDataMonthSummaryService emsUsedDataMonthSummaryService;
    private final EmsChargeSubtotalService emsChargeSubtotalService;

    public static void processStartTimeAndEndTime(EnergyConsumptionQueryDTO queryDTO) {
        Calendar cal = Calendar.getInstance();
        int toYear = cal.get(Calendar.YEAR);
        int toMonth = cal.get(Calendar.MONTH);
        int today = cal.get(Calendar.DAY_OF_MONTH);
        Date start = null;
        Date end = null;
        if ("01".equals(queryDTO.getDateType())) { // 01  年
            queryDTO.setMonth(null);
            if (DateUtils.isQueryingCurrentYear(queryDTO.getYear())) {
                cal.set(queryDTO.getYear(), Calendar.JANUARY, 1, 0, 0, 0);
                start = cal.getTime();
                cal.set(queryDTO.getYear(), toMonth, today - 1, 0, 0, 0);
            } else {
                cal.set(queryDTO.getYear(), Calendar.JANUARY, 1, 0, 0, 0);
                start = cal.getTime();
                cal.add(Calendar.YEAR, 1);
                cal.add(Calendar.DAY_OF_MONTH, -1);
            }
        } else { // 02月
            if (DateUtils.isQueryingCurrentMonth(queryDTO.getYear(), queryDTO.getMonth())) {
                cal.set(toYear, toMonth, 1, 0, 0, 0);
                start = cal.getTime();
                cal.set(toYear, toMonth, today - 1, 0, 0, 0);
            } else {
                cal.set(toYear, queryDTO.getMonth() - 1, 1, 0, 0, 0);
                start = cal.getTime();
                cal.add(Calendar.MONTH, 1);
                cal.add(Calendar.DAY_OF_MONTH, -1);
            }
        }
        queryDTO.setStartTime(start);
        queryDTO.setEndTime(cal.getTime());
    }

    public Map<String, EnergyCostBoardTotalChargeResultDTO> getTotalElectricityCostOverview(EnergyConsumptionQueryDTO queryDTO) {
        processStartTimeAndEndTime(queryDTO);
        int totalHours = 24 * DateUtils.getDayCountByRange(queryDTO.getStartTime(), queryDTO.getEndTime());
        List<EnergyCostBoardTotalChargeResultDTO> costOverview = emsChargeSubtotalService.getTotalElectricityCostOverview(queryDTO);
        Map<String, List<EnergyCostBoardTotalChargeResultDTO>> periodToResult = costOverview.stream().collect(Collectors.groupingBy(EnergyCostBoardTotalChargeResultDTO::getTimePeriod));
        EnergyCostBoardTotalChargeResultDTO total = periodToResult.get("合计").get(0);
        Map<String, EnergyCostBoardTotalChargeResultDTO> resultDTOMap = new HashMap<>();
        periodToResult.forEach((k, v) -> { //尖，List<EnergyCostBoardTotalChargeResultDTO>
            EnergyCostBoardTotalChargeResultDTO currentProcessing = v.get(0);
            currentProcessing.calculatePercentage(total.getTotalCharge(), totalHours);
            resultDTOMap.put(k, currentProcessing);
        });
        //临时处理

        return resultDTOMap;
    }

    public List<EnergyCostBoardUsageResultDTO> getTotalElectricityCostLineChart(EnergyConsumptionQueryDTO queryDTO) {
        processStartTimeAndEndTime(queryDTO);
        List<EmsChargeSubtotalPO> lineChartResult = Objects.equals(queryDTO.getDateType(), "01")  // 01 年
                ? emsChargeSubtotalService.getTotalElectricityCostLineChartByYear(queryDTO)
                : emsChargeSubtotalService.getTotalElectricityCostLineChartByMonth(queryDTO);
        Map<Date, List<EmsChargeSubtotalPO>> timeToPOList = lineChartResult.stream().collect(Collectors.groupingBy(EmsChargeSubtotalPO::getDate));
        String dateFormat = "yyyy-" + (Objects.equals(queryDTO.getDateType(), "01") ? "MM" : "MM-dd");
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(dateFormat);
        List<EnergyCostBoardUsageResultDTO> resultDTOList = new ArrayList<>();
        timeToPOList.forEach((k, v) -> {// 2023-06-21，List<EmsChargeSubtotalPO>
            EnergyCostBoardUsageResultDTO tempResultDTO = new EnergyCostBoardUsageResultDTO(k, simpleDateFormat.format(k));
            v.forEach(tempPO -> {
                if (!Objects.isNull(tempPO.getTimePeriod())) {
                    switch (tempPO.getTimePeriod()) {
                        case "尖":
                            tempResultDTO.setUsagePeek(BigDecimal.valueOf(tempPO.getTotalCharge()));
                            break;
                        case "峰":
                            tempResultDTO.setUsageBrae(BigDecimal.valueOf(tempPO.getTotalCharge()));
                            break;
                        case "平":
                            tempResultDTO.setUsagePlain(BigDecimal.valueOf(tempPO.getTotalCharge()));
                            break;
                        case "谷":
                            tempResultDTO.setUsageValley(BigDecimal.valueOf(tempPO.getTotalCharge()));
                            break;
                    }
                }
            });
            resultDTOList.add(tempResultDTO);
        });
        return resultDTOList.stream().sorted(Comparator.comparing(EnergyCostBoardUsageResultDTO::getDate)).collect(Collectors.toList());
    }
}