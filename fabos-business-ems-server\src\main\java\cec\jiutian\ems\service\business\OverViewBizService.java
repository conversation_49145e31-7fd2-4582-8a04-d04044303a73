
package cec.jiutian.ems.service.business;

import cec.jiutian.ems.constant.EmsConstant;
import cec.jiutian.ems.query.dto.CarbonEmissionDTO;
import cec.jiutian.ems.query.dto.ConsumptionTrendDTO;
import cec.jiutian.ems.query.dto.LoadTrendDTO;
import cec.jiutian.ems.query.dto.OverViewQueryDTO;
import cec.jiutian.ems.service.EmsUsedDataService;
import cec.jiutian.ems.utils.DateUtils;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.GregorianCalendar;
import java.util.List;

/**
 * 总览查询业务
 *
 * <AUTHOR> CodeGenerator
 * @date : 2023-5-29
 */
@Slf4j
@Service
@Transactional
@AllArgsConstructor
public class OverViewBizService {
    private final EmsUsedDataService emsUsedDataService;


    public Object getEnergySort(OverViewQueryDTO queryDTO) {
        return emsUsedDataService.getEnergySort(queryDTO);
    }

    public Object getFactoryEnergy(OverViewQueryDTO queryDTO) {
        return emsUsedDataService.getFactoryEnergy(queryDTO);
    }

    // 1：根据timeType计算出同比的日期
    public Object getCarbonEmission(OverViewQueryDTO queryDTO) {
        DateUtils.setChainDay(queryDTO);
        return emsUsedDataService.getCarbonEmission(queryDTO);
    }

    // 年月日分别查询
    public Object getTotalLoadTrend(OverViewQueryDTO queryDTO) {
        DateUtils.setChainDay(queryDTO);
        String timeType = queryDTO.getTimeType();
        Integer year = queryDTO.getYear();
        Integer month = queryDTO.getMonth();
        Calendar calendar = new GregorianCalendar();
        List<LoadTrendDTO> result = new ArrayList<>();
        switch (timeType) {
            case EmsConstant.timeType.YEAR:
                result = emsUsedDataService.getTotalLoadTrendYear(queryDTO);
                break;

            case EmsConstant.timeType.MONTH:
                // 构造当月的日期
                calendar.set(year, month -1, 1);
                String dateStr = DateUtils.parseDateToString(calendar.getTime());
                queryDTO.setDateStr(dateStr);
                result = emsUsedDataService.getTotalLoadTrendMonth(queryDTO);
                break;
            case EmsConstant.timeType.DAY:
                result = emsUsedDataService.getTotalLoadTrendDay(queryDTO);
                break;
            default:
                break;
        }
        return result;
    }

    // 年月日分别查询
    public Object getConsumptionTrend(OverViewQueryDTO queryDTO) {
        DateUtils.setChainDay(queryDTO);
        String timeType = queryDTO.getTimeType();
        Integer year = queryDTO.getYear();
        Integer month = queryDTO.getMonth();
        Calendar calendar = new GregorianCalendar();
        List<ConsumptionTrendDTO> result = new ArrayList<>();
        switch (timeType) {
            case EmsConstant.timeType.YEAR:
                result = emsUsedDataService.getConsumptionTrendYear(queryDTO);
                break;

            case EmsConstant.timeType.MONTH:
                // 构造当月的日期
                calendar.set(year, month-1, 1);
                String dateStr = DateUtils.parseDateToString(calendar.getTime());
                queryDTO.setDateStr(dateStr);
                result = emsUsedDataService.getConsumptionTrendMonth(queryDTO);
                break;
            case EmsConstant.timeType.DAY:
                result = emsUsedDataService.getConsumptionTrendDay(queryDTO);
                break;
            default:
                break;
        }
        return result;
    }

    public Object getEnergyBalance(OverViewQueryDTO queryDTO) {
        return emsUsedDataService.getEnergyBalance(queryDTO);
    }

    public Object getFactory() { return emsUsedDataService.getFactory();
    }
}