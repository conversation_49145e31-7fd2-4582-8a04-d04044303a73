
package cec.jiutian.ems.service.business;

import cec.jiutian.ems.dto.EnergyConsumptionSimpleResultDTO;
import cec.jiutian.ems.dto.EnergyUnitConsumptionRecordResultDTO;
import cec.jiutian.ems.query.dto.EnergyConsumptionQueryDTO;
import cec.jiutian.ems.service.EmsUsedDataDaySummaryService;
import cec.jiutian.ems.service.EmsUsedDataMonthSummaryService;
import cec.jiutian.ems.service.EmsYieldDataService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 单位能耗业务
 */
@Slf4j
@Service
@Transactional
@AllArgsConstructor
public class UnitEnergyConsumptionBizService {

    private final EmsUsedDataDaySummaryService emsUsedDataDaySummaryService;
    private final EmsUsedDataMonthSummaryService emsUsedDataMonthSummaryService;
    private final EmsYieldDataService emsYieldDataService;

    private EnergyConsumptionQueryDTO setUpQueryDatesAccordingToYear(EnergyConsumptionQueryDTO queryDTO) {
        Calendar cal = Calendar.getInstance();
        int currentYear = cal.get(Calendar.YEAR);
        cal.set(Calendar.YEAR, queryDTO.getYear());
        cal.set(Calendar.DAY_OF_MONTH, 1);
        cal.set(Calendar.MONTH, 0);
        queryDTO.setStartTime(cal.getTime());
        if (queryDTO.getYear() == currentYear) {
            queryDTO.setEndTime(new Date());
        } else {
            // 如果不是当年，则取选定年份最后一天
            cal.add(Calendar.YEAR, 1);
            cal.add(Calendar.DAY_OF_MONTH, -1);
            queryDTO.setEndTime(cal.getTime());
        }
        return queryDTO;
    }

    private EnergyConsumptionQueryDTO setUpQueryDatesAccordingToMonth(EnergyConsumptionQueryDTO queryDTO) {
        Calendar cal = Calendar.getInstance();
        int currentYear = cal.get(Calendar.YEAR);
        int currentMonth = cal.get(Calendar.MONTH) + 1;
        cal.set(Calendar.YEAR, queryDTO.getYear());
        cal.set(Calendar.MONTH, queryDTO.getMonth() - 1);
        cal.set(Calendar.DAY_OF_MONTH, 1);
        queryDTO.setStartTime(cal.getTime());
        if (queryDTO.getYear() == currentYear && queryDTO.getMonth() == currentMonth) {
            cal = Calendar.getInstance();
            cal.add(Calendar.DAY_OF_MONTH, -1);
            queryDTO.setEndTime(cal.getTime());
        } else {
            // 如果不是当年，则取选定月份最后一天
            cal.add(Calendar.MONTH, 1);
            cal.add(Calendar.DAY_OF_MONTH, -1);
            queryDTO.setEndTime(cal.getTime());
        }
        return queryDTO;
    }

    public List<EnergyConsumptionSimpleResultDTO> getUnitEnergyConsumptionTrend(EnergyConsumptionQueryDTO queryDTO) {
        List<EnergyConsumptionSimpleResultDTO> consumptionTrend;
        if (Objects.equals(queryDTO.getDateType(), "01")) { // 按年
            consumptionTrend = emsUsedDataMonthSummaryService.getUnitEnergyConsumptionTrend(setUpQueryDatesAccordingToYear(queryDTO));
        } else { // 按月, 显示天
            consumptionTrend = emsUsedDataDaySummaryService.getUnitEnergyConsumptionTrend(setUpQueryDatesAccordingToMonth(queryDTO));
        }
        consumptionTrend.forEach(e -> e.setUsage(e.getUsage().setScale(6, RoundingMode.HALF_UP)));
        return consumptionTrend;
    }

    public List<EnergyUnitConsumptionRecordResultDTO> getUnitEnergyConsumptionRecord(EnergyConsumptionQueryDTO queryDTO) {
        List<EnergyUnitConsumptionRecordResultDTO> resultList = new ArrayList<>();
        List<EnergyConsumptionSimpleResultDTO> consumptionTrend;
        if (Objects.equals(queryDTO.getDateType(), "01")) { // 按年
            consumptionTrend = emsUsedDataMonthSummaryService.getUnitEnergyConsumptionTrend(setUpQueryDatesAccordingToYear(queryDTO));
        } else { // 按月, 显示天
            consumptionTrend = emsUsedDataDaySummaryService.getUnitEnergyConsumptionTrend(setUpQueryDatesAccordingToMonth(queryDTO));
        }
        //  size = 4        index
        //  06-01  05-31    1,0
        //  06-02  06-01    2,1
        //  06-03  06-02    3,2
        if (CollectionUtils.isNotEmpty(consumptionTrend)) {
            for (int i = 1; i <= consumptionTrend.size() - 1; i++) {
                EnergyConsumptionSimpleResultDTO dtoCRTRange = consumptionTrend.get(i);
                EnergyConsumptionSimpleResultDTO dtoLASTRange = consumptionTrend.get(i - 1);
                resultList.add(EnergyUnitConsumptionRecordResultDTO.build(dtoCRTRange, dtoLASTRange));
            }
        }
        return resultList;
    }
}