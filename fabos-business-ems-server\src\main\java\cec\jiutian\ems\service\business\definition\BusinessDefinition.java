package cec.jiutian.ems.service.business.definition;

import cec.jiutian.core.service.BusinessDefinitionService;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2023/05/17
 */
@RestController
public class BusinessDefinition extends BusinessDefinitionService {
    @Override
    public Object getAllOperationCode() {
        return super.getAllOperationCode(BusinessDefinition.class);
    }

    /**
     * 标准煤折算系数
     */
    public static class EnergyConsumptionConstants {
        /**
         * 标准煤折算系数-电
         */
        public static final BigDecimal STANDARD_COAL_CONVERSION_FACTOR_ELECTRICITY = new BigDecimal("0.1229");
        /**
         * 标准煤折算系数-水
         */
        public static final BigDecimal STANDARD_COAL_CONVERSION_FACTOR_WATER = new BigDecimal("0.0857");
        /**
         * 标准煤折算系数-气
         */
        public static final BigDecimal STANDARD_COAL_CONVERSION_FACTOR_GAS = new BigDecimal("1.2143");
    }

    public static class EnergyTypes {
        public static final String ENERGY_TYPE_WATER = "水";
        public static final String ENERGY_TYPE_GAS = "气";
        public static final String ENERGY_TYPE_ELECTRICITY = "电";

    }

    public static class LocationTreeErrors {
        public static final String LOCATION_GID_NOT_EXISTS = "节点不存在";
        public static final String LOCATION_NAME_ALREADY_EXISTS = "节点名称已存在";
    }

    public static class ParamInfoErrors {
        public static final String PARAM_NAME_ALREADY_EXISTS = "测点名称已存在";
    }

    public static class ParamTypes {
        public static final String PARAM_TYPE_WATER = "waterConsumption";
        public static final String PARAM_TYPE_GAS = "gasConsumption";
        public static final String PARAM_TYPE_ELECTRICITY = "electricityConsumption";
        public static final String PARAM_TYPE_CARBON = "CarbonEmission";
        public static final String PARAM_TYPE_STANDARD_COAL = "convertedStandardCoalConsumption";
    }

}
