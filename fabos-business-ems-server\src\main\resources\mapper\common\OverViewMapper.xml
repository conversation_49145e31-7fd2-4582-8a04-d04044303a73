<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cec.jiutian.ems.mapper.OverViewMapper">
    <resultMap id="EnergyUsageDTO" type="cec.jiutian.ems.query.dto.EnergyUsageDTO">
        <result column="area" jdbcType="VARCHAR" property="area"/>
        <result column="electricityConsumption" jdbcType="DECIMAL" property="electricityConsumption"/>
        <result column="waterConsumption" jdbcType="DECIMAL" property="waterConsumption"/>
        <result column="gasConsumption" jdbcType="DECIMAL" property="gasConsumption"/>
        <result column="comprehensiveEnergy" jdbcType="DECIMAL" property="comprehensiveEnergy"/>
    </resultMap>

    <select id="getEnergySort" resultMap="EnergyUsageDTO">
        <if test='locationName == null or locationName == ""'>
        select H.location_name AS area
        ,  sum(case when  B.param_type = 'comprehensiveEnergy' then a.total_value else 0 end) AS comprehensiveEnergy
        ,  sum(case when  B.param_type = 'electricityConsumption' then a.total_value else 0 end) AS electricityConsumption
        ,  sum(case when  B.param_type = 'waterConsumption' then a.total_value else 0 end) AS waterConsumption
        ,  sum(case when  B.param_type = 'gasConsumption' then a.total_value else 0 end) AS gasConsumption

        from ems_used_data A
        join ems_param_info B ON A.param_name = B.param_name
        JOIN ems_location_device_param_relation C ON C.param_name = B.param_name
        JOIN ems_location_tree D ON D.gid = C.location_gid
        join ems_location_tree H ON D.level1_location_gid = H.gid
        WHERE B.param_type in ('comprehensiveEnergy','electricityConsumption','waterConsumption','gasConsumption')
        and D.location_type='line'
        and D.gid not in (select parent_gid from  ems_location_tree)
        AND (A.year = #{year})
        AND (A.month = (case #{timeType} when 'year' then A.month else #{month} end ) )
        AND (A.day = (case #{timeType} when 'day' then #{day}  else A.day end ))
        group by H.location_name
        </if>
        <if test='locationName != null and locationName != ""'>
            select H.location_name AS area
            ,  sum(case when  B.param_type = 'comprehensiveEnergy' then a.total_value else 0 end) AS comprehensiveEnergy
            ,  sum(case when  B.param_type = 'electricityConsumption' then a.total_value else 0 end) AS electricityConsumption
            ,  sum(case when  B.param_type = 'waterConsumption' then a.total_value else 0 end) AS waterConsumption
            ,  sum(case when  B.param_type = 'gasConsumption' then a.total_value else 0 end) AS gasConsumption

            from ems_used_data A
            join ems_param_info B ON A.param_name = B.param_name
            JOIN ems_location_device_param_relation C ON C.param_name = B.param_name
            JOIN ems_location_tree D ON D.gid = C.location_gid
            left join ems_location_tree H ON D.level2_location_gid = H.gid
            join (select A.location_name as s,B.location_name as e
                  FROM ems_location_tree A join ems_location_tree B on A.level1_location_gid = B.gid
				   WHERE A.location_level = '2') AS BB on H.location_name=BB.s
            WHERE B.param_type in ('comprehensiveEnergy','electricityConsumption','waterConsumption','gasConsumption')
            and D.location_type='line'
            and D.gid not in (select parent_gid from  ems_location_tree)
            AND (A.year = #{year})
            AND (A.month = (case #{timeType} when 'year' then A.month else #{month} end ) )
            AND (A.day = (case #{timeType} when 'day' then #{day}  else A.day end ))
            and BB.e = #{locationName}
            group by H.location_name
        </if>
    </select>
    <select id="getUsedDataByLocId" resultType="cec.jiutian.ems.po.EmsUsedDataPO">
        select e1.*,e2.location_name as device_name from ems_used_data e1,ems_location_tree e2 WHERE e1.location_gid=e2.gid

    <if test='gid != null and gid != ""'>
        and( e1.location_gid = #{gid}
        or e1.level1_location_gid = #{gid} or e1.level2_location_gid = #{gid}
        or e1.level3_location_gid = #{gid} or e1.level4_location_gid = #{gid}
        )
    </if>

    </select>
    <select id="getGroupedUsedDataByHours" resultType="cec.jiutian.ems.dto.EnergyConsumptionSimpleResultDTO">
        select sum(total_value) as usage, hour as time
        from ems_used_data ud
        <if test='energyType != null and energyType != ""'>
            left join ems_param_info epi on ud.param_name = epi.param_name
        </if>
        where day = #{day}
        <if test='energyType != null and energyType != ""'>
            and epi.param_type = #{energyType}
        </if>
        and year = #{year}
        and month = #{month}
        group by hour;
    </select>

    <select id="getGroupedUsedDataByHoursAndType" resultType="cec.jiutian.ems.dto.EnergyConsumptionSimpleResultDTO">
        select sum(total_value) as usage, epi.param_type as energyType
        from ems_used_data ud
                 left join ems_param_info epi on ud.param_name = epi.param_name
        where day = #{day}
          and year = #{year}
          and month = #{month}
          and hour &lt;= #{hour}
        group by epi.param_type;
    </select>

    <select id="getUsedDataByDay" resultType="cec.jiutian.ems.po.EmsUsedDataPO">
        select *
        from ems_used_data
        where day = #{day}
          and year = #{year}
          and month = #{month}
    </select>
    <select id="getGroupedByLocationGidListAndYearMonthDay"
            resultType="cec.jiutian.ems.dto.EnergyConsumptionUsageResultDTO">
        select sum(total_value) as usage, elt.location_name as locationName, elt.gid as locationGid,
        epi.param_type as paramType, uds.hour as time
        from ems_used_data uds
        left join ems_location_tree elt on uds.location_gid = elt.gid
        left join ems_param_info epi on uds.param_name = epi.param_name
        where uds.day = #{day}
        and uds.year = #{year}
        and uds.month = #{month}
        <if test='energyType != null and energyType != ""'>
            and epi.param_type = #{energyType}
        </if>
        <if test="locationGidList != null and locationGidList.size() > 0">
            and (uds.location_gid) in
            <foreach collection="locationGidList" index="index" item="item" close=")" open="(" separator=",">
                (#{item})
            </foreach>
        </if>
        group by elt.location_name, elt.gid, uds.day, epi.param_type
    </select>
    <select id="getFactoryEnergy" resultMap="EnergyUsageDTO">
        select
            SUM ( CASE WHEN B.param_type = 'electricityConsumption' THEN A.total_value ELSE 0 END ) AS electricityConsumption,
            SUM ( CASE WHEN B.param_type = 'waterConsumption' THEN A.total_value ELSE 0 END ) AS waterConsumption,
            SUM ( CASE WHEN B.param_type = 'gasConsumption' THEN A.total_value ELSE 0 END ) AS gasConsumption

        from ems_used_data A
                 join ems_param_info B ON A.param_name = B.param_name
                 JOIN ems_location_device_param_relation C ON C.param_name = B.param_name
                 JOIN ems_location_tree D  ON D.gid = C.location_gid
                 left join ems_location_tree H ON D.level1_location_gid = H.gid
        WHERE B.param_type in ('electricityConsumption','waterConsumption','gasConsumption')
            and D.location_type='line'
          and D.gid not in (select parent_gid from  ems_location_tree)
            and B.unit in ('kW·h','m³','t')
          AND (A.year = #{year})
          AND (A.month = (case #{timeType} when 'year' then A.month else #{month} end ) )
          AND (A.day = (case #{timeType} when 'day' then #{day}  else A.day end ))
        <if test='locationName != null and locationName != ""'>
            and H.location_name = #{locationName}
        </if>
    </select>
    <select id="selTotal" resultType="double">
        select sum(total_usage)
        from ems_charge_subtotal
        where energy_type = 'electricityConsumption';
    </select>
    <resultMap id="CarbonEmissionDTO" type="cec.jiutian.ems.query.dto.CarbonEmissionDTO">
        <result column="area" jdbcType="VARCHAR" property="area"/>
        <result column="carbonEmission" jdbcType="DECIMAL" property="carbonEmission"/>
        <result column="chainValue" jdbcType="DECIMAL" property="chainValue"/>
    </resultMap>
    <!--碳排放-->
    <select id="getCarbonEmission" resultMap="CarbonEmissionDTO">

<!--        select H.location_name AS area,-->
<!--        SUM ( CASE WHEN B.param_type = 'electricityConsumption' THEN A.total_value ELSE 0 END ) AS electricityConsumption,-->
<!--        SUM ( CASE WHEN B.param_type = 'waterConsumption' THEN A.total_value ELSE 0 END ) AS waterConsumption,-->
<!--        SUM ( CASE WHEN B.param_type = 'gasConsumption' THEN A.total_value ELSE 0 END ) AS gasConsumption ,-->
<!--        SUM ( CASE-->
<!--        WHEN B.param_type = 'waterConsumption' THEN A.total_value * 0.91-->
<!--        when B.param_type = 'electricityConsumption' then A.total_value * 0.0009489-->
<!--        when B.param_type = 'gasConsumption' then A.total_value * 0.0022  ELSE 0 END ) AS CarbonEmission-->

<!--        from ems_used_data A-->
<!--        join ems_param_info B ON A.param_name = B.param_name-->
<!--        JOIN ems_location_device_param_relation C ON C.param_name = B.param_name-->
<!--        JOIN ems_location_tree D  ON D.gid = C.location_gid-->
<!--        left join ems_location_tree H ON D.level1_location_gid = H.gid-->
<!--        WHERE B.param_type in ('electricityConsumption','waterConsumption','gasConsumption')-->
<!--        and D.location_type='line'-->
<!--        and D.gid not in (select parent_gid from  ems_location_tree)-->
<!--        AND (A.year = #{year})-->
<!--&lt;!&ndash;        AND (A.month = (case #{timeType} when 'year' then A.month else #{month} end ) )&ndash;&gt;-->
<!--&lt;!&ndash;        AND (A.day = (case #{timeType} when 'day' then #{day}  else A.day  end ))&ndash;&gt;-->
<!--        <if test='locationName != null and locationName != ""'>-->
<!--            and H.location_name = #{locationName}-->
<!--        </if>-->
<!--        GROUP BY H.location_name-->
        with AA AS(
            select H.location_name AS area,
                   SUM ( CASE WHEN B.param_type = 'electricityConsumption' THEN A.total_value  ELSE 0 END ) AS electricityConsumption,
                   SUM ( CASE WHEN B.param_type = 'waterConsumption'  THEN A.total_value ELSE 0 END ) AS waterConsumption,
                   SUM ( CASE WHEN B.param_type = 'gasConsumption' THEN A.total_value ELSE 0 END ) AS gasConsumption ,
                   SUM ( CASE
                             WHEN B.param_type = 'waterConsumption'  THEN A.total_value * 0
                             when B.param_type = 'electricityConsumption'  then round(A.total_value * 0.0009489,2)
                             when B.param_type = 'gasConsumption' then round(A.total_value * 0.0022,2)  ELSE 0 END ) AS CarbonEmission
            from ems_used_data A
                     join ems_param_info B ON A.param_name = B.param_name
                     JOIN ems_location_device_param_relation C ON C.param_name = B.param_name
                     JOIN ems_location_tree D  ON D.gid = C.location_gid
                     left join ems_location_tree H ON D.level1_location_gid = H.gid
            WHERE B.param_type in ('electricityConsumption','waterConsumption','gasConsumption')
                and D.location_type='line'
              and D.gid not in (select parent_gid from  ems_location_tree)
               AND (A.year = #{year})
              	AND (A.month = (case #{timeType} when 'year' then A.month else #{month} end ) )
              	AND (A.day = (case #{timeType} when 'day' then #{day}  else A.day  end ))
                <if test='locationName != null and locationName != ""'>
                    and H.location_name = #{locationName}
                </if>
            GROUP BY H.location_name
        ),
             BB AS(
                 select H.location_name AS area,
                        SUM ( CASE WHEN B.param_type = 'electricityConsumption' THEN A.total_value ELSE 0 END ) AS electricityConsumption,
                        SUM ( CASE WHEN B.param_type = 'waterConsumption' THEN A.total_value ELSE 0 END ) AS waterConsumption,
                        SUM ( CASE WHEN B.param_type = 'gasConsumption' THEN A.total_value ELSE 0 END ) AS gasConsumption ,
                        SUM ( CASE
                                  WHEN B.param_type = 'waterConsumption' THEN A.total_value * 0.91
                                  when B.param_type = 'electricityConsumption' then A.total_value * 0.0009489
                                  when B.param_type = 'gasConsumption' then A.total_value * 0.0022
                                  ELSE 0 END) AS carbonEmission

                 from ems_used_data A
                          join ems_param_info B ON A.param_name = B.param_name
                          JOIN ems_location_device_param_relation C ON C.param_name = B.param_name
                          JOIN ems_location_tree D ON D.gid = C.location_gid
                          left join ems_location_tree H ON D.level1_location_gid = H.gid
                 WHERE B.param_type in ('electricityConsumption', 'waterConsumption', 'gasConsumption')
                   and D.gid not in (select parent_gid from ems_location_tree)
                   AND (A.year = #{chainYear})
                   AND (A.month = (case #{timeType} when 'year' then A.month else #{chainMonth} end))
                   AND (A.day = (case #{timeType} when 'day' then #{chainDay} else A.day end))
                    <if test='locationName != null and locationName != ""'>
                        and H.location_name = #{locationName}
                    </if>
                 GROUP BY H.location_name)
        select AA.*
             , ROUND((case when (AA.carbonEmission = 0 or AA.carbonEmission is NULL)
                 then 0 else 100*(AA.carbonEmission - (case when BB.carbonEmission IS NULL then 0 else BB.carbonEmission end) )/BB.carbonEmission END), 2)  chainValue
        from  AA
                  left join  BB ON AA.area = BB.area

    </select>
    <resultMap id="LoadTrendDTO" type="cec.jiutian.ems.query.dto.LoadTrendDTO">
        <result column="startTime" jdbcType="VARCHAR" property="startTime"/>
        <result column="endTime" jdbcType="VARCHAR" property="endTime"/>
        <result column="powerLoad" jdbcType="DECIMAL" property="powerLoad"/>
        <result column="chainPowerLoad" jdbcType="DECIMAL" property="chainPowerLoad"/>
    </resultMap>
    <select id="getTotalLoadTrendYear" resultMap="LoadTrendDTO">
        WITH TM AS(
            SELECT 1 as startTime , 2 as endTime
            UNION SELECT 2 as startTime , 3 as endTime
            UNION SELECT 3 as startTime , 4 as endTime
            UNION SELECT 4 as startTime , 5 as endTime
            UNION SELECT 5 as startTime , 6 as endTime
            UNION SELECT 6 as startTime , 7 as endTime
            UNION SELECT 7 as startTime , 8 as endTime
            UNION SELECT 8 as startTime , 9 as endTime
            UNION SELECT 9 as startTime , 10 as endTime
            UNION SELECT 10 as startTime , 11 as endTime
            UNION SELECT 11 as startTime , 12 as endTime
            UNION SELECT 12 as startTime , 1 as endTime
        ),
             AA AS (
                 select  A.year,a.month,
                         avg(CASE WHEN A.total_value is not null THEN  A.total_value ELSE 0 END) AS  total_value

                 from ems_used_data A
                          join ems_param_info B ON A.param_name = B.param_name and B.param_type in ('powerLoad')
                          JOIN ems_location_device_param_relation C ON C.param_name = B.param_name
                          JOIN ems_location_tree D  ON D.gid = C.location_gid and D.gid not in (select parent_gid from  ems_location_tree)
                          left join ems_location_tree H ON D.level1_location_gid = H.gid
                  where (A.year = #{year})
                    and D.location_type='line'
                    <if test='locationName != null and locationName != ""'>
                        and H.location_name = #{locationName}
                    </if>
                 GROUP BY  A.year,a.month
             ),
             BB AS (
                 select  A.year,a.month,
                         avg(CASE WHEN A.total_value is not null THEN  A.total_value ELSE 0 END) AS  total_value

                 from ems_used_data A
                          join ems_param_info B ON A.param_name = B.param_name and B.param_type in ('powerLoad')
                          JOIN ems_location_device_param_relation C ON C.param_name = B.param_name
                          JOIN ems_location_tree D  ON D.gid = C.location_gid and D.gid not in (select parent_gid from  ems_location_tree)
                          left join ems_location_tree H ON D.level1_location_gid = H.gid
                  where (A.year = #{chainYear})
                    and D.location_type='line'
                    <if test='locationName != null and locationName != ""'>
                        and H.location_name = #{locationName}
                    </if>
                 GROUP BY  A.year,a.month
             )

        select
            TM.startTime, TM.endTime
             , round(avg(case when AA.total_value is null then  0  else AA.total_value end),3)   powerLoad
             , round(avg(case when BB.total_value is null then  0  else BB.total_value end),3)   chainPowerLoad

        from TM left JOIN  AA on AA.month = TM.startTime
                left join BB ON BB.month = TM.startTime

        GROUP BY TM.startTime, TM.endTime
        ORDER BY TM.startTime
    </select>
    <select id="getTotalLoadTrendMonth" resultMap="LoadTrendDTO">
        WITH TM AS(
            SELECT CAST
                       ( to_char( d, 'DD' ) AS INT ) startTime
            FROM
                generate_series ( #{dateStr} :: DATE, ( SELECT date_trunc( 'month', #{dateStr} :: DATE ) + INTERVAL '1 month' - INTERVAL '1 day' ) :: DATE, '1 DAY' ) AS d
        ),
             AA AS (
                 select  A.year,a.month,a.day,
                         avg(CASE WHEN A.total_value is not null THEN  A.total_value ELSE 0 END) AS  total_value

                 from ems_used_data A
                          join ems_param_info B ON A.param_name = B.param_name and B.param_type in ('powerLoad')
                          JOIN ems_location_device_param_relation C ON C.param_name = B.param_name
                          JOIN ems_location_tree D  ON D.gid = C.location_gid and D.gid not in (select parent_gid from  ems_location_tree)
                          left join ems_location_tree H ON D.level1_location_gid = H.gid
                  where (A.year = #{year})
                  AND (A.month = #{month})
                and D.location_type='line'
                    <if test='locationName != null and locationName != ""'>
                        and H.location_name = #{locationName}
                    </if>
                 GROUP BY  A.year,a.month,A.day
             ),
             BB AS (
                 select  A.year,a.month,a.day,
                         avg(CASE WHEN A.total_value is not null THEN  A.total_value ELSE 0 END) AS  total_value
                 from ems_used_data A
                          join ems_param_info B ON A.param_name = B.param_name and B.param_type in ('powerLoad')
                          JOIN ems_location_device_param_relation C ON C.param_name = B.param_name
                          JOIN ems_location_tree D  ON D.gid = C.location_gid and D.gid not in (select parent_gid from  ems_location_tree)
                          left join ems_location_tree H ON D.level1_location_gid = H.gid
                  where (A.year = #{chainYear})
                  AND (A.month = #{chainMonth})
                and D.location_type='line'
                    <if test='locationName != null and locationName != ""'>
                        and H.location_name = #{locationName}
                    </if>
                 GROUP BY  A.year,a.month,A.day
             )
        select
            TM.startTime
             , round(avg(case when AA.total_value is null then  0  else AA.total_value end),3)   powerLoad
             , round(avg(case when BB.total_value is null then  0  else BB.total_value end),3)   chainPowerLoad

        from TM left JOIN  AA on AA.DAY = TM.startTime
                left join BB ON BB.DAY = TM.startTime

        GROUP BY TM.startTime
        ORDER BY TM.startTime
    </select>
    <select id="getTotalLoadTrendDay" resultMap="LoadTrendDTO">
        WITH TM AS(
            SELECT
                0 as startTime , 1 as endTime
            UNION SELECT 1 as startTime , 2 as endTime
            UNION SELECT 2 as startTime , 3 as endTime
            UNION SELECT 3 as startTime , 4 as endTime
            UNION SELECT 4 as startTime , 5 as endTime
            UNION SELECT 5 as startTime , 6 as endTime
            UNION SELECT 6 as startTime , 7 as endTime
            UNION SELECT 7 as startTime , 8 as endTime
            UNION SELECT 8 as startTime , 9 as endTime
            UNION SELECT 9 as startTime , 10 as endTime
            UNION SELECT 10 as startTime , 11 as endTime
            UNION SELECT 11 as startTime , 12 as endTime
            UNION SELECT 12 as startTime , 13 as endTime
            UNION SELECT 13 as startTime , 14 as endTime
            UNION SELECT 14 as startTime , 15 as endTime
            UNION SELECT 15 as startTime , 16 as endTime
            UNION SELECT 16 as startTime , 17 as endTime
            UNION SELECT 17 as startTime , 18 as endTime
            UNION SELECT 18 as startTime , 19 as endTime
            UNION SELECT 19 as startTime , 20 as endTime
            UNION SELECT 20 as startTime , 21 as endTime
            UNION SELECT 21 as startTime , 22 as endTime
            UNION SELECT 22 as startTime , 23 as endTime
            UNION SELECT 23 as startTime , 24 as endTime
        ),
             AA AS (
                 select  A.year,a.month,a.day,a.hour,
                         avg(CASE WHEN A.total_value is not null THEN  A.total_value ELSE 0 END) AS  total_value

                 from ems_used_data A
                          join ems_param_info B ON A.param_name = B.param_name and B.param_type in ('powerLoad')
                          JOIN ems_location_device_param_relation C ON C.param_name = B.param_name
                          JOIN ems_location_tree D  ON D.gid = C.location_gid and D.gid not in (select parent_gid from  ems_location_tree)
                          left join ems_location_tree H ON D.level1_location_gid = H.gid
                  where (A.year = #{year})
                 AND (A.month = #{month})
                 AND (A.day = #{day})
        and D.location_type='line'
                    <if test='locationName != null and locationName != ""'>
                        and H.location_name = #{locationName}
                    </if>
                 GROUP BY  A.year,a.month,A.day,A.hour
             ),
             BB AS (
                 select  A.year,a.month,a.day,a.hour,
                         avg(CASE WHEN A.total_value is not null THEN  A.total_value ELSE 0 END) AS  total_value

                 from ems_used_data A
                          join ems_param_info B ON A.param_name = B.param_name and B.param_type in ('powerLoad')
                          JOIN ems_location_device_param_relation C ON C.param_name = B.param_name
                          JOIN ems_location_tree D  ON D.gid = C.location_gid and D.gid not in (select parent_gid from  ems_location_tree)
                          left join ems_location_tree H ON D.level1_location_gid = H.gid
                  where (A.year = #{chainYear})
                 AND (A.month = #{chainMonth})
                 AND (A.day = #{chainDay})
        and D.location_type='line'
                    <if test='locationName != null and locationName != ""'>
                        and H.location_name = #{locationName}
                    </if>
                 GROUP BY  A.year,a.month,A.day,A.hour
             )

        select
            TM.startTime, TM.endTime
             , round(avg(case when AA.total_value is null then  0  else AA.total_value end),3)   powerLoad
             , round(avg(case when BB.total_value is null then  0  else BB.total_value end),3)   chainPowerLoad

        from TM left JOIN  AA on AA.HOUR = TM.startTime
                left join BB ON BB.HOUR = TM.startTime

        GROUP BY TM.startTime, TM.endTime
        ORDER BY TM.startTime
    </select>
    <resultMap id="ConsumptionTrendDTO" type="cec.jiutian.ems.query.dto.ConsumptionTrendDTO">
        <result column="startTime" jdbcType="VARCHAR" property="startTime"/>
        <result column="endTime" jdbcType="VARCHAR" property="endTime"/>
        <result column="comprehensiveEnergy" jdbcType="DECIMAL" property="comprehensiveEnergy"/>
        <result column="chainComprehensiveEnergy" jdbcType="DECIMAL" property="chainComprehensiveEnergy"/>
        <result column="waterConsumption" jdbcType="DECIMAL" property="waterConsumption"/>
        <result column="chainWaterConsumption" jdbcType="DECIMAL" property="chainWaterConsumption"/>
        <result column="gasConsumption" jdbcType="DECIMAL" property="gasConsumption"/>
        <result column="chainGasConsumption" jdbcType="DECIMAL" property="chainGasConsumption"/>
        <result column="electricityConsumption" jdbcType="DECIMAL" property="electricityConsumption"/>
        <result column="chainElectricityConsumption" jdbcType="DECIMAL" property="chainElectricityConsumption"/>
    </resultMap>
    <select id="getConsumptionTrendYear" resultType="cec.jiutian.ems.query.dto.ConsumptionTrendDTO">
        WITH TM AS(
            SELECT 1 as startTime , 2 as endTime
            UNION SELECT 2 as startTime , 3 as endTime
            UNION SELECT 3 as startTime , 4 as endTime
            UNION SELECT 4 as startTime , 5 as endTime
            UNION SELECT 5 as startTime , 6 as endTime
            UNION SELECT 6 as startTime , 7 as endTime
            UNION SELECT 7 as startTime , 8 as endTime
            UNION SELECT 8 as startTime , 9 as endTime
            UNION SELECT 9 as startTime , 10 as endTime
            UNION SELECT 10 as startTime , 11 as endTime
            UNION SELECT 11 as startTime , 12 as endTime
            UNION SELECT 12 as startTime , 1 as endTime
        ),
             AA AS (
                 select  A.year,a.month,
                         sum(CASE WHEN B.param_type = 'comprehensiveEnergy' THEN A.total_value ELSE 0 END) AS  comprehensiveEnergy,
                         sum(CASE WHEN B.param_type = 'electricityConsumption' THEN A.total_value ELSE 0 END ) AS   electricityConsumption,
                         sum(CASE WHEN B.param_type = 'waterConsumption' THEN A.total_value ELSE 0 END ) AS   waterConsumption,
                         sum(CASE WHEN B.param_type = 'gasConsumption' THEN A.total_value ELSE 0 END  ) AS  gasConsumption
                 from ems_used_data A
                          join ems_param_info B ON A.param_name = B.param_name and B.param_type in ( 'comprehensiveEnergy', 'electricityConsumption', 'waterConsumption', 'gasConsumption' )
                          JOIN ems_location_device_param_relation C ON C.param_name = B.param_name
                          JOIN ems_location_tree D  ON D.gid = C.location_gid and D.gid not in (select parent_gid from  ems_location_tree)
                          left join ems_location_tree H ON D.level1_location_gid = H.gid
                  where (A.year = #{year})
        and D.location_type='line'
                        <if test='locationName != null and locationName != ""'>
                            and H.location_name = #{locationName}
                        </if>
                 GROUP BY  A.year,a.month
             ),
             BB AS (
                 select  A.year,a.month,
                         sum( CASE WHEN B.param_type = 'comprehensiveEnergy' THEN A.total_value ELSE 0 END ) AS   comprehensiveEnergy,
                         sum( CASE WHEN B.param_type = 'electricityConsumption' THEN A.total_value ELSE 0 END) AS    electricityConsumption,
                         sum( CASE WHEN B.param_type = 'waterConsumption' THEN A.total_value ELSE 0 END ) AS   waterConsumption,
                         sum( CASE WHEN B.param_type = 'gasConsumption' THEN A.total_value ELSE 0 END ) AS   gasConsumption

                 from ems_used_data A
                          join ems_param_info B ON A.param_name = B.param_name and B.param_type in ( 'comprehensiveEnergy', 'electricityConsumption', 'waterConsumption', 'gasConsumption' )
                          JOIN ems_location_device_param_relation C ON C.param_name = B.param_name
                          JOIN ems_location_tree D  ON D.gid = C.location_gid and D.gid not in (select parent_gid from  ems_location_tree)
                          left join ems_location_tree H ON D.level1_location_gid = H.gid
                  where (A.year = #{chainYear})
        and D.location_type='line'
                        <if test='locationName != null and locationName != ""'>
                            and H.location_name = #{locationName}
                        </if>
                 GROUP BY  A.year,a.month
             )

        select
            TM.startTime, TM.endTime
             , sum(case when AA.comprehensiveEnergy is null then  0  else AA.comprehensiveEnergy end)   comprehensiveEnergy
             , sum(case when BB.comprehensiveEnergy is null then  0  else BB.comprehensiveEnergy end)   chainComprehensiveEnergy

             , sum(case when AA.electricityConsumption is null then  0  else AA.electricityConsumption end)   electricityConsumption
             , sum(case when BB.electricityConsumption is null then  0  else BB.electricityConsumption end)   chainElectricityConsumption

             , sum(case when AA.waterConsumption is null then  0  else AA.waterConsumption end)   waterConsumption
             , sum(case when BB.waterConsumption is null then  0  else BB.waterConsumption end)   chainWaterConsumption

             , sum(case when AA.gasConsumption is null then  0  else AA.gasConsumption end)   gasConsumption
             , sum(case when BB.gasConsumption is null then  0  else BB.gasConsumption end)   chainGasConsumption

        from TM left JOIN  AA on AA.month = TM.startTime
                left JOIN BB ON BB.month = TM.startTime AND BB.month = AA.month

        GROUP BY TM.startTime, TM.endTime
        ORDER BY TM.startTime
    </select>
    <select id="getConsumptionTrendMonth" resultType="cec.jiutian.ems.query.dto.ConsumptionTrendDTO">
        WITH TM AS(
            SELECT CAST
                       ( to_char( d, 'DD' ) AS INT ) startTime
            FROM
                generate_series ( #{dateStr} :: DATE, ( SELECT date_trunc( 'month', #{dateStr} :: DATE ) + INTERVAL '1 month' - INTERVAL '1 day' ) :: DATE, '1 DAY' ) AS d
        ),
             AA AS (
                 select  A.year,a.month,A.day,
                         sum(CASE WHEN B.param_type = 'comprehensiveEnergy' THEN A.total_value ELSE 0 END) AS  comprehensiveEnergy,
                         sum(CASE WHEN B.param_type = 'electricityConsumption' THEN A.total_value ELSE 0 END ) AS   electricityConsumption,
                         sum(CASE WHEN B.param_type = 'waterConsumption' THEN A.total_value ELSE 0 END ) AS   waterConsumption,
                         sum(CASE WHEN B.param_type = 'gasConsumption' THEN A.total_value ELSE 0 END  ) AS  gasConsumption
                 from ems_used_data A
                          join ems_param_info B ON A.param_name = B.param_name and B.param_type in ( 'comprehensiveEnergy', 'electricityConsumption', 'waterConsumption', 'gasConsumption' )
                          JOIN ems_location_device_param_relation C ON C.param_name = B.param_name
                          JOIN ems_location_tree D  ON D.gid = C.location_gid and D.gid not in (select parent_gid from  ems_location_tree)
                          left join ems_location_tree H ON D.level1_location_gid = H.gid
              where (A.year = #{year})
                      AND (A.month = #{month})
        and D.location_type='line'
                        <if test='locationName != null and locationName != ""'>
                            and H.location_name = #{locationName}
                        </if>
                 GROUP BY  A.year,a.month,A.day
             ),
             BB AS (
                 select  A.year,a.month,A.day,
                         sum( CASE WHEN B.param_type = 'comprehensiveEnergy' THEN A.total_value ELSE 0 END ) AS   comprehensiveEnergy,
                         sum( CASE WHEN B.param_type = 'electricityConsumption' THEN A.total_value ELSE 0 END) AS    electricityConsumption,
                         sum( CASE WHEN B.param_type = 'waterConsumption' THEN A.total_value ELSE 0 END ) AS   waterConsumption,
                         sum( CASE WHEN B.param_type = 'gasConsumption' THEN A.total_value ELSE 0 END ) AS   gasConsumption

                 from ems_used_data A
                          join ems_param_info B ON A.param_name = B.param_name and B.param_type in ( 'comprehensiveEnergy', 'electricityConsumption', 'waterConsumption', 'gasConsumption' )
                          JOIN ems_location_device_param_relation C ON C.param_name = B.param_name
                          JOIN ems_location_tree D  ON D.gid = C.location_gid and D.gid not in (select parent_gid from  ems_location_tree)
                          left join ems_location_tree H ON D.level1_location_gid = H.gid
              where (A.year = #{chainYear})
                      AND (A.month = #{chainMonth})
        and D.location_type='line'
                        <if test='locationName != null and locationName != ""'>
                            and H.location_name = #{locationName}
                        </if>
                 GROUP BY  A.year,a.month,A.day
             )

        select
            TM.startTime
             , sum(case when AA.comprehensiveEnergy is null then  0  else AA.comprehensiveEnergy end)   comprehensiveEnergy
             , sum(case when BB.comprehensiveEnergy is null then  0  else BB.comprehensiveEnergy end)   chainComprehensiveEnergy

             , sum(case when AA.electricityConsumption is null then  0  else AA.electricityConsumption end)   electricityConsumption
             , sum(case when BB.electricityConsumption is null then  0  else BB.electricityConsumption end)   chainElectricityConsumption

             , sum(case when AA.waterConsumption is null then  0  else AA.waterConsumption end)   waterConsumption
             , sum(case when BB.waterConsumption is null then  0  else BB.waterConsumption end)   chainWaterConsumption

             , sum(case when AA.gasConsumption is null then  0  else AA.gasConsumption end)   gasConsumption
             , sum(case when BB.gasConsumption is null then  0  else BB.gasConsumption end)   chainGasConsumption

        from TM left JOIN  AA on AA.day = TM.startTime
                left JOIN BB ON BB.day = TM.startTime AND BB.day = AA.day

        GROUP BY TM.startTime
        ORDER BY TM.startTime
    </select>
    <select id="getConsumptionTrendDay" resultType="cec.jiutian.ems.query.dto.ConsumptionTrendDTO">
        WITH TM AS(
            SELECT
                0 as startTime , 1 as endTime
                UNION SELECT 1 as startTime , 2 as endTime
              UNION SELECT 2 as startTime , 3 as endTime
              UNION SELECT 3 as startTime , 4 as endTime
              UNION SELECT 4 as startTime , 5 as endTime
              UNION SELECT 5 as startTime , 6 as endTime
              UNION SELECT 6 as startTime , 7 as endTime
              UNION SELECT 7 as startTime , 8 as endTime
              UNION SELECT 8 as startTime , 9 as endTime
              UNION SELECT 9 as startTime , 10 as endTime
              UNION SELECT 10 as startTime , 11 as endTime
              UNION SELECT 11 as startTime , 12 as endTime
              UNION SELECT 12 as startTime , 13 as endTime
              UNION SELECT 13 as startTime , 14 as endTime
              UNION SELECT 14 as startTime , 15 as endTime
              UNION SELECT 15 as startTime , 16 as endTime
              UNION SELECT 16 as startTime , 17 as endTime
              UNION SELECT 17 as startTime , 18 as endTime
              UNION SELECT 18 as startTime , 19 as endTime
              UNION SELECT 19 as startTime , 20 as endTime
              UNION SELECT 20 as startTime , 21 as endTime
              UNION SELECT 21 as startTime , 22 as endTime
              UNION SELECT 22 as startTime , 23 as endTime
              UNION SELECT 23 as startTime , 24 as endTime
        ),
             AA AS (
                 select  A.year,a.month,A.day,A.hour,
                         sum(CASE WHEN B.param_type = 'comprehensiveEnergy' THEN A.total_value ELSE 0 END) AS  comprehensiveEnergy,
                         sum(CASE WHEN B.param_type = 'electricityConsumption' THEN A.total_value ELSE 0 END ) AS   electricityConsumption,
                         sum(CASE WHEN B.param_type = 'waterConsumption' THEN A.total_value ELSE 0 END ) AS   waterConsumption,
                         sum(CASE WHEN B.param_type = 'gasConsumption' THEN A.total_value ELSE 0 END  ) AS  gasConsumption
                 from ems_used_data A
                          join ems_param_info B ON A.param_name = B.param_name and B.param_type in ( 'comprehensiveEnergy', 'electricityConsumption', 'waterConsumption', 'gasConsumption' )
                          JOIN ems_location_device_param_relation C ON C.param_name = B.param_name
                          JOIN ems_location_tree D  ON D.gid = C.location_gid and D.gid not in (select parent_gid from  ems_location_tree)
                          left join ems_location_tree H ON D.level1_location_gid = H.gid
              where (A.year = #{year})
                      AND (A.month = #{month})
                      AND (A.day = #{day})
        and D.location_type='line'
                        <if test='locationName != null and locationName != ""'>
                            and H.location_name = #{locationName}
                        </if>
                 GROUP BY  A.year,a.month,A.day,A.hour
             ),
             BB AS (
                 select  A.year,a.month,A.day,A.hour,
                         sum( CASE WHEN B.param_type = 'comprehensiveEnergy' THEN A.total_value ELSE 0 END ) AS   comprehensiveEnergy,
                         sum( CASE WHEN B.param_type = 'electricityConsumption' THEN A.total_value ELSE 0 END) AS    electricityConsumption,
                         sum( CASE WHEN B.param_type = 'waterConsumption' THEN A.total_value ELSE 0 END ) AS   waterConsumption,
                         sum( CASE WHEN B.param_type = 'gasConsumption' THEN A.total_value ELSE 0 END ) AS   gasConsumption

                 from ems_used_data A
                          join ems_param_info B ON A.param_name = B.param_name and B.param_type in ( 'comprehensiveEnergy', 'electricityConsumption', 'waterConsumption', 'gasConsumption' )
                          JOIN ems_location_device_param_relation C ON C.param_name = B.param_name
                          JOIN ems_location_tree D  ON D.gid = C.location_gid and D.gid not in (select parent_gid from  ems_location_tree)
                          left join ems_location_tree H ON D.level1_location_gid = H.gid
              where (A.year = #{chainYear})
                      AND (A.month = #{chainMonth})
                      AND (A.day = #{chainDay})
        and D.location_type='line'
                        <if test='locationName != null and locationName != ""'>
                            and H.location_name = #{locationName}
                        </if>
                 GROUP BY  A.year,a.month,A.day,A.hour
             )

        select
            TM.startTime
             , sum(case when AA.comprehensiveEnergy is null then  0  else AA.comprehensiveEnergy end)   comprehensiveEnergy
             , sum(case when BB.comprehensiveEnergy is null then  0  else BB.comprehensiveEnergy end)   chainComprehensiveEnergy

             , sum(case when AA.electricityConsumption is null then  0  else AA.electricityConsumption end)   electricityConsumption
             , sum(case when BB.electricityConsumption is null then  0  else BB.electricityConsumption end)   chainElectricityConsumption

             , sum(case when AA.waterConsumption is null then  0  else AA.waterConsumption end)   waterConsumption
             , sum(case when BB.waterConsumption is null then  0  else BB.waterConsumption end)   chainWaterConsumption

             , sum(case when AA.gasConsumption is null then  0  else AA.gasConsumption end)   gasConsumption
             , sum(case when BB.gasConsumption is null then  0  else BB.gasConsumption end)   chainGasConsumption

        from TM left JOIN  AA on AA.hour = TM.startTime
                left JOIN BB ON BB.hour = TM.startTime AND BB.hour = AA.hour

        GROUP BY TM.startTime
        ORDER BY TM.startTime
    </select>
    <resultMap id="EnergyBalanceDTO" type="cec.jiutian.ems.query.dto.EnergyBalanceDTO">
        <result column="source" jdbcType="VARCHAR" property="source"/>
        <result column="target" jdbcType="DECIMAL" property="target"/>
        <result column="value" jdbcType="DECIMAL" property="value"/>
    </resultMap>
    <select id="getEnergyBalance" resultType="cec.jiutian.ems.query.dto.EnergyBalanceDTO">

/*        三级和二级的名称
*/        SELECT G
                   .location_name AS SOURCE,
               ( CASE WHEN H.location_name IS NOT NULL THEN H.location_name ELSE d.location_name END ) target,
               SUM ( CASE WHEN B.param_type = #{energyType} THEN A.total_value ELSE 0 END ) AS
VALUE

        FROM
            ems_used_data
            A JOIN ems_param_info B ON A.param_name = B.param_name
            JOIN ems_location_device_param_relation C ON C.param_name = B.param_name
            JOIN ems_location_tree D ON D.gid = C.location_gid
            LEFT JOIN ems_location_tree H ON D.level3_location_gid = H.gid
            JOIN ems_location_tree G ON D.level2_location_gid = G.gid
        WHERE
            B.param_type IN ( #{energyType} )
            <if test='locationType != null and locationType != ""'>
                and D.location_type = #{locationType}
            </if>
          AND D.gid NOT IN ( SELECT parent_gid FROM ems_location_tree )
        GROUP BY
            H.location_name,
            G.location_name,
            D.location_name

        UNION

/*        -- 二级和一级的名称
*/        SELECT G
                   .location_name AS SOURCE,
                 H.location_name target,
               SUM ( CASE WHEN B.param_type = #{energyType} THEN A.total_value ELSE 0 END ) AS
VALUE

        FROM
            ems_used_data
            A JOIN ems_param_info B ON A.param_name = B.param_name
            JOIN ems_location_device_param_relation C ON C.param_name = B.param_name
            JOIN ems_location_tree D ON D.gid = C.location_gid
            JOIN ems_location_tree H ON D.level2_location_gid = H.gid
            JOIN ems_location_tree G ON D.level1_location_gid = G.gid
        WHERE
            B.param_type IN ( #{energyType} )
            <if test='locationType != null and locationType != ""'>
                and D.location_type = #{locationType}
            </if>
          AND D.gid NOT IN ( SELECT parent_gid FROM ems_location_tree )
        GROUP BY
            H.location_name,
            G.location_name

/*            -- 一级和 总和
*/        UNION
        SELECT
            '全厂总用能' SOURCE,
            G.location_name AS target,
            SUM ( CASE WHEN B.param_type = #{energyType} THEN A.total_value ELSE 0 END ) AS
VALUE

        FROM
            ems_used_data
            A JOIN ems_param_info B ON A.param_name = B.param_name
            JOIN ems_location_device_param_relation C ON C.param_name = B.param_name
            JOIN ems_location_tree D ON D.gid = C.location_gid
            JOIN ems_location_tree G ON D.level1_location_gid = G.gid
        WHERE
            B.param_type IN ( #{energyType} )
            <if test='locationType != null and locationType != ""'>
                and D.location_type = #{locationType}
            </if>
          AND D.gid NOT IN ( SELECT parent_gid FROM ems_location_tree )
        GROUP BY
            G.location_name
    </select>
</mapper>
